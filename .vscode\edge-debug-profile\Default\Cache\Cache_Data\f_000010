import { createHotContext as __vite__createHotContext } from "/@vite/client";import.meta.hot = __vite__createHotContext("/src/components/ClientList.tsx");if (!window.$RefreshReg$) throw new Error("React refresh preamble was not loaded. Something is wrong.");
const prevRefreshReg = window.$RefreshReg$;
const prevRefreshSig = window.$RefreshSig$;
window.$RefreshReg$ = RefreshRuntime.getRefreshReg("C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientList.tsx");
window.$RefreshSig$ = RefreshRuntime.createSignatureFunctionForTransform;

import * as RefreshRuntime from "/@react-refresh";

import __vite__cjsImport1_react_jsxDevRuntime from "/node_modules/.vite/deps/react_jsx-dev-runtime.js?v=8392bea5"; const _jsxDEV = __vite__cjsImport1_react_jsxDevRuntime["jsxDEV"];
import { Card, CardHeader, CardTitle, CardContent } from "/src/components/ui/card.tsx";
import { Badge } from "/src/components/ui/badge.tsx";
import { Users, Mail, Phone, MapPin, Calendar, Star } from "/node_modules/.vite/deps/lucide-react.js?v=8392bea5";
const ClientList = ({ clients })=>{
    const getScoreColor = (score)=>{
        if (score >= 80) return 'bg-green-500';
        if (score >= 60) return 'bg-yellow-500';
        if (score >= 40) return 'bg-orange-500';
        return 'bg-red-500';
    };
    const getScoreLabel = (score)=>{
        if (score >= 80) return 'Excellent';
        if (score >= 60) return 'Bon';
        if (score >= 40) return 'Moyen';
        return 'Faible';
    };
    if (clients.length === 0) {
        return /*#__PURE__*/ _jsxDEV(Card, {
            className: "border-tunisietelecom-blue/20",
            children: /*#__PURE__*/ _jsxDEV(CardContent, {
                className: "p-8 text-center",
                children: [
                    /*#__PURE__*/ _jsxDEV(Users, {
                        size: 48,
                        className: "mx-auto text-gray-400 mb-4"
                    }, void 0, false, {
                        fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientList.tsx",
                        lineNumber: 41,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ _jsxDEV("h3", {
                        className: "text-lg font-semibold text-gray-600 mb-2",
                        children: "Aucun client enregistré"
                    }, void 0, false, {
                        fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientList.tsx",
                        lineNumber: 42,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ _jsxDEV("p", {
                        className: "text-gray-500",
                        children: "Commencez par ajouter votre premier client avec le formulaire ci-dessus."
                    }, void 0, false, {
                        fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientList.tsx",
                        lineNumber: 45,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientList.tsx",
                lineNumber: 40,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientList.tsx",
            lineNumber: 39,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ _jsxDEV(Card, {
        className: "border-tunisietelecom-blue/20 shadow-lg",
        children: [
            /*#__PURE__*/ _jsxDEV(CardHeader, {
                className: "bg-gradient-to-r from-tunisietelecom-blue to-tunisietelecom-darkblue text-white",
                children: /*#__PURE__*/ _jsxDEV(CardTitle, {
                    className: "flex items-center gap-2",
                    children: [
                        /*#__PURE__*/ _jsxDEV(Users, {
                            size: 20
                        }, void 0, false, {
                            fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientList.tsx",
                            lineNumber: 57,
                            columnNumber: 11
                        }, this),
                        "Liste des Clients (",
                        clients.length,
                        ")"
                    ]
                }, void 0, true, {
                    fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientList.tsx",
                    lineNumber: 56,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientList.tsx",
                lineNumber: 55,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ _jsxDEV(CardContent, {
                className: "p-6",
                children: /*#__PURE__*/ _jsxDEV("div", {
                    className: "space-y-4",
                    children: clients.map((client)=>/*#__PURE__*/ _jsxDEV("div", {
                            className: "border rounded-lg p-4 hover:shadow-md transition-shadow duration-200 bg-white",
                            children: /*#__PURE__*/ _jsxDEV("div", {
                                className: "flex flex-col md:flex-row md:items-center justify-between gap-4",
                                children: [
                                    /*#__PURE__*/ _jsxDEV("div", {
                                        className: "flex-1",
                                        children: [
                                            /*#__PURE__*/ _jsxDEV("div", {
                                                className: "flex items-center gap-3 mb-2",
                                                children: [
                                                    /*#__PURE__*/ _jsxDEV("h3", {
                                                        className: "text-lg font-semibold text-tunisietelecom-darkgray",
                                                        children: [
                                                            client.prenom,
                                                            " ",
                                                            client.nom
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientList.tsx",
                                                        lineNumber: 72,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ _jsxDEV(Badge, {
                                                        className: `${getScoreColor(client.score)} text-white`,
                                                        children: [
                                                            /*#__PURE__*/ _jsxDEV(Star, {
                                                                size: 12,
                                                                className: "mr-1"
                                                            }, void 0, false, {
                                                                fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientList.tsx",
                                                                lineNumber: 76,
                                                                columnNumber: 23
                                                            }, this),
                                                            client.score,
                                                            "/100 - ",
                                                            getScoreLabel(client.score)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientList.tsx",
                                                        lineNumber: 75,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientList.tsx",
                                                lineNumber: 71,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ _jsxDEV("div", {
                                                className: "grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-600",
                                                children: [
                                                    client.email && /*#__PURE__*/ _jsxDEV("div", {
                                                        className: "flex items-center gap-2",
                                                        children: [
                                                            /*#__PURE__*/ _jsxDEV(Mail, {
                                                                size: 14,
                                                                className: "text-tunisietelecom-blue"
                                                            }, void 0, false, {
                                                                fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientList.tsx",
                                                                lineNumber: 84,
                                                                columnNumber: 25
                                                            }, this),
                                                            /*#__PURE__*/ _jsxDEV("span", {
                                                                children: client.email
                                                            }, void 0, false, {
                                                                fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientList.tsx",
                                                                lineNumber: 85,
                                                                columnNumber: 25
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientList.tsx",
                                                        lineNumber: 83,
                                                        columnNumber: 23
                                                    }, this),
                                                    client.telephone && /*#__PURE__*/ _jsxDEV("div", {
                                                        className: "flex items-center gap-2",
                                                        children: [
                                                            /*#__PURE__*/ _jsxDEV(Phone, {
                                                                size: 14,
                                                                className: "text-tunisietelecom-blue"
                                                            }, void 0, false, {
                                                                fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientList.tsx",
                                                                lineNumber: 91,
                                                                columnNumber: 25
                                                            }, this),
                                                            /*#__PURE__*/ _jsxDEV("span", {
                                                                children: client.telephone
                                                            }, void 0, false, {
                                                                fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientList.tsx",
                                                                lineNumber: 92,
                                                                columnNumber: 25
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientList.tsx",
                                                        lineNumber: 90,
                                                        columnNumber: 23
                                                    }, this),
                                                    client.adresse && /*#__PURE__*/ _jsxDEV("div", {
                                                        className: "flex items-center gap-2",
                                                        children: [
                                                            /*#__PURE__*/ _jsxDEV(MapPin, {
                                                                size: 14,
                                                                className: "text-tunisietelecom-blue"
                                                            }, void 0, false, {
                                                                fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientList.tsx",
                                                                lineNumber: 98,
                                                                columnNumber: 25
                                                            }, this),
                                                            /*#__PURE__*/ _jsxDEV("span", {
                                                                children: client.adresse
                                                            }, void 0, false, {
                                                                fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientList.tsx",
                                                                lineNumber: 99,
                                                                columnNumber: 25
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientList.tsx",
                                                        lineNumber: 97,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ _jsxDEV("div", {
                                                        className: "flex items-center gap-2",
                                                        children: [
                                                            /*#__PURE__*/ _jsxDEV(Calendar, {
                                                                size: 14,
                                                                className: "text-tunisietelecom-blue"
                                                            }, void 0, false, {
                                                                fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientList.tsx",
                                                                lineNumber: 104,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ _jsxDEV("span", {
                                                                children: [
                                                                    "Ajouté le ",
                                                                    new Date(client.dateCreation).toLocaleDateString('fr-FR')
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientList.tsx",
                                                                lineNumber: 105,
                                                                columnNumber: 23
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientList.tsx",
                                                        lineNumber: 103,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientList.tsx",
                                                lineNumber: 81,
                                                columnNumber: 19
                                            }, this),
                                            client.notes && /*#__PURE__*/ _jsxDEV("div", {
                                                className: "mt-3 p-3 bg-gray-50 rounded-md",
                                                children: /*#__PURE__*/ _jsxDEV("p", {
                                                    className: "text-sm text-gray-700",
                                                    children: [
                                                        /*#__PURE__*/ _jsxDEV("strong", {
                                                            children: "Notes :"
                                                        }, void 0, false, {
                                                            fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientList.tsx",
                                                            lineNumber: 112,
                                                            columnNumber: 25
                                                        }, this),
                                                        " ",
                                                        client.notes
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientList.tsx",
                                                    lineNumber: 111,
                                                    columnNumber: 23
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientList.tsx",
                                                lineNumber: 110,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientList.tsx",
                                        lineNumber: 70,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ _jsxDEV("div", {
                                        className: "flex flex-col items-center",
                                        children: [
                                            /*#__PURE__*/ _jsxDEV("div", {
                                                className: `w-16 h-16 rounded-full ${getScoreColor(client.score)} flex items-center justify-center text-white font-bold text-lg shadow-lg`,
                                                children: client.score
                                            }, void 0, false, {
                                                fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientList.tsx",
                                                lineNumber: 119,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ _jsxDEV("span", {
                                                className: "text-xs text-gray-500 mt-1",
                                                children: "Score"
                                            }, void 0, false, {
                                                fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientList.tsx",
                                                lineNumber: 122,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientList.tsx",
                                        lineNumber: 118,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientList.tsx",
                                lineNumber: 69,
                                columnNumber: 15
                            }, this)
                        }, client.id, false, {
                            fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientList.tsx",
                            lineNumber: 65,
                            columnNumber: 13
                        }, this))
                }, void 0, false, {
                    fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientList.tsx",
                    lineNumber: 63,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientList.tsx",
                lineNumber: 62,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientList.tsx",
        lineNumber: 54,
        columnNumber: 5
    }, this);
};
_c = ClientList;
export default ClientList;
var _c;
$RefreshReg$(_c, "ClientList");


window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;

RefreshRuntime.__hmr_import(import.meta.url).then((currentExports) => {
  RefreshRuntime.registerExportsForReactRefresh("C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientList.tsx", currentExports);
  import.meta.hot.accept((nextExports) => {
    if (!nextExports) return;
    const invalidateMessage = RefreshRuntime.validateRefreshBoundaryAndEnqueueUpdate("C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientList.tsx", currentExports, nextExports);
    if (invalidateMessage) import.meta.hot.invalidate(invalidateMessage);
  });
});

//# sourceMappingURL=data:application/json;base64,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