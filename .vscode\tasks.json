{"version": "2.0.0", "tasks": [{"label": "start-dev-server", "type": "shell", "command": "cmd", "args": ["/c", "cd /d \"${workspaceFolder}\\tunisie-telecom-scores-hub-main\" && npm run dev"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "isBackground": true, "problemMatcher": {"owner": "vite", "pattern": {"regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error):\\s+(.*)$", "file": 1, "line": 2, "column": 3, "severity": 4, "message": 5}, "background": {"activeOnStart": true, "beginsPattern": ".*Local:.*", "endsPattern": ".*ready.*"}}}, {"label": "stop-dev-server", "type": "shell", "command": "taskkill", "args": ["/F", "/IM", "node.exe"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "start-vite-simple", "type": "shell", "command": "npx", "args": ["vite", "--host", "0.0.0.0", "--port", "5173"], "options": {"cwd": "${workspaceFolder}/tunisie-telecom-scores-hub-main"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "isBackground": true}]}