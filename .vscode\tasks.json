{"version": "2.0.0", "tasks": [{"label": "start-dev-server-simple", "type": "shell", "command": "powershell", "args": ["-Command", "cd '${workspaceFolder}/tunisie-telecom-scores-hub-main'; npm run dev"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "isBackground": true, "problemMatcher": {"background": {"activeOnStart": true, "beginsPattern": ".*Local:.*", "endsPattern": ".*ready.*"}}}, {"label": "stop-dev-server", "type": "shell", "command": "taskkill", "args": ["/F", "/IM", "node.exe"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "start-vite-simple", "type": "shell", "command": "npx", "args": ["vite", "--host", "0.0.0.0", "--port", "5173"], "options": {"cwd": "${workspaceFolder}/tunisie-telecom-scores-hub-main"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "isBackground": true}]}