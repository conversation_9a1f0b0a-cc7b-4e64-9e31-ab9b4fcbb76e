import { createHotContext as __vite__createHotContext } from "/@vite/client";import.meta.hot = __vite__createHotContext("/src/components/AuthModal.tsx");if (!window.$RefreshReg$) throw new Error("React refresh preamble was not loaded. Something is wrong.");
const prevRefreshReg = window.$RefreshReg$;
const prevRefreshSig = window.$RefreshSig$;
window.$RefreshReg$ = RefreshRuntime.getRefreshReg("C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/AuthModal.tsx");
window.$RefreshSig$ = RefreshRuntime.createSignatureFunctionForTransform;

import * as RefreshRuntime from "/@react-refresh";

import __vite__cjsImport1_react_jsxDevRuntime from "/node_modules/.vite/deps/react_jsx-dev-runtime.js?v=8392bea5"; const _jsxDEV = __vite__cjsImport1_react_jsxDevRuntime["jsxDEV"];
var _s = $RefreshSig$();
import __vite__cjsImport2_react from "/node_modules/.vite/deps/react.js?v=8392bea5"; const useState = __vite__cjsImport2_react["useState"];
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "/src/components/ui/dialog.tsx";
import { Button } from "/src/components/ui/button.tsx";
import { Input } from "/src/components/ui/input.tsx";
import { Label } from "/src/components/ui/label.tsx";
import { Card, CardHeader, CardTitle, CardContent } from "/src/components/ui/card.tsx";
import { UserPlus, LogIn } from "/node_modules/.vite/deps/lucide-react.js?v=8392bea5";
const AuthModal = ({ isOpen, onClose, onAuthenticate })=>{
    _s();
    const [isLogin, setIsLogin] = useState(true);
    const [formData, setFormData] = useState({
        name: '',
        email: '',
        password: '',
        confirmPassword: ''
    });
    const handleSubmit = (e)=>{
        e.preventDefault();
        if (!isLogin && formData.password !== formData.confirmPassword) {
            alert('Les mots de passe ne correspondent pas');
            return;
        }
        if (!formData.email || !formData.password) {
            alert('Veuillez remplir tous les champs requis');
            return;
        }
        if (!isLogin && !formData.name) {
            alert('Veuillez entrer votre nom');
            return;
        }
        // Simuler l'authentification
        onAuthenticate({
            name: formData.name || formData.email.split('@')[0],
            email: formData.email
        });
        onClose();
        setFormData({
            name: '',
            email: '',
            password: '',
            confirmPassword: ''
        });
    };
    const toggleAuthMode = ()=>{
        setIsLogin(!isLogin);
        setFormData({
            name: '',
            email: '',
            password: '',
            confirmPassword: ''
        });
    };
    return /*#__PURE__*/ _jsxDEV(Dialog, {
        open: isOpen,
        onOpenChange: onClose,
        children: /*#__PURE__*/ _jsxDEV(DialogContent, {
            className: "sm:max-w-md",
            children: [
                /*#__PURE__*/ _jsxDEV(DialogHeader, {
                    children: /*#__PURE__*/ _jsxDEV(DialogTitle, {
                        className: "text-2xl font-bold text-center text-tunisietelecom-darkgray",
                        children: isLogin ? 'Connexion' : 'Inscription'
                    }, void 0, false, {
                        fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/AuthModal.tsx",
                        lineNumber: 62,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/AuthModal.tsx",
                    lineNumber: 61,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ _jsxDEV(Card, {
                    className: "border-0 shadow-none",
                    children: [
                        /*#__PURE__*/ _jsxDEV(CardHeader, {
                            className: "pb-4",
                            children: /*#__PURE__*/ _jsxDEV(CardTitle, {
                                className: "text-center text-tunisietelecom-blue flex items-center justify-center gap-2",
                                children: [
                                    isLogin ? /*#__PURE__*/ _jsxDEV(LogIn, {
                                        size: 20
                                    }, void 0, false, {
                                        fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/AuthModal.tsx",
                                        lineNumber: 70,
                                        columnNumber: 26
                                    }, this) : /*#__PURE__*/ _jsxDEV(UserPlus, {
                                        size: 20
                                    }, void 0, false, {
                                        fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/AuthModal.tsx",
                                        lineNumber: 70,
                                        columnNumber: 48
                                    }, this),
                                    isLogin ? 'Se connecter' : 'Créer un compte'
                                ]
                            }, void 0, true, {
                                fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/AuthModal.tsx",
                                lineNumber: 69,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/AuthModal.tsx",
                            lineNumber: 68,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ _jsxDEV(CardContent, {
                            children: /*#__PURE__*/ _jsxDEV("form", {
                                onSubmit: handleSubmit,
                                className: "space-y-4",
                                children: [
                                    !isLogin && /*#__PURE__*/ _jsxDEV("div", {
                                        className: "space-y-2",
                                        children: [
                                            /*#__PURE__*/ _jsxDEV(Label, {
                                                htmlFor: "name",
                                                children: "Nom complet"
                                            }, void 0, false, {
                                                fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/AuthModal.tsx",
                                                lineNumber: 79,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ _jsxDEV(Input, {
                                                id: "name",
                                                type: "text",
                                                placeholder: "Votre nom complet",
                                                value: formData.name,
                                                onChange: (e)=>setFormData({
                                                        ...formData,
                                                        name: e.target.value
                                                    }),
                                                className: "border-gray-300 focus:border-tunisietelecom-blue"
                                            }, void 0, false, {
                                                fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/AuthModal.tsx",
                                                lineNumber: 80,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/AuthModal.tsx",
                                        lineNumber: 78,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ _jsxDEV("div", {
                                        className: "space-y-2",
                                        children: [
                                            /*#__PURE__*/ _jsxDEV(Label, {
                                                htmlFor: "email",
                                                children: "Email"
                                            }, void 0, false, {
                                                fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/AuthModal.tsx",
                                                lineNumber: 92,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ _jsxDEV(Input, {
                                                id: "email",
                                                type: "email",
                                                placeholder: "<EMAIL>",
                                                value: formData.email,
                                                onChange: (e)=>setFormData({
                                                        ...formData,
                                                        email: e.target.value
                                                    }),
                                                className: "border-gray-300 focus:border-tunisietelecom-blue",
                                                required: true
                                            }, void 0, false, {
                                                fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/AuthModal.tsx",
                                                lineNumber: 93,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/AuthModal.tsx",
                                        lineNumber: 91,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ _jsxDEV("div", {
                                        className: "space-y-2",
                                        children: [
                                            /*#__PURE__*/ _jsxDEV(Label, {
                                                htmlFor: "password",
                                                children: "Mot de passe"
                                            }, void 0, false, {
                                                fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/AuthModal.tsx",
                                                lineNumber: 105,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ _jsxDEV(Input, {
                                                id: "password",
                                                type: "password",
                                                placeholder: "Votre mot de passe",
                                                value: formData.password,
                                                onChange: (e)=>setFormData({
                                                        ...formData,
                                                        password: e.target.value
                                                    }),
                                                className: "border-gray-300 focus:border-tunisietelecom-blue",
                                                required: true
                                            }, void 0, false, {
                                                fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/AuthModal.tsx",
                                                lineNumber: 106,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/AuthModal.tsx",
                                        lineNumber: 104,
                                        columnNumber: 15
                                    }, this),
                                    !isLogin && /*#__PURE__*/ _jsxDEV("div", {
                                        className: "space-y-2",
                                        children: [
                                            /*#__PURE__*/ _jsxDEV(Label, {
                                                htmlFor: "confirmPassword",
                                                children: "Confirmer le mot de passe"
                                            }, void 0, false, {
                                                fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/AuthModal.tsx",
                                                lineNumber: 119,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ _jsxDEV(Input, {
                                                id: "confirmPassword",
                                                type: "password",
                                                placeholder: "Confirmez votre mot de passe",
                                                value: formData.confirmPassword,
                                                onChange: (e)=>setFormData({
                                                        ...formData,
                                                        confirmPassword: e.target.value
                                                    }),
                                                className: "border-gray-300 focus:border-tunisietelecom-blue"
                                            }, void 0, false, {
                                                fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/AuthModal.tsx",
                                                lineNumber: 120,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/AuthModal.tsx",
                                        lineNumber: 118,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ _jsxDEV(Button, {
                                        type: "submit",
                                        className: "w-full bg-tunisietelecom-blue hover:bg-tunisietelecom-darkblue text-white py-3",
                                        children: isLogin ? 'Se connecter' : 'Créer le compte'
                                    }, void 0, false, {
                                        fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/AuthModal.tsx",
                                        lineNumber: 131,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ _jsxDEV("div", {
                                        className: "text-center",
                                        children: /*#__PURE__*/ _jsxDEV("button", {
                                            type: "button",
                                            onClick: toggleAuthMode,
                                            className: "text-tunisietelecom-blue hover:underline text-sm",
                                            children: isLogin ? "Pas de compte ? Créer un compte" : "Déjà un compte ? Se connecter"
                                        }, void 0, false, {
                                            fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/AuthModal.tsx",
                                            lineNumber: 139,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/AuthModal.tsx",
                                        lineNumber: 138,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/AuthModal.tsx",
                                lineNumber: 76,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/AuthModal.tsx",
                            lineNumber: 75,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/AuthModal.tsx",
                    lineNumber: 67,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/AuthModal.tsx",
            lineNumber: 60,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/AuthModal.tsx",
        lineNumber: 59,
        columnNumber: 5
    }, this);
};
_s(AuthModal, "sfxYXkdpckxABPji6Vh/otFAVPs=");
_c = AuthModal;
export default AuthModal;
var _c;
$RefreshReg$(_c, "AuthModal");


window.$RefreshReg$ = prevRefreshReg;
window.$RefreshSig$ = prevRefreshSig;

RefreshRuntime.__hmr_import(import.meta.url).then((currentExports) => {
  RefreshRuntime.registerExportsForReactRefresh("C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/AuthModal.tsx", currentExports);
  import.meta.hot.accept((nextExports) => {
    if (!nextExports) return;
    const invalidateMessage = RefreshRuntime.validateRefreshBoundaryAndEnqueueUpdate("C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/AuthModal.tsx", currentExports, nextExports);
    if (invalidateMessage) import.meta.hot.invalidate(invalidateMessage);
  });
});

//# sourceMappingURL=data:application/json;base64,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