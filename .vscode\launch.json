{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "type": "msedge",
            "request": "launch",
            "name": "Launch Edge with Auth (Recommended)",
            "url": "http://localhost:5173",
            "webRoot": "${workspaceFolder}/tunisie-telecom-scores-hub-main",
            "userDataDir": "${workspaceFolder}/.vscode/edge-debug-profile",
            "runtimeArgs": [
                "--disable-web-security",
                "--disable-features=VizDisplayCompositor",
                "--disable-site-isolation-trials",
                "--auth-server-whitelist=localhost:5173",
                "--auth-negotiate-delegate-whitelist=localhost:5173",
                "--disable-background-timer-throttling",
                "--disable-backgrounding-occluded-windows",
                "--disable-renderer-backgrounding"
            ],
            "env": {
                "LOGIN": "admin",
                "PASSWORD": "admin"
            },
            "preLaunchTask": "start-dev-server",
            "sourceMapPathOverrides": {
                "webpack:///./src/*": "${webRoot}/src/*"
            }
        },
        {
            "type": "chrome",
            "request": "launch",
            "name": "Launch Chrome with Auth",
            "url": "http://localhost:5173",
            "webRoot": "${workspaceFolder}/tunisie-telecom-scores-hub-main",
            "userDataDir": "${workspaceFolder}/.vscode/chrome-debug-profile",
            "runtimeArgs": [
                "--disable-web-security",
                "--disable-features=VizDisplayCompositor",
                "--disable-site-isolation-trials",
                "--auth-server-whitelist=localhost:5173",
                "--auth-negotiate-delegate-whitelist=localhost:5173",
                "--disable-background-timer-throttling",
                "--disable-backgrounding-occluded-windows",
                "--disable-renderer-backgrounding"
            ],
            "env": {
                "LOGIN": "admin",
                "PASSWORD": "admin"
            },
            "preLaunchTask": "start-dev-server",
            "sourceMapPathOverrides": {
                "webpack:///./src/*": "${webRoot}/src/*"
            }
        },
        {
            "type": "msedge",
            "request": "launch",
            "name": "Launch Edge - Simple (No Auth)",
            "url": "http://localhost:5173",
            "webRoot": "${workspaceFolder}/tunisie-telecom-scores-hub-main",
            "preLaunchTask": "start-vite-simple"
        },
        {
            "type": "msedge",
            "request": "launch",
            "name": "Launch Edge - Manual Start",
            "url": "http://localhost:5173",
            "webRoot": "${workspaceFolder}/tunisie-telecom-scores-hub-main"
        }
    ]
}