!function(){"use strict";var e={d:function(t,i){for(var r in i)e.o(i,r)&&!e.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:i[r]})},o:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)}};function t(e){return!e||!e.trim()}e.d({},{d2:function(){return z}});let i=function(e){return e.Opal="Opal",e.Extension="Extension",e.SafariExtension="SafariExtension",e.ChromeExtension="ChromeExtension",e.SafariIOSExtension="SafariIOSExtension",e.Edge="Edge",e.EdgeMobile="EdgeMobile",e.Sapphire="Sapphire",e.RBC="RBC",e.EdgeAndroid="EdgeAndroid",e.EdgeiOS="EdgeiOS",e.EdgeDiscover="EdgeDiscover",e}({});i.EdgeMobile,i.EdgeAndroid,i.EdgeiOS;i.Edge;let r=function(e){return e.ORDERED="Ordered",e.SHIPPED="Shipped",e.DELIVERED="Delivered",e.RETURNED="Returned",e.CANCELED="Canceled",e.ERROR="Error",e}({});new Set(["amazon.com","amazon.ca","amazon.co.uk","amazon.co.jp","alibaba.com"]),new Map(Object.entries({"etsy.com":"receipt_id","target.com":"referenceId","tmall.com":"bizOrderId"}));let o=function(e){return e.DELIVERED_REGEX="(delivered|配達しました|已完成|交易成功)",e.SHIPPED_REGEX="(arriv|(^((?!not).)*ship)|到着|正在出库)",e.RETURNED_REGEX="(return|戻る|返金)",e.CANCELED_REGEX="(cancel|closed|キャンセル|已取消|交易关闭)",e}({});const n="COMPONENT_TO_FOCUS_IN_SHORELINE";Object.keys({"bestbuy.com":{policyDays:15,supportPageUrl:"https://www.bestbuy.com/site/help-topics/price-match-guarantee/pcmcat290300050002.c?id=pcmcat290300050002"},"costco.com":{policyDays:30,supportPageUrl:"https://customerservice.costco.com/app/answers/detail/a_id/628/~/price-adjustment---costco.com-orders",useCartAtPathname:"/checkoutcartdisplayview"},"kohls.com":{policyDays:14,supportPageUrl:"https://cs.kohls.com/app/answers/detail/a_id/90/~/price-match-policy"},"target.com":{policyDays:14,supportPageUrl:"https://help.target.com/help/subcategoryarticle?childcat=Price+Match+Guarantee&parentcat=Policies+%26+Guidelines&searchQuery=search+help",useCartAtPathname:"/cart"},"dickssportinggoods.com":{policyDays:14,supportPageUrl:"https://www.dickssportinggoods.com/s/price-match-policy",useCartAtPathname:"/orderitemdisplay"},"jcpenney.com":{policyDays:14,supportPageUrl:"https://www.jcpenney.com/m/customer-service/our-lowest-price-guarantee"},"macys.com":{policyDays:10,supportPageUrl:"https://customerservice-macys.com/articles/how-can-i-get-a-price-adjustment",useCartAtPathname:"/my-bag",hasCsrError:!0},"ashleyfurniture.com":{policyDays:30,supportPageUrl:"https://www.ashleyfurniture.com/price-match/"},"gap.com":{policyDays:14,supportPageUrl:"https://www.gap.com/customerService/info.do?cid=1192378"},"staples.com":{policyDays:14,supportPageUrl:"https://www.staples.com/sbd/cre/marketing/pmg/index.html"}});let s=null;const a="test-shopping-localstorage";function p(e){let t=null;return function(){try{if(null!==s)return s;"undefined"!=typeof window&&window?.localStorage&&(window.localStorage.setItem(a,a),window.localStorage.getItem(a),window.localStorage.removeItem(a),s=!0)}catch(e){s=!1}return s}()&&(t=window.localStorage.getItem(e)),t}class c{static Sleep(e){return new Promise((t=>setTimeout(t,e)))}static StringifyMap(e,t){return t instanceof Map?{dataType:"Map",value:Array.from(t.entries())}:t}static parseBool(e){return"true"===e||!0===e}static ParseMap(e,t){return"object"==typeof t&&null!==t&&"Map"===t.dataType?new Map(t.value):t}static async WaitForCondition(e,t,i){const r=(new Date).getTime();for(;!await e()&&r+t>(new Date).getTime();)await c.Sleep(i??100);return await e()}static async WaitUntilCondition(e,t){const i=(new Date).getTime();for(;i+t>(new Date).getTime();){if(await e())return!0;await c.Sleep(100)}return!1}static async WaitForSyncCondition(e,t){const i=(new Date).getTime();for(;i+t>(new Date).getTime();){if(e())return!0;await c.Sleep(100)}return!1}static IsValidDataField(e){return null!=e&&e.length>0&&"null"!==e}static IsPageMatch(e,t,i,r){let o=!1;if(c.IsValidDataField(e)&&(o=c.IsOnPage(e,i)),c.IsValidDataField(t))try{!r&&location.href?.toLocaleLowerCase()?.includes(i.toLocaleLowerCase())&&"chrome-untrusted://shopping/"!==location.href&&(r=location.href?.toLocaleLowerCase()),o=c.IsPageRegexMatch(t,r??i)}catch{}return o}static IsPageRegexMatch(e,t){return!!c.IsValidDataField(e)&&new RegExp(e).test(t.toLowerCase())}static IsOnPage(e,t){if(c.IsValidDataField(e)&&t){const i=e.toLowerCase().replace(/\s+/g,"").split(","),r=t.toLowerCase();let o=!1;for(const e of i)if(r.indexOf(e)>=0){o=!0;break}return o}return!1}static ObserveUntil(e,t){const i=new MutationObserver((async()=>{e()&&(i.disconnect(),t())}));i.observe(document.body,{attributeFilter:["offsetWidth","offsetHeight"],childList:!0,subtree:!0})}static async MeasureExecutionTime(e,t){const i=performance.now();return await e(),performance.now()-i}static DeepAssign(e,t){return Object.keys(t).forEach((i=>{if("object"==typeof t[i])e[i]||Object.assign(e,{[i]:{}}),c.DeepAssign(e[i],t[i]);else{let r=t[i];"urlRegex"===i&&"string"==typeof r&&r.endsWith("/")&&(r=r.substring(0,r.length-1)),Object.assign(e,{[i]:r})}})),e}static scrollToModuleIfTargeted(e,t){p(n)===t&&setTimeout((()=>{e?.scrollIntoView({behavior:"smooth",block:"start"}),localStorage.removeItem(n)}),500)}}var g=c;function l(e){return l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},l(e)}function d(e,t,i){return(t=function(e){var t=function(e){if("object"!==l(e)||null===e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var i=t.call(e,"string");if("object"!==l(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===l(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}let h=function(e){return e.sanConfirmationMessage="msShoppingExp0",e.onlineSelectorExtraction="msShoppingExp1",e.couponRedesignExperiment="msShoppingExp2",e.PCDouble="msShoppingExp3",e.PCOutOfStock="msShoppingExp4",e.blossomCampaign="msShoppingExp5",e.newNotificationManagementUI="msShoppingExp6",e.manualCoupon="msShoppingExp9",e.startShoppingRelatedProducts="msShoppingExp11",e.singleScrollingShoreline="msShoppingExp12",e.dealczarBackend="msShoppingExp13",e.sanConsent="msShoppingExp14",e.pcClickoutReverse="msShoppingExp15",e.optionalCheckoutPageUrl="msShoppingExp16",e.domMutationEnabled="msShoppingExp18",e.pdpSelectorExtraction="msShoppingExp19",e.newPriceHistoryGraph="msShoppingExp20",e.closeOnClickAnywhere="msShoppingExp21",e.disableChatChips="msShoppingExp22",e.snoozeWithOptions="msShoppingExp23",e.calculatedCutOffPriceFiltering="msShoppingExp25",e.pcbAddressBar="msShoppingExp29",e.shoppingRClient="msShoppingExp30",e.checkoutSkuCashbackEstimation="msShoppingExp31",e.spbInstantAutoActivate="msShoppingExp33",e.productTracking="msShoppingExp34",e.spbAutoDismissTreatment="msShoppingExp35",e.suggestedCouponEnhancement="msShoppingExp36",e.pcboutofflownotification="msShoppingExp38",e.dynamicRanking="msShoppingExp39",e.trendingRecommendations="msShoppingExp40",e.groceryAnonymous="msShoppingExp41",e.spbAutoDismissControl="msShoppingExp42",e.buyingGuideMultiLine="msShoppingExp43",e.spbPdpWithActivated="msShoppingExp44",e.sanConsentStringUpdate="msShoppingExp45",e.spbAutoSnoozeTreatment="msShoppingExp48",e.similarOffers="msShoppingExp49",e.shorelineinboxcoupons="msShoppingExp52",e.spbPdpCashBack="msShoppingExp53",e.intersperseNativeAds="msShoppingExp54",e.sidepaneCashback="msShoppingExp55",e.spbAutoActivationOnSearch="msShoppingExp56",e.startShoppingMomentInTimeModule="msShoppingExp58",e.triviaIcon="msShoppingExp59",e.groceryOffersSearch="msShoppingExp60",e.spbAutoShowFlagTreatmen="msShoppingExp61",e.rewardNotification="msShoppingExp62",e.discoverShopping="msShoppingExp63",e.cpsPDPcontrolFlag="msShoppingExp64",e.spbPDPShortDesign="msShoppingExp65",e.spbCheckoutAutoActivation="msShoppingExp66",e.domMutationExpansion="msShoppingExp67",e.temporalShoppingOnDiscover="msShoppingExp68",e.computeJourneyStage="msShoppingExp69",e.footerExperiments="msShoppingExp70",e.couponsConfidence="msShoppingExp71",e.ProductBestCouponConsent="msShoppingExp72",e.lowCouponExpectationExpansion="msShoppingExp73",e.spbExpiryMicroNotif="msShoppingExp74",e.spbShortNotification="msShoppingExp75",e.chatChipsOrder3="msShoppingExp76",e.spbHomePage="msShoppingExp77",e.enrollToRebateBonus="msShoppingExp78",e.instantAddToCart="msShoppingExp79",e.autoActivateInstantAtc="msShoppingExp80",e.buyingOptionsExperiment="msShoppingExp81",e.searchFeature="msShoppingExp99",e.sustainability="msShoppingExp100",e.startShoppingCategoryAds="msShoppingExp101",e.startShoppingNativeAds="msShoppingExp110",e.incentiveCampaign="msShoppingExp10000",e.pcEMI="msEdgeShoppingExp2000",e.affiliateFastActivationReturn="msEdgeShoppingFastAffiliateActivationReturn",e.bgaaService="msEdgeShoppingBackgroundAutoApply",e.bgaaUx="msEdgeShoppingBgaaUx",e.bgaaFlightingForChannel="msEdgeShoppingBackgroundAutoApplyFlightingForChannel",e.bannerShowCoupons="msEdgeShoppingBannerCoupons",e.categoryDeals="msEdgeShoppingPwiloNotificationsCategoryDeals",e.clarity="msEdgeShoppingClarityEnabled",e.clarityWithCookies="msEdgeShoppingClarityEnabledWithCookies",e.clientUrlNavigation="msEdgeShoppingClientUrlNavigation",e.coloredHeader="msShoppingColoredHeader",e.expressCheckout="msEdgeShoppingExpressCheckout",e.expressCheckoutFillDetails="msEdgeShoppingExpressCheckoutFillDetails",e.injectConfirmationScriptUponPurchaseCompletion="msEdgeShoppingAllowInjectConfirmationScriptNextNavigation",e.microNotification="msEdgeShoppingNotifications",e.rebatesSsoFlow="msEdgeShoppingRebatesV2Enroll",e.msWalletBNPL="msWalletBNPL",e.muidNotifications="msEdgeShoppingPwiloNotifications",e.pcBannerUXExperimentation1="msEdgeShoppingPCBannerUXExperimentation1",e.pcBannerUXExperimentation2="msEdgeShoppingPCBannerUXExperimentation2",e.pcBannerUXExperimentation3="msEdgeShoppingPCBannerUXExperimentation3",e.pcBannerUXExperimentation4="msEdgeShoppingPCBannerUXExperimentation4",e.pcBannerUXExperimentation5="msEdgeShoppingPCBannerUXExperimentation5",e.pcBannerUXExperimentation6="msEdgeShoppingPCBannerUXExperimentation6",e.priceDropNotification="msEdgeShoppingServerNotifications",e.productTrackingOmnibox="msEdgeShoppingProductTracking",e.pwiloOnOpenProcessing="msEdgeShoppingPwiloNotificationsOnOpenProcess",e.rewardFlyout="msEdgeShoppingRewards",e.showAmazonOtherSellers="msEdgeShoppingOtherSeller",e.showNudgeTipsOverlay="msShoppingExpNudge",e.showPaymentOptions="msZipPayVirtualCard",e.showPaymentOptionsOnProductPage="msEdgeShoppingProductFlyoutWallet",e.showProductHistory="msWebAssistQuery",e.showWalletFooter="msEnableWallet",e.signinRebates="msEdgeShoppingRebatesSignUp",e.snoozeAutoshow="msEdgeShoppingAutoShowMuteForFeature",e.socialCoupons="msEdgeShoppingExclusiveCoupons",e.socialCouponsAutoShow="msEdgeShoppingExclusiveCouponsAutoShow",e.rebatesOrganicNew="msEdgeShoppingRebatesAutoShowNewUser",e.paneOpenWithTab1="msEdgeShoppingMainTab1",e.paneOpenWithTab2="msEdgeShoppingMainTab2",e.paneOpenWithTab3="msEdgeShoppingMainTab3",e.axShopping="msEnableAXShoppingNavigation",e.shoppingOnDiscover="msEdgeShoppingOnDiscover",e.pcOfferLevel="msShoppingAutoShowPriceComparisonOfferLevel",e.discoverPdpTopPriority="msDiscoverPdpTopPriority",e.limitPdpNotificationsOnLowStages="msLimitPdpNotificationsOnLowStages",e.clientRequests="msEdgeShoppingGenericDealsService",e.shoppingCohorts="msEdgeShoppingCohorts",e.shoppingCohortsReclaimed="msEdgeShoppingCohortsReclaimed",e.shoppingCohortsVulnerable="msEdgeShoppingCohortsVulnerable",e.shoppingSettingsInPane="msEdgeShoppingSettingsInPane",e.shoppingSettingsInPaneNotificationBottomToggle="msEdgeShoppingSettingsInPaneNotificationBottomToggle",e.edgeShoppingRebatesForAADAccounts="msEdgeShoppingRebatesForAADAccounts",e.shoppingCjkProductUpsell="msEdgeShoppingCJKProductUpsell",e.pcAutoshowAvailable="msEdgeShoppingAutoShowPriceComparisonAvailable",e.travel1="msEdgeShoppingTravel1",e.travel2="msEdgeShoppingTravel2",e.travel3="msEdgeShoppingTravel3",e.travel4="msEdgeShoppingTravel4",e.travel5="msEdgeShoppingTravel5",e.travel6="msEdgeShoppingTravel6",e.settingsPaneNavigateFromFlyout="msEdgeShoppingSettingsInPaneNavigationFromFlyout",e.udaPriceComparison="msShoppingUapiExp41",e.autoApplyRewards="msEdgeShoppingAutoApplyRewards",e.cashbackActivationOmnibar="msEdgeShoppingCashbackActivationFromOmnibar",e.omniboxAnimationServerSide="msEdgeShoppingOmniboxAnimationAOC",e.omniboxBadgeControlServerSide="msEdgeShoppingControlBadgeTextFromServer",e.omniboxBackplate="msEdgeShoppingOmniboxBackplate",e.DisableRegexEnhancment="edgeServerUX.shopping.disableRegexEnhancment",e.CouponsPersonalization="edgeServerUX.shopping.couponsPersonalization",e.PhPersonalization="edgeServerUX.shopping.phPersonalization",e.CashbackPersonalization="edgeServerUX.shopping.cashbackPersonalization",e.cashBackDelayedEnrollmentEdgeFlow="edgeServerUX.shopping.cashBackDelayedEnrollmentEdgeFlow",e.cashbackDismissTimeout="edgeServerUX.shopping.msEdgeShoppingCashbackDismissTimeout2s",e.CouponMessageFiltering="edgeServerUX.shopping.couponMessageFiltering",e.inProgressTitleV1="edgeServerUX.shopping.inProgressTitleV1",e.inProgressTitleV2="edgeServerUX.shopping.inProgressTitleV2",e.ServerSideSearchInPane="edgeServerUX.shopping.searchInPane",e.campaign="edgeServerUX.shopping.campaign",e.campaignAwareness="edgeServerUX.shopping.campaignAwareness",e.cbAnimationVar1="edgeServerUX.shopping.cbAnimationVar1",e.cbAnimationVar4="edgeServerUX.shopping.cbAnimationVar4",e.compositeNotificationsPriceHistoryPlusCoupons="edgeServerUX.shopping.compositeNotificationsPriceHistoryPlusCoupons",e.compositeNotificationsPriceHistoryPlusCouponsV2="edgeServerUX.shopping.compositeNotificationsPriceHistoryPlusCouponsV2",e.cashbackPdpPlusCouponsNotification="edgeServerUX.shopping.compositeNotificationsCashbackPdpPlusCoupons",e.cashbackPdpPlusCouponsNotificationV2="edgeServerUX.shopping.compositeNotificationsV2UX",e.cashbackPdpPlusPriceDropNotification="edgeServerUX.shopping.compositeNotificationsPriceHistoryPlusCashback",e.cashbackPdpPlusPriceDropNotificationV2="edgeServerUX.shopping.compositeNotificationsPriceHistoryPlusCashbackV2",e.lowerPricePdpPlusCashbackPlusCoupons="edgeServerUX.shopping.compositeNotificationsLowerPricePdpPlusCashbackPlusCoupons",e.lowerPricePdpPlusCashbackPlusCouponsV2="edgeServerUX.shopping.compositeNotificationsLowerPricePdpPlusCashbackPlusCouponsV2",e.ptConciseUi="edgeServerUX.shopping.ptConciseUi",e.BingL2BRQ="edgeServerUX.shopping.BingL2BRQ",e.SeeAllClickout="edgeServerUX.shopping.SeeAllClickout",e.cartExtractionFromBody="edgeServerUX.shopping.cartExtractionFromBody",e.checkoutPageTypeCheck="edgeServerUX.shopping.checkoutPageTypeCheck",e.PTRecentVwd="edgeServerUX.shopping.PTRecentVwd",e.spbAddToCart="edgeServerUX.shopping.addToCartVariation",e.couponRedesignCompressed="edgeServerUX.shopping.couponRedesignCompressed",e.couponRedesignExpanded="edgeServerUX.shopping.couponRedesignExpanded",e.lostUsersCashBack="edgeServerUX.shopping.lostUsersCashback",e.lostUsersPriceComparison="edgeServerUX.shopping.lostUsersPriceComparison",e.lostUsersPriceComparisonRegular="edgeServerUX.shopping.lostUsersPriceComparisonRegular",e.inboxSavings="edgeServerUX.shopping.inboxSavings",e.CouponsPipelineV2="shopping.couponsV2Pipeline",e.spbSearchAutoActivation="edgeServerUX.shopping.spbSearchAutoActivation",e.postPurchaseTracking="edgeServerUX.shopping.postPurchaseTracking",e.pdpStorewideCashbackEstimation="edgeServerUX.shopping.pdpStorewideCashbackEstimation",e.blockLowPerformanceNotifications="edgeServerUX.shopping.blockLowPerformanceNotifications",e.instantAddToCartExperiment="edgeServerUX.shopping.InstantATC",e.suppressAutoApply="edgeServerUX.shopping.suppressAutoApply",e.cbSplitModules="edgeServerUX.shopping.SeparateCashBackModules",e.inboxCouponsAAFailureUpsell="edgeServerUX.shopping.inboxCouponsAAFailureUpsell",e.inboxCouponsAASuccessUpsell="edgeServerUX.shopping.inboxCouponsAASuccessUpsell",e.mobileCashbackModule="edgeServerUX.shopping.mobileCashbackModule",e.mobileProductModules="edgeServerUX.shopping.mobileProductModules",e.ptCouponsCashbackNotification="edgeServerUX.shoppping.ptCouponsCashbackNotification",e.journeyStageTelemetry="edgeServerUX.shopping.journeyStageTelemetry",e.revampedPriceComparison="edgeServerUX.shopping.revampedPriceComparison",e.qtCartPageAutoActivation="edgeServerUX.shopping.qtAutoActivation",e.discoverChatChips="edgeServerUX.shopping.discoverChatChips",e.discoverNotification="edgeServerUX.shopping.discoverNotification",e.discoverNotification50="edgeServerUX.shopping.discoverNotification50",e.discoverNotification100="edgeServerUX.shopping.discoverNotification100",e.discoverNotification500="edgeServerUX.shopping.discoverNotification500",e.travelCheaperFlights="edgeServerUX.shopping.travelCheaperFlights",e.travelCheaperFlightsListView="edgeServerUX.shopping.travelCheaperFlightsListView",e.travelExecuteAutoOpenService="edgeServerUX.shopping.travelExecuteAutoOpenService",e.travelIsHotelExperimentActive="edgeServerUX.shopping.travelIsHotelExperimentActive",e.travelRenderCheaperFlightsNotification="edgeServerUX.shopping.travelRenderCheaperFlightsNotification",e.travelForceMarket="edgeServerUX.shopping.travelForceMarket",e.travelCheaperFlightsModule="edgeServerUX.shopping.travelCheaperFlightsModule",e.travelRenderCheaperHotelsNotification="edgeServerUX.shopping.travelRenderCheaperHotelsNotification",e.notificationRedesignFpc="edgeServerUX.shopping.notificationRedesignFpc",e.notificationRedesignHpc="edgeServerUX.shopping.notificationRedesignHpc",e.travelHpcPriceViewportTrigger="edgeServerUX.shopping.travelHpcPriceViewportTrigger",e.travelHUx2="edgeServerUX.shopping.travelHUx2",e.travelHUx3="edgeServerUX.shopping.travelHUx3",e.travelHUx4="edgeServerUX.shopping.travelHUx4",e.pageDetection="edgeServerUX.shopping.pageDetection",e.disableJourneyStageComputation="edgeServerUX.shopping.disableJourneyStageComputation",e.userInsights="edgeServerUX.shopping.userInsights",e.expertInsights="edgeServerUX.shopping.expertInsights",e.communityInsightsPH="edgeServerUX.shopping.communityInsightsPH",e.communityInsightsPC="edgeServerUX.shopping.communityInsightsPC",e.communityInsightsCB="edgeServerUX.shopping.communityInsightsCB",e.shoppingR="edgeServerUX.shopping.shoppingR",e.productSummaryCardEnabled="edgeServerUX.shopping.productSummaryCardTestExp99",e.productSummaryCardEnabledSticky="edgeServerUX.shopping.productSummaryCardStickyTestExp99",e.productSummaryInsights="edgeServerUX.shopping.productSummaryInsightsInShoppingPane",e.cbMerchantAbTesting="edgeServerUX.shopping.merchantAbTesting",e.cbMerchantAbTestingControl="edgeServerUX.shopping.merchantAbTestingCf",e.showSpbOffersOnShorelineOpen="edgeServerUX.shopping.showSpbOffersOnShorelineOpen",e.showSpbOffersOnShorelineOpenV2="edgeServerUX.shopping.showSpbOffersOnShorelineOpenV2",e.aaNoDontShowAgain="edgeServerUX.shopping.aaNoDontShowAgain",e.aaEarlyReturn="edgeServerUX.shopping.aaEarlyReturn",e.suppressLowCTR="edgeServerUX.shopping.suppressLowCTR",e.couponBackgroundDarkCode="edgeServerUX.shopping.couponBackgroundDarkCode",e.couponBackgroundLightCode="edgeServerUX.shopping.couponBackgroundLightCode",e.couponLightCode="edgeServerUX.shopping.couponLightCode",e.couponDarkCode="edgeServerUX.shopping.couponDarkCode",e.cashbackEUMarkets="edgeServerUX.shopping.cashbackEUMarkets",e.autoSuppressNegativeBGAA="edgeServerUX.shopping.autoSupperssNegativeBGAA",e.bgaaNoSnoozeTime="edgeServerUX.shopping.bgaaNoSnoozeTime",e.newHeader="edgeServerUX.shopping.newHeader",e.loggingThrottle="edgeServerUX.shopping.loggingThrottle",e.shopnotifoffline1="edgeServerUX.shopping.expSNData",e.onlineSNModel="edgeServerUX.shopping.onlineSNModel",e.sellerIconCovIncr="edgeServerUX.shopping.sellerIconCovIncr",e.pcSeeMore="edgeServerUX.shopping.pcSeeMore",e.priceCompareV2="edgeServerUX.shopping.priceCompareV2",e.pcNotificationCashbackV2="edgeServerUX.shopping.pcNotifCbV2",e.domMutationReverseFlight="domMutationReverseFlight",e.mockCashbackCurrentTime="edgeServerUX.shopping.mockCashbackCurrentTime",e.mockCurrentTimeForExpiration="edgeServerUX.shopping.mockCurrentTimeForExpiration",e.domainExpirableOrigins="edgeServerUX.shopping.domainExpirableOrigins",e.globalExpirableOrigins="edgeServerUX.shopping.globalExpirableOrigins",e.domainExpirationRate="edgeServerUX.shopping.domainExpirationRate",e.globalExpirationRate="edgeServerUX.shopping.globalExpirationRate",e.fragmentPageTypeFiltering="edgeServerUX.shopping.fragmentPageTypeFiltering",e.cashbackConsolidationExp="edgeServerUX.shopping.cashbackRefactor_v4",e.collectAllFragments="edgeServerUX.shopping.collectAllFragments",e.disablePageTypeCollectionForFragments="edgeServerUX.shopping.disablePageTypeCollectionForFragments",e.suppressedCoupons="edgeServerUX.shopping.suppressedCoupons",e.autoShowNotificationForAmazonWarehouseSellers="edgeServerUX.shopping.AutoShowNotificationAmazonWarehouseSeller",e.pcAutoshowExpanded="edgeServerUX.shopping.pcAutoshowExpanded",e.pcNotificationButtonText="edgeServerUX.shopping.pcNotificationButtonText",e.pcCloseSnooze="edgeServerUX.shopping.pcCloseSnooze",e.newCouponStringsT1="edgeServerUX.shopping.newCouponStringsT1",e.newCouponStringsT2="edgeServerUX.shopping.newCouponStringsT2",e.newCouponStringsT3="edgeServerUX.shopping.newCouponStringsT3",e.AARewardString="edgeServerUX.shopping.AARewardString",e.cashbackCloseSnooze="edgeServerUX.shopping.cashbackCloseSnooze",e.phCloseSnooze="edgeServerUX.shopping.phCloseSnooze",e.aaCloseSnooze="edgeServerUX.shopping.aaCloseSnooze",e.disableWebComponentCoupon="edgeServerUX.shopping.disableWebComponentCoupon",e.disableNotificationForSimilarSellers="edgeServerUX.shopping.DisableNotificationSimilarSeller",e.disableCashbackOrganicNotification="edgeServerUX.shopping.disableCashbackOrganicNotification",e.disableSPBNotification="edgeServerUX.shopping.disableSPBNotification",e.spbOffersSearch="edgeServerUX.shopping.authoShowSpbT",e.spbDismissTimeout="edgeServerUX.shopping.spbDismissTimeout",e.spbCloseSnooze="edgeServerUX.shopping.spbCloseSnooze",e.fixCartUpdate="edgeServerUX.shopping.fixCartUpdate",e.buyingOptions="edgeServerUX.shopping.buyingOptions",e.couponsSunset="edgeServerUX.shopping.couponsSunset",e.ptOutOfStock="edgeServerUX.shopping.ptOutOfStock",e.ptOutOfStockDev="edgeServerUX.shopping.ptOutOfStockDev",e.pcbStorewideUXDesignChange="edgeServerUX.shopping.pcbStorewideUXDesignChangev3",e.personalizePromotionNewUser="edgeServerUX.shopping.ppNewUser",e.reenableProductBestCoupon="edgeServerUX.shopping.reenableProductBestCoupon",e.reenableProductBestCouponSearchPage="edgeServerUX.shopping.reenableProductBestCouponSearchPage",e.pdpRerank="edgeServerUX.shopping.pdpRerank",e.ptNotifications="edgeServerUX.shopping.ptNotifications",e.aaWaitForBlockedCoupons="edgeServerUX.shopping.aaWaitForBlockedCoupons",e.onlyShowAANotificationWithEligibleCoupons="edgeServerUX.shopping.onlyShowAANotificationWithEligibleCoupons",e.delayForAAEligibleCouponsCheck="edgeServerUX.shopping.delayForAAEligibleCouponsCheck",e.autoOpenPaneScenariosActive="edgeServerUX.shopping.autoOpenPaneScenariosActive",e.autoOpenPaneDismissTime="edgeServerUX.shopping.autoOpenPaneDismissTime",e.enablePurchaseDetectionRewardsAPI="edgeServerUX.shopping.enablePurchaseDetectionRewardsAPI",e.enableCashbackCampaignEmail="edgeServerUX.shopping.enableCashbackCampaignEmail",e.sendNativePurchaseDetectionSignal="edgeServerUX.shopping.sendNativePurchaseDetectionSignal",e.disableAllFlights="edgeServerUX.shopping.disableAllFlights",e.triggerServiceForPdp="edgeServerUX.shopping.triggerServiceForPdp",e.triggerServiceForRetailer="edgeServerUX.shopping.triggerServiceForRetailer",e.fetchRewardOfferDuringAA="edgeServerUX.shopping.fetchRewardOfferDuringAA",e.disableConfirmationNotifications="edgeServerUX.shopping.disableConfirmationNotifications",e.aggregatorPDPUrlMap="edgeServerUX.shopping.aggregatorPDPUrlMap",e.disablefeed="edgeServerUX.shopping.disablefeed",e.spbCashbackModule="edgeServerUX.shopping.spbCashbackModule",e.enableLoadingSpinnerLabelsRotation="edgeServerUX.shopping.enableLoadingSpinnerLabelsRotation",e.loadingSpinnerRotationTimeInMS="edgeServerUX.shopping.loadingSpinnerRotationTimeInMS",e.disableZeroSuccessRateCouponShuffling="edgeServerUX.shopping.disableZeroSuccessRateCouponShuffling",e.enableAffiliateActivationStatusUpdate="edgeServerUX.shopping.enableAffiliateActivationStatusUpdate",e.notificationRedesign="edgeServerUX.shopping.notificationRedesign",e.notificationRedesignPH="edgeServerUX.shopping.notificationRedesignPH",e.notificationRedesignCoupons="edgeServerUX.shopping.notificationRedesignCoupons",e.pricehistoryWC="edgeServerUX.shopping.pricehistoryWC",e.sitewideEnabled="edgeServerUX.shopping.sitewideEnabled",e.autoApplyRewardsOverlay="edgeServerUX.shopping.autoApplyRewardsOverlay",e.extraAutoApplyExtraDelay="edgeServerUX.shopping.extraAutoApplyExtraDelay",e.extraAutoApplyWaitAfterTyping="edgeServerUX.shopping.extraAutoApplyWaitAfterTyping",e.shorelineRevampMoreCashbackOffers="edgeServerUX.shopping.shorelineRevampMoreCashbackOffers",e.shorelineRevampSpbOffers="edgeServerUX.shopping.shorelineRevampSpbOffers",e.cpsPdpCommerceNotificationApplyVariant="edgeServerUX.shopping.cpsPdpCommerceNotificationApplyVariant",e.cpsPdpCommerceNotificationOkVariant="edgeServerUX.shopping.cpsPdpCommerceNotificationOkVariant",e.notificationRedesignPTPriceDrop="edgeServerUX.shopping.notificationRedesignPTPriceDrop",e.notificationRedesignPC="edgeServerUX.shopping.notificationRedesignPC",e.notificationRedesignCBAffiliate="edgeServerUX.shopping.notificationRedesignCBAffiliate",e.notificationRedesignCBOptOutUser="edgeServerUX.shopping.notificationRedesignCBOptOutUser",e.notificationRedesignPCBStorewide="edgeServerUX.shopping.notificationRedesignPCBStorewide",e.notificationRedesignCBCPS="edgeServerUX.shopping.notificationRedesignCBCPS",e.articleProductsScenario="edgeServerUX.shopping.articleProductsScenario",e.articleProductsScenarioProd="edgeServerUX.shopping.articleProductsScenarioProd",e.autoApplyGates="edgeServerUX.shopping.aaGates",e.contextualCashback="edgeServerUX.shopping.contextualCashback",e.contextualCashbackSearch="edgeServerUX.shopping.contextualCashbackSearch",e.disableIconWhenNoRetailerData="edgeServerUX.shopping.disableIconWhenNoRetailerData",e.autoApplyCouponReduction="edgeServerUX.shopping.becoupons",e.autoApplyEarlyEnd="edgeServerUX.shopping.aaEarlyEnd",e.autoApplyEarlyEndTimer="edgeServerUX.shopping.aaEarlyEndTimer",e.autoApplyDisableRemove="edgeServerUX.shopping.aaRemove",e.autoApplyDisableRemoveSame="edgeServerUX.shopping.aaRemoveSame",e.autoApplyEdgeEnabledNotification="edgeServerUX.shopping.notifblk",e.autoApplyForceApplyFirst="edgeServerUX.shopping.aaForceApplyFirst",e.autoApplyCartRanking="edgeServerUX.shopping.cartRanking",e.autoApplyBGAASave="edgeServerUX.shopping.aaBGAASave",e.couponApplicableFor="edgeServerUX.shopping.couponApplicableFor",e.percentStorewide="edgeServerUX.shopping.percentStorewide",e.productTrackingToggleModule="edgeServerUX.shopping.productTrackingToggleModule",e.revampedStorybook="edgeServerUX.shopping.revampStorybook",e.triggerReportRewards="edgeServerUX.shopping.triggerReportRewards",e.disableBackfillScenario="edgeServerUX.shopping.disableBackfillScenario",e.disableCashbackOnCouponCopy="edgeServerUX.shopping.disableCashbackOnCouponCopy",e.enableShorelineCashbackRedemption="edgeServerUX.shopping.enableShorelineCashbackRedemption",e.omniboxCouponsAAWithoutShoreline="edgeServerUX.shopping.omniboxCouponsAAWithoutShoreline",e.omniboxCouponsAAWithShoreline="edgeServerUX.shopping.omniboxCouponsAAWithShoreline",e.enableReferredOmnibox="edgeServerUX.shopping.enableReferredOmnibox",e.bgaaUXOnPDP="edgeServerUX.shopping.bgaaUXOnPDP",e.phCta="edgeServerUX.shopping.phCta",e.enableShorelineCashbackRedemptionDecision="edgeServerUX.shopping.enableShorelineCashbackRedemptionDecision",e.GenExperiences="edgeServerUX.shopping.GenExperiences",e.GenExperiencesServerPromptName="edgeServerUX.shopping.GenExperiencesServerPromptName",e.GenExperiencesNoServerCache="edgeServerUX.shopping.GenExperiencesNoServerCache",e.enableExtractionServicePD="edgeServerUX.shopping.enableExtractionServicePD",e.purchaseConfirmation="edgeServerUX.shopping.purchaseConfirmation",e.enablePhInsightsV2="edgeServerUX.shopping.enablePhInsightsV2",e.enableOfferDeactivation="edgeServerUX.shopping.enableOfferDeactivation",e.telemetryTrafficControl="edgeServerUX.shopping.telemetryTrafficControl",e.inlineContent="msExtensionIC",e.extensionAuthenticationUX="msExtensionAuthenticationUX",e.extensionAuthenticationService="msExtensionAuthenticationService",e.extensionCashbackUX="msExtensionCashbackUX",e}({});var S=class{constructor(){d(this,"name",void 0),d(this,"supported",void 0),d(this,"variant",void 0),d(this,"multipleVariants",void 0)}},u=class{constructor(){d(this,"value",void 0),d(this,"params",void 0)}};let m=function(e){return e.msShoppingTestExp1="msShoppingTestExp1",e.msShoppingTestExp2="msShoppingTestExp2",e.msShoppingTestExp3="msShoppingTestExp3",e.msShoppingTestExp4="msShoppingTestExp4",e.msShoppingTestExp5="msShoppingTestExp5",e.msShoppingTestExp6="msShoppingTestExp6",e.msShoppingTestExp7="msShoppingTestExp7",e.msShoppingTestExp8="msShoppingTestExp8",e.msShoppingTestExp9="msShoppingTestExp9",e.msShoppingTestExp10="msShoppingTestExp10",e.msShoppingTestExp11="msShoppingTestExp11",e.msShoppingTestExp12="msShoppingTestExp12",e.msShoppingTestExp13="msShoppingTestExp13",e.msShoppingTestExp14="msShoppingTestExp14",e.msShoppingTestExp15="msShoppingTestExp15",e.msShoppingTestExp16="msShoppingTestExp16",e.msShoppingTestExp17="msShoppingTestExp17",e.msShoppingTestExp18="msShoppingTestExp18",e.msShoppingTestExp99="msShoppingTestExp99",e}({});const E=new class{constructor(){d(this,"experiments",new Map),d(this,"serviceExperiments",new Map),d(this,"experimentsSet",new Set),d(this,"serviceExperimentsSet",new Set),d(this,"enablePCBannerUX",!1),d(this,"expRawData",void 0),d(this,"serviceFeatures",void 0),d(this,"rawEnabledFeatures",void 0),d(this,"rawEnabledServiceFeatures",void 0),d(this,"enabledFeaturesJson",void 0)}Create(e,t){this.experiments=new Map,this.serviceExperiments=new Map,this.experimentsSet=new Set,this.serviceExperimentsSet=new Set,"string"==typeof e?this.CreateFromJsonStr(e):(this.CreateClientFeatures(e),this.enabledFeaturesJson=e),t&&("string"==typeof t?this.CreateServiceFeaturesFromJsonStr(t):this.CreateServiceFeaturesFromJsonObj(t)),this.rawEnabledFeatures=e,this.rawEnabledServiceFeatures=t}GetEnabledFeaturesJson(){return this.enabledFeaturesJson}GetEnabledFeaturesMap(){return this.experiments??new Map}GetExpRawData(){return this.expRawData}GetRawEnabledFeatures(){return this.rawEnabledFeatures}GetRawEnabledServiceFeatures(){return this.rawEnabledServiceFeatures}GetServiceExperiments(){return this.serviceFeatures}GetServerSideExperiments(){const e={};return this.serviceExperiments.forEach(((t,i)=>{e[i]=t})),e}isExperimentActive(e){return!this.experimentsSet.has(h.disableAllFlights)&&!this.serviceExperimentsSet.has(h.disableAllFlights)&&(this.experimentsSet.has(e)||this.serviceExperimentsSet.has(e))}getServiceExperimentValue(e){return this.serviceExperiments.get(e)?.value}getServiceExperimentParams(e){return this.serviceExperiments.get(e)?.params}isTestFlagActive(e){return this.experimentsSet.has(e.toString())||this.serviceExperimentsSet.has(e.toString())}setShowPCBannerUX(e,t,i,r){this.enablePCBannerUX=void 0!==e&&e||void 0!==t&&t&&void 0!==i&&i.CompetingPrices&&i.CompetingPrices.length>0&&void 0!==r&&r}isAffiliateActivationStatusUpdateUIActive(){return v.IsMobile()||this.isExperimentActive(h.enableAffiliateActivationStatusUpdate)||this.isExperimentActive(h.affiliateFastActivationReturn)}isPCBannerUXExperimentActive(){return(this.experimentsSet.has(h.pcBannerUXExperimentation1)||this.experimentsSet.has(h.pcBannerUXExperimentation2)||this.experimentsSet.has(h.pcBannerUXExperimentation3)||this.experimentsSet.has(h.pcBannerUXExperimentation4)||this.experimentsSet.has(h.pcBannerUXExperimentation5)||this.experimentsSet.has(h.pcBannerUXExperimentation6))&&this.enablePCBannerUX}newFlyoutHeaderActive(){return this.experimentsSet.has(h.newHeader)}isVariantActive(e,t){return!!this.isExperimentActive(e)&&this.experiments.get(e)?.variant===t}isMultipleVariantActive(e,t,i){if(this.isExperimentActive(e)){const r=this.experiments.get(e);if(!r)return!1;if(r.variant===i)return!0;if(r.multipleVariants&&r.multipleVariants.length>0&&-1!==r.multipleVariants.findIndex((e=>e.key===t&&e.value===i)))return!0}return!1}getVariantValue(e,i,r){return parseInt(e.find((e=>e.key===i&&("number"==typeof e.value||!t(e.value))))?.value??r.toString(),void 0)}getVariantStringValue(e,i,r){return e.find((e=>e.key===i&&!t(e.value)))?.value??r}getMultipleVariants(e){return this.isExperimentActive(e)?this.experiments.get(e)?.multipleVariants??[]:[]}getVariantName(e){const t=this.experiments.get(e);if(this.isExperimentActive(e)&&t&&t.variant)return t.variant}createServiceFeaturesFromMap(e){const t=new Map;e&&e.length>0&&e.forEach((e=>{const i=new u,r=e[0];"object"==typeof e[1]?(i.value=e[1].value?.toString(),i.params=new Map(Object.entries(e[1].params))):(i.value=e[1],i.params=new Map),"false"!==i.value&&(t.set(r,i),this.serviceExperimentsSet.add(r))})),this.serviceExperiments=t}CreateClientFeatures(e){this.expRawData=e;const t=new Map;if(e&&e.length>0){const i=Object.values(h),r=Object.values(m);e.forEach((e=>{const o=new S;o.name=e.name,o.supported=!1,e.params?.length>1?o.multipleVariants=e.params:e.params?.length>0&&(o.variant=e.params[0].value),(i.includes(e.name)||r.includes(e.name))&&(1===e.params?.length&&!1===e.params[0]?o.supported=!1:(o.supported=!0,this.experimentsSet.add(e.name))),t.set(o.name,o)}))}this.experiments=t}CreateFromJsonStr(e){try{if(e){const t=JSON.parse(e);this.enabledFeaturesJson=t,this.CreateClientFeatures(t)}}catch(e){}}CreateServiceFeaturesFromJsonStr(e){if(!e)return;const t=JSON.parse(e);this.CreateServiceFeaturesFromJsonObj(t)}CreateServiceFeaturesFromJsonObj(e){e&&(this.serviceFeatures=e,this.createServiceFeaturesFromMap(Object.entries(e)))}},A=new class{constructor(){d(this,"appName",void 0),this.appName=i.Edge}SetAppName(e){this.appName=e}GetAppName(){return this.appName}};class C{static GetBuildVersion(){try{const e=navigator.userAgent.match(/Edg(?:A|iOS)?\/([0-9]+\.[0-9]+\.[0-9]+\.[0-9]+)/);if(e&&2===e.length)return e[1];throw Error("Invalid build version.")}catch(e){}}static IsBuildVersionSupported(e){const t=C.GetBuildVersion();if(void 0===t)return!1;const i=C.CompareBuildVersions(t,e);return void 0!==i&&i>=0}static CompareBuildVersions(e,t){if(e&&t){const i=e.split("."),r=t.split(".");if(4!==i.length||4!==r.length)return;for(let e=0;e<4;e++){const t=parseInt(i[e],10),o=parseInt(r[e],10);if(isNaN(t)||isNaN(o))return;if(t>o)return 1;if(o>t)return-1}return 0}}static IsOtherSellersExpActive(e){return"walmart.com"===e||E.isExperimentActive(h.showAmazonOtherSellers)}static GetClientName(){try{if(-1!==navigator.userAgent.toLowerCase().indexOf("android"))return i.EdgeAndroid;if(/iPad|iPhone|iPod/.test(navigator.userAgent))return i.EdgeiOS}catch(e){throw Error("Error getting client name")}return A.GetAppName()}static IsMobile(){return[i.EdgeAndroid,i.EdgeiOS].includes(C.GetClientName())}}d(C,"enabledServiceFlights","");var v=C;let I=function(e){return e[e.NOT_ACTIVATING=0]="NOT_ACTIVATING",e[e.STARTED=1]="STARTED",e[e.FAILED=2]="FAILED",e[e.FAILED_SWITCH_TO_MSA_PROFILE=3]="FAILED_SWITCH_TO_MSA_PROFILE",e[e.FAILED_SWITCH_TO_MSA_DECLINED=4]="FAILED_SWITCH_TO_MSA_DECLINED",e[e.FAILED_SIGN_INTO_PROFILE=5]="FAILED_SIGN_INTO_PROFILE",e[e.PENDING_ENROLL_REBATES_USER=6]="PENDING_ENROLL_REBATES_USER",e[e.PENDING_SWITCH_TO_MSA_PROFILE=7]="PENDING_SWITCH_TO_MSA_PROFILE",e[e.PENDING_SIGN_INTO_PROFILE=8]="PENDING_SIGN_INTO_PROFILE",e[e.PENDING_ACTIVATION_IN_PROFILE=9]="PENDING_ACTIVATION_IN_PROFILE",e[e.FAILED_ACTIVATION_NO_RETAILER_DATA=10]="FAILED_ACTIVATION_NO_RETAILER_DATA",e[e.FAILED_ACTIVATION_URL_FETCH=11]="FAILED_ACTIVATION_URL_FETCH",e[e.FAILED_ACTIVATION_INVALID_JSON=12]="FAILED_ACTIVATION_INVALID_JSON",e[e.FAILED_ACTIVATION_URL_PARSING=13]="FAILED_ACTIVATION_URL_PARSING",e[e.FAILED_ACTIVATION_URL_INVALID=14]="FAILED_ACTIVATION_URL_INVALID",e[e.FAILED_ACTIVATION_ENROLL_NAVIGATION_LOAD=15]="FAILED_ACTIVATION_ENROLL_NAVIGATION_LOAD",e[e.FAILED_ACTIVATION_ENROLL_NAVIGATION_TIMED_OUT=16]="FAILED_ACTIVATION_ENROLL_NAVIGATION_TIMED_OUT",e[e.FAILED_ACTIVATION_ATTRIBUTION_NAVIGATION_LOAD=17]="FAILED_ACTIVATION_ATTRIBUTION_NAVIGATION_LOAD",e[e.FAILED_ACTIVATION_ATTRIBUTION_NAVIGATION_TIMED_OUT=18]="FAILED_ACTIVATION_ATTRIBUTION_NAVIGATION_TIMED_OUT",e[e.ACTIVATED=19]="ACTIVATED",e[e.FAILED_ACTIVATION_UNIFIED_API_BACKED_OFF=20]="FAILED_ACTIVATION_UNIFIED_API_BACKED_OFF",e[e.FAILED_ACTIVATION_ENROLL_VERIFY_COOKIE=21]="FAILED_ACTIVATION_ENROLL_VERIFY_COOKIE",e[e.FAILED_ACTIVATION_ENROLL_COOKIE_TIMED_OUT=22]="FAILED_ACTIVATION_ENROLL_COOKIE_TIMED_OUT",e[e.PENDING_BING_SSO=23]="PENDING_BING_SSO",e[e.PENDING_BING_IDENTITY_CHECK=24]="PENDING_BING_IDENTITY_CHECK",e[e.FAILED_ACTIVATION_ENROLL_API=26]="FAILED_ACTIVATION_ENROLL_API",e[e.FAILED_FETCHING_USER_INFO=27]="FAILED_FETCHING_USER_INFO",e}({});I.STARTED,I.PENDING_ACTIVATION_IN_PROFILE,I.PENDING_SWITCH_TO_MSA_PROFILE,I.PENDING_SIGN_INTO_PROFILE,I.PENDING_ACTIVATION_IN_PROFILE,I.PENDING_BING_SSO,I.PENDING_BING_IDENTITY_CHECK,I.FAILED,I.FAILED_SWITCH_TO_MSA_PROFILE,I.FAILED_SWITCH_TO_MSA_DECLINED,I.FAILED_SIGN_INTO_PROFILE,I.FAILED_ACTIVATION_NO_RETAILER_DATA,I.FAILED_ACTIVATION_URL_FETCH,I.FAILED_ACTIVATION_INVALID_JSON,I.FAILED_ACTIVATION_URL_PARSING,I.FAILED_ACTIVATION_URL_INVALID,I.FAILED_ACTIVATION_ENROLL_NAVIGATION_LOAD,I.FAILED_ACTIVATION_ENROLL_NAVIGATION_TIMED_OUT,I.FAILED_ACTIVATION_ATTRIBUTION_NAVIGATION_LOAD,I.FAILED_ACTIVATION_ATTRIBUTION_NAVIGATION_TIMED_OUT,I.FAILED_ACTIVATION_UNIFIED_API_BACKED_OFF,I.FAILED_ACTIVATION_ENROLL_VERIFY_COOKIE,I.FAILED_ACTIVATION_ENROLL_COOKIE_TIMED_OUT,I.FAILED_ACTIVATION_ENROLL_API,I.FAILED_FETCHING_USER_INFO;const T=(new Set(["₹","￡","€","¥","￥","₽","元","₩","₱","đ","₫","฿","₦","US$","CA$","CL$","AU$","MX$","MXN$","C$","A$","R$","NZ$","COL$","NT$","£E","SG$","zł","S$","AR$","$","£","USD","INR","GBP","CAD","EUR","AUD","JPY","BRL","MXN","NZD","CNY","KRW","RUB","IDR","PHP","VND","THB","TWD","EGP","DKK","SEK","RM","MYR","AMD","CHF","PLN","NOK","SGD","CZK","TL","TRY","COP","ZAR","CLP","SAR","AED","Rp","dr.","Fr.","Kč","Rs","kr.","kr","R","円","Rs","HUF","HK$","руб","₪","JMD","lei","UAH","DOP","XAF","XOF","KD","KWD","AWG","CFP","BGN","BAM","KZT","MAD","BOB","GEL","JOD","QAR","XCD","MNT","TND","NGN","BDT","S/","MDL","MZN","PKR","LAK","BWP","OMR","CRC","DZD","KES","LKR"]),new Map([["en-us","$"],["en-gb","£"],["en-ca","CA$"],["en-au","AU$"],["en-in","₹"],["fr-fr","€"],["de-de","€"],["ja-jp","¥"],["it-it","€"],["nl-nl","€"],["es-es","€"],["en-nz","NZ$"],["pt-br","R$"],["zh-cn","元"],["es-mx","MX$"],["ko-kr"," ₩"],["ru-ru","₽"],["id-id","Rp"],["fil-ph","₱"],["vi-vn","đ"],["th-th","฿"],["zh-tw","NT$"],["ar-eg","EGP"],["de-at","€"],["da-dk","kr."],["de-ch","Fr."],["pl-pl","zł"],["sv-se","kr"],["en-my","RM"],["no-no","NOK"],["en-sg","S$"],["fi-fi","€"],["fr-be","€"],["cs-cz","Kč"],["tr-tr","TL"],["es-co","COL$"],["en-za","R"],["en-sa","SAR"],["en-eg","EGP"],["ar-ae","AED"],["es-ar","AR$"],["en-ng","₦"],["ar-kw","KD"],["ar-kw","KWD"]]),new Set(["USD","INR","GBP","EUR","NZD","BRL","CAD","AUD","JPY","CNY","MXN","KRW","RUB","IDR","PHP","VND","THB","TWD","EGP","DKK","CHF","PLN","SEK","MYR","NOK","SGD","CZK","TRY","COP","ZAR","CLP","SAR","AED","ARS","NGN","KWD"]),new Map([["$","USD"],["₹","INR"],["Rs","INR"],["£","GBP"],["€","EUR"],["NZ$","NZD"],["R$","BRL"],["CA$","CAD"],["C$","CAD"],["A$","AUD"],["AU$","AUD"],["¥","JPY"],["￥","JPY"],["円","JPY"],["元","CNY"],["MX$","MXN"],["MXN$","MXN"],["￡","GBP"],["₩","KRW"],["₽","RUB"],["Rp","IDR"],["₱","PHP"],["đ","VND"],["₫","VND"],["฿","THB"],["NT$","TWD"],["EGP","EGP"],["kr.","DKK"],["Fr.","CHF"],["zł","PLN"],["dr.","SEK"],["RM","MYR"],["NOK","NOK"],["S$","SGD"],["Kč","CZK"],["TL","TRY"],["COL$","COP"],["R","ZAR"],["kr","DKK"],["SG$","SGD"],["SAR","SAR"],["AED","AED"],["AMD","AMD"],["AR$","ARS"],["₦","NGN"],["KD","KWD"]]));function N(){return function(e){if(!e)return"";const t=e.toLowerCase().match(/(w){3}.?\./);return t&&null!=t.index&&t[0]?e.substring(t.index+t[0].length):e}(window.location.hostname)}function f(){return window.location.toString()}new Set(T.keys()),new Map([["USD","$"],["INR","₹"],["GBP","£"],["EUR","€"],["NZD","NZ$"],["BRL","R$"],["CAD","CA$"],["AUD","AU$"],["JPY","¥"],["CNY","元"],["MXN","MX$"],["GBP","￡"],["KRW","₩"],["RUB","₽"],["IDR","Rp"],["PHP","₱"],["VND","đ"],["THB","฿"],["TWD","NT$"],["EGP","EGP"],["DKK","kr."],["CHF","Fr."],["PLN","zł"],["SEK","dr."],["MYR","RM"],["NOK","NOK"],["SGD","S$"],["CZK","Kč"],["TRY","TL"],["COP","COL$"],["ZAR","R"],["SAR","SAR"],["AED","AED"],["ARS","AR$"],["NGN","₦"],["KWD","KD"],["ج.م.","EGP"]]);class R extends Error{constructor(e){super(e),Object.setPrototypeOf(this,R.prototype)}}class O{static HasVisibleElement(e){return O.CountVisibleElements(e)>0}static HasVisibleElementInViewport(e){return O.CountVisibleElementsInViewport(e)>0}static CountVisibleElements(e){if(!g.IsValidDataField(e))return 0;const t=e.split(";");for(const e of t){const t=O.CountVisibleElementsSingleSel(e);if(t>0)return t}return 0}static CountVisibleElementsInViewport(e){if(!g.IsValidDataField(e))return 0;const t=e.split(";");for(const e of t){const t=O.CountVisibleElementsSingleSelInViewport(e);if(t>0)return t}return 0}static RunQuerySelectorAll(e,t){if(!g.IsValidDataField(e))return[];const i=(e=e.replace(/;/g,",")).split("<");let r;r=t?t.querySelectorAll(i[0]):document.querySelectorAll(i[0]);for(const e of i.slice(1)){const t=r[0]?.shadowRoot;if(!t)return[];r=t.querySelectorAll(e)}return r}static IsElementVisible(e){return e&&e.offsetWidth>0&&e.offsetHeight>0}static IsElementVisibleInViewport(e){if(e){const t=e.getBoundingClientRect();return t.top>=0&&t.left>=0&&t.bottom<=(window.innerHeight||document.documentElement.clientHeight)&&t.right<=(window.innerWidth||document.documentElement.clientWidth)}return!1}static GetFirstVisibleElement(e,t){if(!g.IsValidDataField(e))return;const i=e.split(";");for(const e of i)try{const i=O.RunQuerySelectorAll(e,t);for(const e of i)if(O.IsElementVisible(e))return e}catch(e){}}static GetAllVisibleElements(e){if(!g.IsValidDataField(e))return[];const t=e.split(";"),i=[];for(const e of t){const t=O.RunQuerySelectorAll(e);for(const e of t)O.IsElementVisible(e)&&i.push(e)}return i}static GetTextValue(e,t){if(!e||!g.IsValidDataField(e))return"";const i=e.split(";"),r=i[0],o=O.GetFirstVisibleElement(r,t);if(!o)return"";let n=o,s=n.innerText;if(1===i.length)n=O.NormalizeIfSuperscripted(o),s=n.innerText;else{const e=n.cloneNode(!0);let o=i[1];const a=O.GetFirstVisibleElement(o,n)??O.GetFirstVisibleElement(o,t);let p="";if(a&&a.innerText){if(p="."+a.innerText,n.contains(a)){const t=O.GetFirstMatchingElement(o,e);if(t?.innerText)e.removeChild(t);else{o.startsWith(r)&&(o=o.slice(r.length));const t=this.GetFirstMatchingElement(o,e);t?.innerText&&e.removeChild(t)}s=e?.innerText?e.innerText:s}s+=p}if(i.length>2){for(const t of i.slice(2)){const i=this.GetFirstMatchingElement(t,e);i?.innerText&&e.removeChild(i)}s=e?.innerText?e.innerText:s}s+=p}return O.StripInvalidJSONCharacters(s)}static GetItemizedData(e,t,i){let r="";if(e&&""!==e){const o=O.RunQuerySelectorAll(e,i);for(const e of o)e&&e.textContent&&(r+=e.textContent?.trim()+t)}return r}static StripInvalidJSONCharacters(e){return e.replace(/\n/gi,"")}static NormalizeIfSuperscripted(e){if(e&&e.innerHTML&&e.innerHTML.toLowerCase().indexOf("</sup>")>-1)try{const t=e.cloneNode(!0),i=t.childNodes.length;for(let e=0;e<i;e++){const i=t.childNodes[e];if("SUP"===i.tagName){let e=i.innerText;const r=/[0-9\.]+/g.exec(e);if(null!==r)return e="."+r[0],i.innerText=e,t}}}catch(t){return e}return e}static GetFirstMatchingElement(e,t){if(!g.IsValidDataField(e))return;const i=e.split(";");for(const e of i){const i=O.RunQuerySelectorAll(e,t);for(const e of i)if(e)return e}}static GetAllMatchingElements(e){if(!g.IsValidDataField(e))return[];const t=e.split(";"),i=[];for(const e of t)try{const t=O.RunQuerySelectorAll(e);for(const e of t)e&&i.push(e)}catch(e){}return i}static CountVisibleElementsSingleSel(e){if(!g.IsValidDataField(e))return 0;const t=O.RunQuerySelectorAll(e);let i=0;for(const e of t)O.IsElementVisible(e)&&i++;return i}static CountVisibleElementsSingleSelInViewport(e){if(!g.IsValidDataField(e))return 0;const t=O.RunQuerySelectorAll(e);let i=0;for(const e of t)O.IsElementVisibleInViewport(e)&&i++;return i}}var D=O,b=class{constructor(){d(this,"orderDate",void 0),d(this,"totalCost",void 0),d(this,"orderNumber",void 0),d(this,"orderDetailsPage",void 0),d(this,"listOfProducts",void 0),d(this,"orderStatus",void 0),d(this,"orderSummary",void 0),d(this,"trackPackageButtonSelector",void 0)}};class U{constructor(){d(this,"email",void 0),d(this,"orderNumber",void 0),d(this,"phoneNumber",void 0),d(this,"trackingUrl",void 0)}static create(e){const t=new U;return e&&(t.email=e.email,t.orderNumber=e.orderNumber,t.phoneNumber=e.phoneNumber,t.trackingUrl=e.trackingUrl),t}}var P=U;class _{constructor(){d(this,"productImageSelector",void 0),d(this,"productUrlSelector",void 0)}static create(e){const t=new _;return e&&(t.productImageSelector=e.productImageSelector,t.productUrlSelector=e.productUrlSelector),t}}var x=_;class w{constructor(){d(this,"trackPackageButtonSelector",void 0),d(this,"orderSummarySelector",void 0),d(this,"productContainerSelector",void 0),d(this,"productContentSelector",void 0)}static create(e){const t=new w;if(e){t.trackPackageButtonSelector=e.trackPackageButtonSelector,t.orderSummarySelector=e.orderSummarySelector,t.productContainerSelector=e.productContainerSelector;const i=e.productContentSelector;i&&(t.productContentSelector=x.create(i))}return t}}var F=w;class X{constructor(){d(this,"orderDateSelector",void 0),d(this,"totalCostSelector",void 0),d(this,"orderNumberSelector",void 0),d(this,"orderDetailsPageSelector",void 0),d(this,"shipmentContainerSelector",void 0),d(this,"shipmentContentSelector",void 0)}static create(e){const t=new X;if(e){t.orderDateSelector=e.orderDateSelector,t.totalCostSelector=e.totalCostSelector,t.orderNumberSelector=e.orderNumberSelector,t.orderDetailsPageSelector=e.orderDetailsPageSelector,t.shipmentContainerSelector=e.shipmentContainerSelector;const i=e.shipmentContentSelector;i&&(t.shipmentContentSelector=F.create(i))}return t}}var y=X;class L{constructor(){d(this,"trackingUrl",void 0),d(this,"clickToOpenOrderSelector",void 0),d(this,"orderContainerSelector",void 0),d(this,"orderContentSelector",void 0)}static create(e){const t=new L;if(e){t.trackingUrl=e.trackingUrl,t.clickToOpenOrderSelector=e.clickToOpenOrderSelector,t.orderContainerSelector=e.orderContainerSelector;const i=e.orderContentSelector;i&&(t.orderContentSelector=y.create(i))}return t}}var k=L;class V{constructor(){d(this,"impressionId",void 0),d(this,"domain",void 0),d(this,"packageTrackingSelectors",void 0)}static create(e){const t=new V;if(e){t.impressionId=e.impressionId,t.domain=e.domain??N();const i=e.packageTrackingSelectors;i&&(t.packageTrackingSelectors=i.map((e=>k.create(e))))}return t}}var G=V,M=class{constructor(){d(this,"productImageUrl",void 0),d(this,"productName",void 0),d(this,"productUrl",void 0)}};class B{initialize(e){e.splice(0,2);try{this.initializeRuntime(e)}catch(e){return!1}return!0}raiseMessageFromHost(e){const t=e.shift();if(t)try{this.handleMessages(e,t)}catch(e){}}postMessageToHost(e,t){try{this.getNativeHandler().sendMessageToHost(e,t)}catch(e){}}}const H=new class extends B{initializeRuntime(e){throw new Error("This should never be called.")}handleMessages(e,t){if("GetTrackingPageData"===t)try{"interactive"===document.readyState||"complete"===document.readyState?window.getTrackingPageData(e):window.addEventListener&&window.addEventListener("DOMContentLoaded",(t=>{window.checkAndGetTrackingPageData(e)}))}catch(e){}}getNativeHandler(){return trackingPageValidatorNativeHandler}};class W{static getTextContentFromHtmlElement(e){if(!e)return"";if(e.hasChildNodes()){let t="";for(const i of e.childNodes)if(i.nodeType===Node.TEXT_NODE&&i.textContent){const e=i.textContent.trim();e&&(t+=e)}if(t)return t}const t=e.textContent||e.innerText;return t?t.trim():""}static getOrderStatusFromTextInput(e){if(!e)return r.ERROR;const t="i";return new RegExp(o.DELIVERED_REGEX,t).test(e)?r.DELIVERED:new RegExp(o.SHIPPED_REGEX,t).test(e)?r.SHIPPED:new RegExp(o.RETURNED_REGEX,t).test(e)?r.RETURNED:new RegExp(o.CANCELED_REGEX,t).test(e)?r.CANCELED:r.ORDERED}static isExpiredDeliveryDate(e){if(e&&e.toLowerCase().includes("cancel"))return z.CANCELED_ORDER;const t=this.getOrderStatusFromTextInput(e),i=t===r.ERROR||t===r.SHIPPED||t===r.ORDERED;if(!e||isNaN(Date.parse(e))||i)return!1;const o=e.match(/\d{4}$/gs);null!=o&&0!==o.length||(e+=" "+(new Date).getFullYear());const n=new Date(Date.parse(e));return(Date.now()-n.getTime())/864e5>2&&z.EXPIRED_DELIVERY_DATE}static getOrderDateForSpecialWebsites(e,t){const i=W.getTextContentFromHtmlElement(t);switch(e){case"etsy.com":if(i.startsWith("On")||i.startsWith("on"))return i.substring(2).trim();break;case"alibaba.com":if(i.startsWith("|"))return i.substring(1).trim()}return i}static getOrderDetailsPageForSpecialWebsites(e,t){return"walmart.com"===e.domain?`${e.domain}/orders/${t}`:""}static getOrderNumberForSpecialWebsites(e,t){const i=W.getTextContentFromHtmlElement(t);switch(e){case"etsy.com":if(t)return t.getAttribute("data-receipt-id")??"";break;case"walmart.com":return W.getFirstRegexMatchOrDefaultString(i,/(?<=Order#\s\s*).*?(?=\s*(\|\sTotal))/gs);default:return i.startsWith("#")?i.substring(1).trim():i}return""}static getTotalCostForSpecialWebsites(e,t){switch(e){case"walmart.com":return W.getFirstRegexMatchOrDefaultString(t,/(?<=Total\s\$\s*)\d+\.\d+/gs);case"ebay.com":return W.getFirstRegexMatchOrDefaultString(t,/(?<=\$)\d+\.\d+/gs);default:return t}}static getFirstRegexMatchOrDefaultString(e,t){const i=e.match(t);return null!=i&&i.length>0?i[0]:e}static getHrefFromHtmlElement(e){return e?e.getAttribute("href"):""}}var K=W;let $=function(e){return e.INVALID_TRACKING_PAGE_ERROR="InvalidTrackingPageError",e.TRACKING_PAGE_DATA="TrackingPageData",e.TRACKING_PAGE_ERROR="TrackingPageError",e.TRACKING_PAGE_PARSING_ERROR="TrackingPageParsingError",e.TRACKING_PAGE_SCRIPT_ERROR="TrackingPageScriptError",e}({}),z=function(e){return e.NO_ORDER_CONTAINER_SELECTOR="orderContainerSelector is null",e.NO_ORDER_CONTAINERS="No order containers found",e.NO_SHIPMENT_CONTAINER_SELECTOR="shipmentContainerSelector is null",e.NO_ORDER_NUMBER="No order number found",e.NOT_ORDER_TO_EXTRACT="Not the order to extract",e.NO_ORDER_DATE="No order date found",e.NOT_VALID_ORDER_FOR_TRACKING_PAGE_FRE="Not a valid order for tracking page FRE",e.NO_SHIPMENT_CONTAINERS="No shipment containers found",e.CANCELED_ORDER="Order is canceled",e.EXPIRED_DELIVERY_DATE="Expired delivery date",e.NO_PRODUCT_CONTAINERS="No product containers found",e.INVALID_PRODUCT_DETAILS="Invalid product details",e}({}),J="",j=!1,Y="",Z=[];async function q(e){let i="";try{J=e[2],j="true"===e[3].toLowerCase(),Y="",Z=[];const r=JSON.parse(e[0]),o=G.create(r),n=JSON.parse(e[1]),s=n?n.filter((e=>!t(e?.orderNumber??""))).map((e=>P.create(e))):[];i=o?.impressionId??"",await g.WaitForCondition((async()=>function(e){const t=e?.packageTrackingSelectors;if(!t)throw new R("Empty or null package tracking selectors");const i=f();let r=!1;for(const e of t)if(e.trackingUrl&&i.includes(e.trackingUrl)){r=!0;break}if(!r)throw new R("Invalid Order Page URL");return!0}(o)),5e3).then((async()=>{await async function(e,i,r){let o=new Set;r?.length&&(o=new Set(r.map((e=>e.orderNumber))));const n=await async function(e,i){const r=[],o=e?.packageTrackingSelectors;if(!o)return r;for(const n of o){const o=n?.clickToOpenOrderSelector??"";if(!t(o)){const t=D.RunQuerySelectorAll(o),i=new Promise((e=>{const t=new MutationObserver((()=>{}));t.observe(document,{attributes:!0,childList:!0,subtree:!0}),setTimeout((()=>{t.disconnect(),e()}),1500)}));let r="homedepot.com"===e?.domain?1:0;for(;r<t.length;r++)t[r].click();await i}const s=n?.orderContainerSelector??"";if(t(s)){Z.push(z.NO_ORDER_CONTAINER_SELECTOR);continue}const a=D.RunQuerySelectorAll(s);if(a?.length)for(const o of a){const s=n?.orderContentSelector?.shipmentContainerSelector;if(!s){Z.push(z.NO_SHIPMENT_CONTAINER_SELECTOR);continue}const a=n?.orderContentSelector?.orderNumberSelector??"DUMMY_SELECTOR",p=o.querySelector(a),c=K.getOrderNumberForSpecialWebsites(e?.domain,p??o);if(t(c)){Z.push(z.NO_ORDER_NUMBER);continue}if(i?.size>0&&!i?.has(c)){Z.push(z.NOT_ORDER_TO_EXTRACT);continue}const g=n?.orderContentSelector?.orderDateSelector,l=g?o.querySelector(g):null,d=K.getOrderDateForSpecialWebsites(e?.domain,l);if(!Q(d))continue;const h=n?.orderContentSelector?.totalCostSelector,S=h?o.querySelector(h):null,u=K.getTextContentFromHtmlElement(S),m=K.getTotalCostForSpecialWebsites(e?.domain,u),E=n?.orderContentSelector?.orderDetailsPageSelector,A=E?o.querySelector(E):null,C=A?K.getHrefFromHtmlElement(A):K.getOrderDetailsPageForSpecialWebsites(e,c),v=o.querySelectorAll(s);if(v?.length)for(const e of v){const i=[],o=n?.orderContentSelector?.shipmentContentSelector,s=o?.trackPackageButtonSelector??"",a=o?.orderSummarySelector,p=o?.productContainerSelector??"",g=a?e.querySelector(a):null,l=K.getTextContentFromHtmlElement(g),h=K.isExpiredDeliveryDate(l);if(h===z.CANCELED_ORDER||h===z.EXPIRED_DELIVERY_DATE){Z.push(h);continue}const S=K.getOrderStatusFromTextInput(l),u=e.querySelectorAll(p);if(!u?.length){Z.push(z.NO_PRODUCT_CONTAINERS);continue}for(const e of u){const r=o?.productContentSelector,n=r?.productImageSelector,s=r?.productUrlSelector,a=n?e.querySelector(n):null,p=a?.src??"",c=s?e.querySelector(s):null,g=ee(c,a);if(t(p)&&t(g)){Z.push(z.INVALID_PRODUCT_DETAILS);continue}const l=Object.assign(new M,{productImageUrl:p,productName:g,productUrl:K.getHrefFromHtmlElement(c)});i.push(l)}if(!i?.length)continue;const E=Object.assign(new b,{listOfProducts:i,orderDate:d,orderDetailsPage:C,orderNumber:c,orderStatus:S,orderSummary:l,totalCost:m,trackPackageButtonSelector:s});r.push(E)}else Z.push(z.NO_SHIPMENT_CONTAINERS)}else Z.push(z.NO_ORDER_CONTAINERS)}return r}(i,o);(function(e,t){let i="Failed to extract any orders";t>0&&(i="Successful order history page extraction");const r={domain:N(),numOfOrders:t,extractionResults:Z};re(ie(e,$.TRACKING_PAGE_DATA,i,r))})(e,n.length),H.postMessageToHost($.TRACKING_PAGE_DATA,[JSON.stringify(n),J])}(i,o,s)})).catch((e=>{te(i,e,o)}))}catch(e){te(i,e,null)}}function Q(e){return!(j&&(t(e)?(Z.push(z.NO_ORDER_DATE),1):t(Y)?(Y=e,0):Y!==e&&(Z.push(z.NOT_VALID_ORDER_FOR_TRACKING_PAGE_FRE),1)))}function ee(e,i){const r=K.getTextContentFromHtmlElement(e);if(t(r)){const e=i?.getAttribute("alt")??"";return t(e)?i?.getAttribute("title")??"":e}return r}function te(e,t,i){let r=$.TRACKING_PAGE_SCRIPT_ERROR;const o=t?.message??"";t instanceof SyntaxError?r=$.TRACKING_PAGE_PARSING_ERROR:t instanceof R&&(r=$.INVALID_TRACKING_PAGE_ERROR),function(e,t,i,r){const o={domain:N(),pageUrl:f(),retailerData:JSON.stringify(r)},n=ie(e,$.TRACKING_PAGE_ERROR,i,o);n&&t&&(n.ErrorType=t),re(n)}(e,r,o,i),H.postMessageToHost(r,[o,J])}function ie(e,t,i,r){const o={};return o.JsonData=JSON.stringify(r),o.EventType=t,o.LogLevel="Information",o.Message=i,o.ClientContext={AppInfoClientName:v.GetClientName(),JSVersion:"2.258"},e&&(o.ImpressionId=e),o}function re(e){const t=[JSON.stringify(e)];H.postMessageToHost("LogScriptTelemetry",t)}window.checkAndGetTrackingPageData=function(e){(function(){try{return window.self!==window.top}catch(e){return!0}})()||q(e)},window.getTrackingPageData=q,window.trackingPageValidatorRuntime=H}();