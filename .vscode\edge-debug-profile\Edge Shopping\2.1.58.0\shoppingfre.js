!function(t){var e={};function i(n){if(e[n])return e[n].exports;var o=e[n]={i:n,l:!1,exports:{}};return t[n].call(o.exports,o,o.exports,i),o.l=!0,o.exports}i.m=t,i.c=e,i.d=function(t,e,n){i.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:n})},i.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},i.t=function(t,e){if(1&e&&(t=i(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(i.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var o in t)i.d(n,o,function(e){return t[e]}.bind(null,o));return n},i.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return i.d(e,"a",e),e},i.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},i.p="",i(i.s=154)}([function(t,e){t.exports=function(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}},function(t,e,i){"use strict";var n=i(106),o=i(10);class r{constructor(t,e,i){this.l=t,this.a=e,this.b=i}static fromObject(t){return!t||isNaN(t.l)||isNaN(t.a)||isNaN(t.b)?null:new r(t.l,t.a,t.b)}equalValue(t){return this.l===t.l&&this.a===t.a&&this.b===t.b}roundToPrecision(t){return new r(Object(o.g)(this.l,t),Object(o.g)(this.a,t),Object(o.g)(this.b,t))}toObject(){return{l:this.l,a:this.a,b:this.b}}}r.epsilon=216/24389,r.kappa=24389/27;var a=i(33);class s{constructor(t,e,i){this.x=t,this.y=e,this.z=i}static fromObject(t){return!t||isNaN(t.x)||isNaN(t.y)||isNaN(t.z)?null:new s(t.x,t.y,t.z)}equalValue(t){return this.x===t.x&&this.y===t.y&&this.z===t.z}roundToPrecision(t){return new s(Object(o.g)(this.x,t),Object(o.g)(this.y,t),Object(o.g)(this.z,t))}toObject(){return{x:this.x,y:this.y,z:this.z}}}function c(t){function e(t){return t<=.03928?t/12.92:Math.pow((t+.055)/1.055,2.4)}return function(t){return.2126*t.r+.7152*t.g+.0722*t.b}(new a.a(e(t.r),e(t.g),e(t.b),1))}s.whitePoint=new s(.95047,1,1.08883);const l=(t,e)=>(t+.05)/(e+.05);var u,A=i(132);function g(t){const e=Object(A.a)(t);return function(t){return"function"==typeof t?i=>e(Object.assign({},i,{backgroundColor:t(i)})):e(t)}}function h(t,e){const i=Object(A.a)(e);return e=>"function"==typeof e?n=>i(Object.assign({},n,{backgroundColor:e(n)}))[t]:i(e)[t]}i.d(e,"a",(function(){return u})),i.d(e,"d",(function(){return g})),i.d(e,"i",(function(){return h})),i.d(e,"g",(function(){return I})),i.d(e,"c",(function(){return M})),i.d(e,"e",(function(){return C})),i.d(e,"h",(function(){return p})),i.d(e,"f",(function(){return f})),i.d(e,"b",(function(){return D})),function(t){t.rest="rest",t.hover="hover",t.active="active",t.focus="focus",t.selected="selected"}(u||(u={}));const d=Object(A.a)(t=>{let e=Object(n.d)(t);if(null!==e)return e;if(e=Object(n.e)(t),null!==e)return e;throw new Error(t+' cannot be converted to a ColorRGBA64. Color strings must be one of the following formats: "#RGB", "#RRGGBB", or "rgb(r, g, b)"')});function I(t){return Object(n.a)(t)||Object(n.b)(t)}function M(t,e){return d(t).equalValue(d(e))}const C=Object(A.a)((t,e)=>function(t,e){const i=c(t),n=c(e);return i>n?l(i,n):l(n,i)}(d(t),d(e)),(t,e)=>t+e);function p(t){return c(d(t))}function f(...t){return e=>Math.max.apply(null,t.map(t=>t(e)))}const D=(t,e,i)=>Math.min(Math.max(t,e),i)},function(t,e,i){"use strict";i.d(e,"k",(function(){return r})),i.d(e,"a",(function(){return a})),i.d(e,"O",(function(){return s})),i.d(e,"j",(function(){return c})),i.d(e,"m",(function(){return l})),i.d(e,"d",(function(){return u})),i.d(e,"b",(function(){return A})),i.d(e,"c",(function(){return g})),i.d(e,"e",(function(){return h})),i.d(e,"i",(function(){return d})),i.d(e,"h",(function(){return I})),i.d(e,"f",(function(){return M})),i.d(e,"g",(function(){return C})),i.d(e,"x",(function(){return p})),i.d(e,"r",(function(){return f})),i.d(e,"o",(function(){return D})),i.d(e,"q",(function(){return y})),i.d(e,"y",(function(){return b})),i.d(e,"v",(function(){return j})),i.d(e,"u",(function(){return E})),i.d(e,"s",(function(){return m})),i.d(e,"t",(function(){return N})),i.d(e,"w",(function(){return O})),i.d(e,"C",(function(){return w})),i.d(e,"B",(function(){return v})),i.d(e,"z",(function(){return Q})),i.d(e,"A",(function(){return x})),i.d(e,"D",(function(){return B})),i.d(e,"G",(function(){return L})),i.d(e,"E",(function(){return S})),i.d(e,"F",(function(){return k})),i.d(e,"l",(function(){return T})),i.d(e,"p",(function(){return z})),i.d(e,"J",(function(){return F})),i.d(e,"H",(function(){return U})),i.d(e,"I",(function(){return Y})),i.d(e,"n",(function(){return H})),i.d(e,"N",(function(){return R})),i.d(e,"M",(function(){return P})),i.d(e,"K",(function(){return G})),i.d(e,"L",(function(){return K}));var n=i(27);function o(t){return e=>e&&void 0!==e[t]?e[t]:n.b[t]}const r=o("backgroundColor"),a=o("accentBaseColor"),s=(o("cornerRadius"),o("elevatedCornerRadius"),o("neutralPalette")),c=o("accentPalette"),l=(o("contrast"),o("designUnit"),o("baseHeightMultiplier"),o("baseHorizontalSpacingMultiplier"),o("direction")),u=(o("outlineWidth"),o("focusOutlineWidth"),o("disabledOpacity"),o("accentFillRestDelta"),o("accentFillHoverDelta")),A=o("accentFillActiveDelta"),g=o("accentFillFocusDelta"),h=o("accentFillSelectedDelta"),d=o("accentForegroundRestDelta"),I=o("accentForegroundHoverDelta"),M=o("accentForegroundActiveDelta"),C=o("accentForegroundFocusDelta"),p=o("neutralFillRestDelta"),f=o("neutralFillHoverDelta"),D=o("neutralFillActiveDelta"),y=o("neutralFillFocusDelta"),b=o("neutralFillSelectedDelta"),j=o("neutralFillInputRestDelta"),E=o("neutralFillInputHoverDelta"),m=o("neutralFillInputActiveDelta"),N=o("neutralFillInputFocusDelta"),O=o("neutralFillInputSelectedDelta"),w=o("neutralFillStealthRestDelta"),v=o("neutralFillStealthHoverDelta"),Q=o("neutralFillStealthActiveDelta"),x=o("neutralFillStealthFocusDelta"),B=o("neutralFillStealthSelectedDelta"),L=o("neutralFillToggleHoverDelta"),S=o("neutralFillToggleActiveDelta"),k=o("neutralFillToggleFocusDelta"),T=o("baseLayerLuminance"),z=o("neutralFillCardDelta"),F=(o("neutralForegroundDarkIndex"),o("neutralForegroundLightIndex"),o("neutralForegroundHoverDelta")),U=o("neutralForegroundActiveDelta"),Y=o("neutralForegroundFocusDelta"),H=o("neutralDividerRestDelta"),R=o("neutralOutlineRestDelta"),P=o("neutralOutlineHoverDelta"),G=o("neutralOutlineActiveDelta"),K=(o("fontWeight"),o("neutralOutlineFocusDelta"))},function(t,e,i){"use strict";var n=i(7),o=i.n(n),r=i(14),a=i.n(r),s=i(0),c=i.n(s),l=i(109),u=i(108),A=i(116),g=i(5),h=i(110),d=i(9),I=i(24),M=i(138),C=i(66),p=i(72),f=i(73),D=i(54),y=i(70),b=Object(y.a)(Object.keys,Object),j=Object.prototype.hasOwnProperty;var E=function(t){if(!Object(D.a)(t))return b(t);var e=[];for(var i in Object(t))j.call(t,i)&&"constructor"!=i&&e.push(i);return e},m=i(42);var N=function(t){return Object(m.a)(t)?Object(f.a)(t):E(t)};var O=function(t,e){return t&&Object(p.a)(t,e,N)};var w=function(t,e,i,n){return O(t,(function(t,o,r){e(n,i(t),o,r)})),n};var v=function(t,e){return function(i,n){return w(i,t,e(n),{})}},Q=i(53),x=Object.prototype.toString,B=v((function(t,e,i){null!=e&&"function"!=typeof e.toString&&(e=x.call(e)),t[e]=i}),Object(C.a)(Q.a)),L=i(105),S=i(129),k=i(60);class T extends h.a{constructor(){super(...arguments),this.focusIndex=-1,this.handleFocusOut=t=>{if(!this.contains(t.relatedTarget)){const t=this.menuItems.findIndex(this.isFocusableElement);this.menuItems[this.focusIndex].setAttribute("tabindex",""),this.menuItems[t].setAttribute("tabindex","0"),this.focusIndex=t}},this.setItems=()=>{const t=this.menuItems.findIndex(this.isFocusableElement);-1!==t&&(this.focusIndex=t);for(let e=0;e<this.menuItems.length;e++)e===t&&this.menuItems[e].setAttribute("tabindex","0"),this.menuItems[e].addEventListener("blur",this.handleMenuItemFocus)},this.resetItems=t=>{for(let e=0;e<t.length;e++)t[e].removeEventListener("blur",this.handleMenuItemFocus)},this.isMenuItemElement=t=>Object(L.b)(t)&&T.focusableElementRoles.hasOwnProperty(t.getAttribute("role")),this.isDisabledElement=t=>this.isMenuItemElement(t)&&"true"===t.getAttribute("aria-disabled"),this.isFocusableElement=t=>this.isMenuItemElement(t)&&!this.isDisabledElement(t),this.handleMenuItemFocus=t=>{const e=t.currentTarget,i=this.menuItems.indexOf(e);this.isDisabledElement(e)?e.blur():i!==this.focusIndex&&-1!==i&&this.setFocus(i,i>this.focusIndex?1:-1)}}itemsChanged(t,e){this.$fastController.isConnected&&(this.menuItems=this.domChildren(),this.resetItems(t),this.setItems())}connectedCallback(){super.connectedCallback(),this.menuItems=this.domChildren()}disconnectedCallback(){super.disconnectedCallback(),this.menuItems=[]}focus(){this.setFocus(0,1)}handleMenuKeyDown(t){if(!t.defaultPrevented)switch(t.keyCode){case S.a:case S.c:return void this.setFocus(this.focusIndex+1,1);case S.d:case S.b:return void this.setFocus(this.focusIndex-1,-1);case S.e:return void this.setFocus(this.domChildren().length-1,-1);case S.g:return void this.setFocus(0,1);default:return!0}}domChildren(){return Array.from(this.children)}setFocus(t,e){const i=this.menuItems;for(;Object(M.a)(t,i.length);){const n=i[t];if(this.isFocusableElement(n)){n.setAttribute("tabindex","0"),n.focus(),i[this.focusIndex].setAttribute("tabindex",""),this.focusIndex=t;break}t+=e}}}T.focusableElementRoles=B(k.a),Object(d.a)([I.c],T.prototype,"items",void 0);var z=i(93),F=i(139);const U=z.a`
    <template
        role="menu"
        @keydown="${(t,e)=>t.handleMenuKeyDown(e.event)}"
        @focusout="${(t,e)=>t.handleFocusOut(e.event)}"
    >
        <slot ${Object(F.a)("items")}></slot>
    </template>
`;var Y=i(30),H=i(103),R=i(11),P=i(137);const G=Y.b`
  ${Object(H.a)("block")} :host {
    --elevation: 11;
    background: ${R.w.var};
    border: calc(var(--outline-width) * 1px) solid transparent;
    border-radius: var(--elevated-corner-radius);
    ${P.a}
    margin: 0;
    border-radius: calc(var(--corner-radius) * 1px);
    padding: calc(var(--design-unit) * 1px) 0;
    max-width: 368px;
    min-width: 64px;
  }

  ::slotted(hr) {
    box-sizing: content-box;
    height: 0;
    margin: 0;
    border: none;
    border-top: calc(var(--outline-width) * 1px) solid var(--neutral-divider-rest);
  }
`.withBehaviors(R.w,R.h);let K=class extends T{};K=Object(g.a)([Object(h.b)({name:"fluent-menu",template:U,styles:G,shadowOptions:{mode:"closed"}})],K);var J=i(120),q=i(18),Z=i(31),W=i(4),V=i(57),X=i(74),$='a[href], button:enabled, textarea, input[type="text"], input[type="password"], div[role="slider"], input[type="radio"]:checked, input[type="checkbox"]:enabled, select:not([style*="display: none"]), details, msft-shopping-coupon, msft-shopping-link, fluent-button:not([slot="header-actions"]), fluent-anchor',_=function(){function t(){var e=this;o()(this,t),c()(this,"container",void 0),c()(this,"currentFocusElement",void 0),c()(this,"initialize",(function(t){e.container=t,e.container.addEventListener("keydown",e.trapFocus)})),c()(this,"trapFocus",(function(t){if("Tab"===t.key&&!t.ctrlKey&&e.container){var i=Array.prototype.slice.call(e.container.querySelectorAll('fluent-button[slot="header-actions"]')),n=Array.prototype.slice.call(e.container.querySelectorAll($));if(i.push.apply(i,n),i&&i.length>0){var o=i[0],r=i[i.length-1];document.activeElement===o&&t.shiftKey?(r.focus(),t.preventDefault()):document.activeElement!==r||t.shiftKey||(o.focus(),t.preventDefault())}}}))}return a()(t,[{key:"makeFocusOnActiveElement",value:function(t){var e=this;if(this.currentFocusElement!==document.activeElement){var i=t.querySelectorAll($);0!==i.length&&(i.forEach((function(t){document.activeElement!==t||(e.currentFocusElement=t)})),this.currentFocusElement&&this.currentFocusElement.focus())}}}],[{key:"getInstance",value:function(){return null===t.currentInstance&&(t.currentInstance=new t),t.currentInstance}}]),t}();c()(_,"currentInstance",null);var tt,et,it,nt,ot,rt,at,st,ct,lt,ut=_,At=i(25);function gt(t,e){var i;if("undefined"==typeof Symbol||null==t[Symbol.iterator]){if(Array.isArray(t)||(i=function(t,e){if(!t)return;if("string"==typeof t)return ht(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);"Object"===i&&t.constructor&&(i=t.constructor.name);if("Map"===i||"Set"===i)return Array.from(t);if("Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return ht(t,e)}(t))||e&&t&&"number"==typeof t.length){i&&(t=i);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var r,a=!0,s=!1;return{s:function(){i=t[Symbol.iterator]()},n:function(){var t=i.next();return a=t.done,t},e:function(t){s=!0,r=t},f:function(){try{a||null==i.return||i.return()}finally{if(s)throw r}}}}function ht(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=new Array(e);i<e;i++)n[i]=t[i];return n}i.d(e,"i",(function(){return tt})),i.d(e,"a",(function(){return et})),i.d(e,"c",(function(){return it})),i.d(e,"b",(function(){return nt})),i.d(e,"j",(function(){return ot})),i.d(e,"h",(function(){return rt})),i.d(e,"e",(function(){return at})),i.d(e,"d",(function(){return st})),i.d(e,"g",(function(){return ct})),i.d(e,"f",(function(){return lt})),l.a,u.a,A.a,J.a,function(t){t.Large="txtLarge",t.Medium="txtMedium",t.Small="txtSmall",t.EmptyCoupon="txtEmptyCoupons"}(tt||(tt={})),function(t){t.Title="colorTitle",t.Content="colorContent",t.Link="colorLink",t.EmptyCoupon="colorEmptyCoupons"}(et||(et={})),function(t){t[t.coupons=2]="coupons",t[t.priceComparison=3]="priceComparison",t[t.deals=2]="deals"}(it||(it={})),function(t){t[t.Desktop=1]="Desktop",t[t.Mobile=2]="Mobile"}(nt||(nt={})),function(t){t[t.Dark=1]="Dark",t[t.Light=2]="Light"}(ot||(ot={})),function(t){t[t.LTR=1]="LTR",t[t.RTL=2]="RTL"}(rt||(rt={})),function(t){t.Mouse="mouse",t.Keyboard="keyboard",t.Auto="auto"}(at||(at={})),function(t){t.couponSectionPluralTitle="couponSectionPluralTitle",t.foundComparisons="foundComparisons",t.privacyConsent="privacyConsent",t.savingsHeader="savingsHeader",t.sectionSeeMore="sectionSeeMore",t.FREFooter="FREFooter"}(st||(st={})),function(t){t[t.COUPONS_COPIED=0]="COUPONS_COPIED",t[t.DISMISSED=1]="DISMISSED",t[t.FRE_DISMISSED=2]="FRE_DISMISSED",t[t.FRE_GOT_IT=3]="FRE_GOT_IT",t[t.FRE_MANAGE=4]="FRE_MANAGE",t[t.FRE_NO_THANKS=5]="FRE_NO_THANKS",t[t.FRE_SETTINGS=6]="FRE_SETTINGS",t[t.MANAGE=7]="MANAGE",t[t.NAVIGATE_TO_RETAILER=8]="NAVIGATE_TO_RETAILER",t[t.TRY_ALL_COUPONS=9]="TRY_ALL_COUPONS",t[t.TURN_OFF_AUTO_SHOW=10]="TURN_OFF_AUTO_SHOW",t[t.TURN_ON_AUTO_SHOW=11]="TURN_ON_AUTO_SHOW",t[t.VIEW_MORE_COUPONS=12]="VIEW_MORE_COUPONS",t[t.VIEW_MORE_RETAILERS=13]="VIEW_MORE_RETAILERS"}(ct||(ct={})),function(t){t.closeButton="closeButton",t.disableAutoOpen="disableAutoOpen",t.enableAutoOpen="enableAutoOpen",t.menuIcon="menuIcon",t.settings="settings",t.couponsFoundDark="couponsFoundDark",t.shoppingImage="shoppingImage",t.headerIcon="headerIcon"}(lt||(lt={}));var dt=function(){function t(){o()(this,t),c()(this,"resources",{closeButton:'<svg id="flyoutClose" width="10" height="10" viewBox="0 0 10 10" xmlns="http://www.w3.org/2000/svg">\n        <path d="M5.33008 5L9.37305 9.04883L8.8457 9.57617L4.79688 5.5332L0.748047 9.57617L0.220703 9.04883L4.26367 5L0.220703 0.951172L0.748047 0.423828L4.79688 4.4668L8.8457 0.423828L9.37305 0.951172L5.33008 5Z"/>\n        </svg>',disableAutoOpen:'<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16">\n        <path d="M8 0C12.4183 0 16 3.58172 16 8C16 12.4183 12.4183 16 8 16C3.58172 16 0 12.4183 0 8C0 3.58172 3.58172 0 8 0ZM8 1.2C4.24446 1.2 1.2 4.24446 1.2 8C1.2 11.7555 4.24446 14.8 8 14.8C11.7555 14.8 14.8 11.7555 14.8 8C14.8 4.24446 11.7555 1.2 8 1.2ZM4.59944 7.39944H11.3994C11.7308 7.39944 11.9994 7.66807 11.9994 7.99944C11.9994 8.30319 11.7737 8.55423 11.4809 8.59396L11.3994 8.59944H4.59944C4.26807 8.59944 3.99944 8.33081 3.99944 7.99944C3.99944 7.69568 4.22516 7.44464 4.51802 7.40491L4.59944 7.39944H11.3994H4.59944Z"/>\n        </svg>',enableAutoOpen:'<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16">\n        <path d="M8 0C12.4183 0 16 3.58172 16 8C16 12.4183 12.4183 16 8 16C3.58172 16 0 12.4183 0 8C0 3.58172 3.58172 0 8 0ZM8 1.2C4.24446 1.2 1.2 4.24446 1.2 8C1.2 11.7555 4.24446 14.8 8 14.8C11.7555 14.8 14.8 11.7555 14.8 8C14.8 4.24446 11.7555 1.2 8 1.2ZM7 9.15147L10.5757 5.57574C10.8101 5.34142 11.1899 5.34142 11.4243 5.57574C11.6373 5.78875 11.6566 6.12208 11.4824 6.35697L11.4243 6.42426L7.42426 10.4243C7.21125 10.6373 6.87792 10.6566 6.64303 10.4824L6.57574 10.4243L4.57574 8.42426C4.34142 8.18995 4.34142 7.81005 4.57574 7.57574C4.78875 7.36272 5.12208 7.34336 5.35697 7.51764L5.42426 7.57574L7 9.15147L10.5757 5.57574L7 9.15147Z"/>\n        </svg>',headerIcon:'<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">\n        <path d="M14.4407 11.9085C14.2304 12.0187 14.0107 12.1173 13.7824 12.2031C13.0961 12.4609 12.3381 12.6071 11.5386 12.6071C8.58104 12.6071 6.00467 10.5726 6.00467 7.96186C6.00467 7.22779 6.4294 6.59148 7.03128 6.25635C4.35654 6.36847 3.66895 9.15637 3.66895 10.7895C3.66895 15.4066 7.92454 15.8752 8.84155 15.8752C9.33568 15.8752 10.0816 15.7312 10.5309 15.5903C10.5584 15.5812 10.5856 15.572 10.6129 15.5626C12.3478 14.9642 13.8122 13.7871 14.7747 12.2613C14.9127 12.0425 14.67 11.7883 14.4407 11.9085Z" fill="url(#paint0_linear)"/>\n        <path opacity="0.35" d="M14.4407 11.9085C14.2304 12.0187 14.0107 12.1173 13.7824 12.2031C13.0961 12.4609 12.3381 12.6071 11.5386 12.6071C8.58104 12.6071 6.00467 10.5726 6.00467 7.96186C6.00467 7.22779 6.4294 6.59148 7.03128 6.25635C4.35654 6.36847 3.66895 9.15637 3.66895 10.7895C3.66895 15.4066 7.92454 15.8752 8.84155 15.8752C9.33568 15.8752 10.0816 15.7312 10.5309 15.5903C10.5584 15.5812 10.5856 15.572 10.6129 15.5626C12.3478 14.9642 13.8122 13.7871 14.7747 12.2613C14.9127 12.0425 14.67 11.7883 14.4407 11.9085Z" fill="url(#paint1_radial)"/>\n        <path d="M6.60699 15.0887C6.0511 14.7441 5.56853 14.2897 5.18603 13.7527C4.59415 12.9217 4.2403 11.8995 4.2403 10.7897C4.2403 9.15655 5.00079 7.70997 6.17302 6.79347C6.43924 6.58534 6.78012 6.3754 7.03131 6.25652C7.22631 6.16418 7.55988 5.99792 8.00288 6.0042C8.73739 6.01462 9.29233 6.38909 9.60821 6.81434C9.85096 7.14109 10.0006 7.54366 10.0056 7.98066C10.0058 7.96754 11.5347 3.00537 5.00249 3.00537C2.25916 3.00537 0 5.60973 0 7.89385C0 9.34274 0.336211 10.4995 0.757169 11.3959C1.69471 13.3928 3.43331 14.9383 5.56007 15.6193C6.33024 15.8659 7.15075 16.0003 8.00288 16.0003C8.8867 16.0003 9.73608 15.8549 10.531 15.5905C10.0816 15.7314 9.60514 15.8086 9.11102 15.8086C8.19407 15.8086 7.33938 15.5427 6.60699 15.0887Z" fill="url(#paint2_linear)"/>\n        <path opacity="0.41" d="M6.60699 15.0887C6.0511 14.7441 5.56853 14.2897 5.18603 13.7527C4.59415 12.9217 4.2403 11.8995 4.2403 10.7897C4.2403 9.15655 5.00079 7.70997 6.17302 6.79347C6.43924 6.58534 6.78012 6.3754 7.03131 6.25652C7.22631 6.16418 7.55988 5.99792 8.00288 6.0042C8.73739 6.01462 9.29233 6.38909 9.60821 6.81434C9.85096 7.14109 10.0006 7.54366 10.0056 7.98066C10.0058 7.96754 11.5347 3.00537 5.00249 3.00537C2.25916 3.00537 0 5.60973 0 7.89385C0 9.34274 0.336211 10.4995 0.757169 11.3959C1.69471 13.3928 3.43331 14.9383 5.56007 15.6193C6.33024 15.8659 7.15075 16.0003 8.00288 16.0003C8.8867 16.0003 9.73608 15.8549 10.531 15.5905C10.0816 15.7314 9.60514 15.8086 9.11102 15.8086C8.19407 15.8086 7.33938 15.5427 6.60699 15.0887Z" fill="url(#paint3_radial)"/>\n        <path d="M9.52007 9.30364C9.46944 9.36927 9.31363 9.45977 9.31363 9.65765C9.31363 9.82046 9.41988 9.97746 9.6087 10.1093C10.5075 10.7365 12.2016 10.652 12.2062 10.652C12.9055 10.6496 13.5347 10.4573 14.0982 10.1278C14.3546 9.9779 14.5919 9.79983 14.8059 9.59652C15.5401 8.89883 15.9877 7.91563 16.0001 6.82318C16.0161 5.42222 15.5026 4.49083 15.2916 4.07842C13.967 1.48976 11.1078 0 7.99906 0C3.61686 0 0.0595379 3.52166 0.000732422 7.88757C0.0304402 5.60364 2.30036 3.75932 4.99864 3.75932C5.21742 3.75932 6.46504 3.78066 7.62399 4.38892C8.64487 4.92472 9.18013 5.57215 9.55213 6.21442C9.93845 6.88131 10.0071 7.72357 10.0071 8.05938C10.0071 8.3952 9.83782 8.89239 9.52007 9.30364Z" fill="url(#paint4_radial)"/>\n        <path d="M9.52007 9.30364C9.46944 9.36927 9.31363 9.45977 9.31363 9.65765C9.31363 9.82046 9.41988 9.97746 9.6087 10.1093C10.5075 10.7365 12.2016 10.652 12.2062 10.652C12.9055 10.6496 13.5347 10.4573 14.0982 10.1278C14.3546 9.9779 14.5919 9.79983 14.8059 9.59652C15.5401 8.89883 15.9877 7.91563 16.0001 6.82318C16.0161 5.42222 15.5026 4.49083 15.2916 4.07842C13.967 1.48976 11.1078 0 7.99906 0C3.61686 0 0.0595379 3.52166 0.000732422 7.88757C0.0304402 5.60364 2.30036 3.75932 4.99864 3.75932C5.21742 3.75932 6.46504 3.78066 7.62399 4.38892C8.64487 4.92472 9.18013 5.57215 9.55213 6.21442C9.93845 6.88131 10.0071 7.72357 10.0071 8.05938C10.0071 8.3952 9.83782 8.89239 9.52007 9.30364Z" fill="url(#paint5_radial)"/>\n        <defs>\n        <linearGradient id="paint0_linear" x1="3.66895" y1="11.0658" x2="14.815" y2="11.0658" gradientUnits="userSpaceOnUse">\n        <stop stop-color="#0C59A4"/>\n        <stop offset="1" stop-color="#114A8B"/>\n        </linearGradient>\n        <radialGradient id="paint1_radial" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(9.8253 11.149) scale(5.96154 5.66985)">\n        <stop offset="0.71659" stop-opacity="0"/>\n        <stop offset="0.9459" stop-opacity="0.53"/>\n        <stop offset="1"/>\n        </radialGradient>\n        <linearGradient id="paint2_linear" x1="9.54527" y1="6.23047" x2="2.58302" y2="13.814" gradientUnits="userSpaceOnUse">\n        <stop stop-color="#1B9DE2"/>\n        <stop offset="0.16164" stop-color="#1595DF"/>\n        <stop offset="0.66749" stop-color="#0680D7"/>\n        <stop offset="1" stop-color="#0078D4"/>\n        </linearGradient>\n        <radialGradient id="paint3_radial" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(4.45659 12.4168) rotate(-81.464) scale(8.95226 7.21381)">\n        <stop offset="0.76293" stop-opacity="0"/>\n        <stop offset="0.9459" stop-opacity="0.5"/>\n        <stop offset="1"/>\n        </radialGradient>\n        <radialGradient id="paint4_radial" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(1.57751 2.92536) rotate(92.129) scale(12.652 26.9468)">\n        <stop stop-color="#35C1F1"/>\n        <stop offset="0.11079" stop-color="#34C1ED"/>\n        <stop offset="0.23164" stop-color="#2FC2DF"/>\n        <stop offset="0.31446" stop-color="#2BC3D2"/>\n        <stop offset="0.67338" stop-color="#36C752"/>\n        </radialGradient>\n        <radialGradient id="paint5_radial" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(15.1549 4.84846) rotate(73.6747) scale(6.08366 4.96699)">\n        <stop stop-color="#66EB6E"/>\n        <stop offset="1" stop-color="#66EB6E" stop-opacity="0"/>\n        </radialGradient>\n        </defs>\n        </svg>',menuIcon:'<svg xmlns="http://www.w3.org/2000/svg" width="14" height="12" viewBox="0 0 14 12">\n        <path d="M1 5C1.14062 5 1.27083 5.02604 1.39062 5.07812C1.51042 5.13021 1.61458 5.20312 1.70312 5.29688C1.79688 5.38542 1.86979 5.48958 1.92188 5.60938C1.97396 5.72917 2 5.85938 2 6C2 6.14062 1.97396 6.27083 1.92188 6.39062C1.86979 6.51042 1.79688 6.61719 1.70312 6.71094C1.61458 6.79948 1.51042 6.86979 1.39062 6.92188C1.27083 6.97396 1.14062 7 1 7C0.859375 7 0.729167 6.97396 0.609375 6.92188C0.489583 6.86979 0.382812 6.79948 0.289062 6.71094C0.200521 6.61719 0.130208 6.51042 0.078125 6.39062C0.0260417 6.27083 0 6.14062 0 6C0 5.85938 0.0260417 5.72917 0.078125 5.60938C0.130208 5.48958 0.200521 5.38542 0.289062 5.29688C0.382812 5.20312 0.489583 5.13021 0.609375 5.07812C0.729167 5.02604 0.859375 5 1 5ZM7 5C7.14062 5 7.27083 5.02604 7.39062 5.07812C7.51042 5.13021 7.61458 5.20312 7.70312 5.29688C7.79688 5.38542 7.86979 5.48958 7.92188 5.60938C7.97396 5.72917 8 5.85938 8 6C8 6.14062 7.97396 6.27083 7.92188 6.39062C7.86979 6.51042 7.79688 6.61719 7.70312 6.71094C7.61458 6.79948 7.51042 6.86979 7.39062 6.92188C7.27083 6.97396 7.14062 7 7 7C6.85938 7 6.72917 6.97396 6.60938 6.92188C6.48958 6.86979 6.38281 6.79948 6.28906 6.71094C6.20052 6.61719 6.13021 6.51042 6.07812 6.39062C6.02604 6.27083 6 6.14062 6 6C6 5.85938 6.02604 5.72917 6.07812 5.60938C6.13021 5.48958 6.20052 5.38542 6.28906 5.29688C6.38281 5.20312 6.48958 5.13021 6.60938 5.07812C6.72917 5.02604 6.85938 5 7 5ZM13 5C13.1406 5 13.2708 5.02604 13.3906 5.07812C13.5104 5.13021 13.6146 5.20312 13.7031 5.29688C13.7969 5.38542 13.8698 5.48958 13.9219 5.60938C13.974 5.72917 14 5.85938 14 6C14 6.14062 13.974 6.27083 13.9219 6.39062C13.8698 6.51042 13.7969 6.61719 13.7031 6.71094C13.6146 6.79948 13.5104 6.86979 13.3906 6.92188C13.2708 6.97396 13.1406 7 13 7C12.8594 7 12.7292 6.97396 12.6094 6.92188C12.4896 6.86979 12.3828 6.79948 12.2891 6.71094C12.2005 6.61719 12.1302 6.51042 12.0781 6.39062C12.026 6.27083 12 6.14062 12 6C12 5.85938 12.026 5.72917 12.0781 5.60938C12.1302 5.48958 12.2005 5.38542 12.2891 5.29688C12.3828 5.20312 12.4896 5.13021 12.6094 5.07812C12.7292 5.02604 12.8594 5 13 5Z"/>\n        </svg>',settings:'<svg xmlns="http://www.w3.org/2000/svg" width="16" height="17" viewBox="0 0 16 17">\n        <path d="M7.81044 0C8.42209 0.00704958 9.03134 0.0777185 9.62836 0.210865C9.88898 0.268988 10.0839 0.486259 10.1135 0.751636L10.2553 2.02401C10.3195 2.60822 10.8128 3.0507 11.4008 3.05132C11.5589 3.05156 11.7152 3.01865 11.8613 2.95403L13.0285 2.4413C13.2712 2.33466 13.5548 2.3928 13.7361 2.58635C14.5796 3.4872 15.2077 4.56762 15.5734 5.74632C15.6522 6.00048 15.5615 6.27669 15.3473 6.43459L14.3127 7.19716C14.0176 7.41399 13.8433 7.75834 13.8433 8.12455C13.8433 8.49076 14.0176 8.8351 14.3133 9.05241L15.3488 9.81525C15.5631 9.97311 15.6539 10.2494 15.575 10.5036C15.2096 11.6821 14.5817 12.7624 13.7387 13.6634C13.5577 13.8569 13.2743 13.9152 13.0315 13.8089L11.8596 13.2954C11.5243 13.1487 11.1393 13.1702 10.8224 13.3533C10.5056 13.5364 10.2947 13.8593 10.2544 14.2231L10.1135 15.4953C10.0845 15.7577 9.89375 15.9735 9.63698 16.0346C8.42994 16.3218 7.17236 16.3218 5.96532 16.0346C5.70855 15.9735 5.51783 15.7577 5.48878 15.4953L5.34809 14.225C5.30677 13.8619 5.09558 13.54 4.77898 13.3575C4.46238 13.175 4.07796 13.1536 3.74379 13.2995L2.57161 13.813C2.32881 13.9194 2.04532 13.8611 1.86428 13.6674C1.02081 12.7654 0.392955 11.6838 0.0279603 10.504C-0.0506552 10.2499 0.0401776 9.97385 0.254331 9.81608L1.29044 9.05277C1.58556 8.83594 1.75986 8.49159 1.75986 8.12538C1.75986 7.75917 1.58556 7.41483 1.29006 7.19771L0.254594 6.43571C0.0401229 6.27787 -0.0507935 6.00149 0.0280992 5.74715C0.393725 4.56846 1.02191 3.48804 1.8654 2.58718C2.04662 2.39363 2.33024 2.33549 2.573 2.44213L3.73998 2.95477C4.07576 3.10213 4.46179 3.07987 4.7801 2.89391C5.09704 2.71007 5.30803 2.38685 5.34877 2.02304L5.4905 0.751636C5.52009 0.486125 5.71518 0.268784 5.97596 0.210785C6.57369 0.0778498 7.18359 0.00721143 7.81044 0ZM7.81059 1.24992C7.43223 1.25437 7.05492 1.28702 6.68181 1.34751L6.59104 2.16182C6.50618 2.91973 6.06699 3.59252 5.40898 3.97419C4.74694 4.36098 3.93976 4.40752 3.23745 4.09931L2.48887 3.77047C2.01226 4.34894 1.63291 5.00113 1.36573 5.7014L2.03056 6.19066C2.64623 6.64301 3.00986 7.36139 3.00986 8.12538C3.00986 8.88937 2.64623 9.60775 2.03121 10.0596L1.36533 10.5502C1.63228 11.2517 2.01169 11.9051 2.48863 12.4847L3.2429 12.1542C3.94131 11.8493 4.743 11.8939 5.40323 12.2745C6.06347 12.6551 6.5039 13.3265 6.59029 14.0855L6.68109 14.9054C7.42246 15.0315 8.17984 15.0315 8.92121 14.9054L9.01201 14.0856C9.096 13.3267 9.53592 12.6531 10.1969 12.271C10.858 11.889 11.6613 11.8442 12.361 12.1504L13.1146 12.4806C13.5911 11.9019 13.9704 11.2495 14.2375 10.5491L13.5726 10.0593C12.9569 9.60692 12.5933 8.88854 12.5933 8.12455C12.5933 7.36056 12.9569 6.64218 13.5718 6.19039L14.2359 5.70091C13.9687 5.00051 13.5893 4.3482 13.1126 3.76963L12.3655 4.09781C12.0611 4.23251 11.7318 4.30184 11.3992 4.30132C10.1744 4.30003 9.14657 3.37796 9.0129 2.16152L8.92213 1.34725C8.55085 1.28683 8.17749 1.25427 7.81059 1.24992ZM7.80004 4.99996C9.52593 4.99996 10.925 6.39907 10.925 8.12496C10.925 9.85085 9.52593 11.25 7.80004 11.25C6.07415 11.25 4.67504 9.85085 4.67504 8.12496C4.67504 6.39907 6.07415 4.99996 7.80004 4.99996ZM7.80004 6.24996C6.76451 6.24996 5.92504 7.08943 5.92504 8.12496C5.92504 9.16049 6.76451 9.99996 7.80004 9.99996C8.83557 9.99996 9.67504 9.16049 9.67504 8.12496C9.67504 7.08943 8.83557 6.24996 7.80004 6.24996Z"/>\n        </svg>'}),c()(this,"styles","\n        ::part(header-title) {\n            font-weight: 600;\n        }\n        #divContentWrapper {\n            width: 360px;\n            height: auto;\n        }\n        body {\n            margin: 0px;\n        }\n        .hide {\n            display: none;\n        }\n        msft-edge-shopping {\n            font-family: 'Segoe UI','Roboto', Helvetica, Verdana, sans-serif;\n            height: auto;\n            max-height: 580px;\n        }\n        .divPages {\n            height: auto;\n            display: flex;\n            flex-direction: column;\n            justify-content: flex-start;\n            margin: 12px;\n        }\n        fluent-design-system-provider {\n            width: 360px;\n            margin: 0px;\n        }\n        .txtLarge {\n            font-family: 'Segoe UI','Roboto', Helvetica, Verdana, sans-serif;\n            font-size: 24px;\n            line-height: 28px;\n            margin: 0px;\n        }\n\n        .txtMedium {\n            font-family: 'Segoe UI','Roboto', Helvetica, Verdana, sans-serif;\n            font-size: 16px;\n            line-height: 20px;\n            margin: 0px;\n        }\n\n        .txtSmall {\n            font-family: 'Segoe UI','Roboto', Helvetica, Verdana, sans-serif;\n            font-size: 14px;\n            line-height: 16px;\n            margin: 0px;\n        }\n\n        .txtEmptyCoupons {\n            all: initial;\n            font-family: 'Segoe UI','Roboto', Helvetica, Verdana, sans-serif;\n            font-size: 18px;\n            line-height: 25px;\n            margin: 0px;\n            color: rgba(0, 0, 0, 0.83);\n        }\n        .defaultFontFamily {\n            font-family: 'Segoe UI','Roboto', Helvetica, Verdana, sans-serif;\n        }\n        .colorTitle {\n            color: #2C2C2C;\n        }\n\n        .colorContent {\n            color: #767676;\n        }\n\n        .colorLink {\n            cursor: pointer;\n            color: #0078D4;\n        }\n\n        .colorEmptyCoupons {\n            color: #A2A2A2;\n        }\n        #divFooter {\n            height: auto;\n            display: flex;\n            flex-direction: column;\n            justify-content: center;\n            margin: 12px;\n        }\n        msft-shopping-link::part(attribution) {\n            margin-top: 6px;\n        } "),c()(this,"mobileStyles","\n        #divContentWrapper {\n            width: auto;\n        }\n        msft-edge-shopping {\n            font-family: 'Segoe UI','Roboto', Helvetica, Verdana, sans-serif;\n            height: auto;\n            max-height: unset;\n        }\n        fluent-design-system-provider {\n            width: auto;\n            margin: 0px;\n        }\n        #mainImage {\n            width: auto;\n            max-width: 374px;\n            height: auto;\n        }")}return a()(t,[{key:"isExperimentActive",value:function(t,e){var i,n=gt(t);try{for(n.s();!(i=n.n()).done;){if(i.value.name===e)return!0}}catch(t){n.e(t)}finally{n.f()}return!1}},{key:"createPolicyForStaticResources",value:function(){var e=this;window.trustedTypes&&(t.svgStaticPolicy=window.trustedTypes.createPolicy("shopping-static",{createHTML:function(t){switch(t){case lt.closeButton:return e.resources.closeButton;case lt.disableAutoOpen:return e.resources.disableAutoOpen;case lt.enableAutoOpen:return e.resources.enableAutoOpen;case lt.menuIcon:return e.resources.menuIcon;case lt.settings:return e.resources.settings;case lt.couponsFoundDark:return V.a.resources.couponsFoundDark;case lt.shoppingImage:return X.a.resources.shoppingImage;case lt.headerIcon:return e.resources.headerIcon;default:throw new Error("Error at createPolicyForStaticResources()")}}}))}},{key:"addCommonStyles",value:function(e){if(e){var i=document.querySelector("fluent-design-system-provider");i&&(t.currentTheme===ot.Dark?i.setAttribute("background-color","#4a4a4a"):i.setAttribute("background-color","#FFFFFF"),t.currentDirection===rt.RTL?i.style.direction="rtl":i.style.direction="ltr",i.setAttribute("aria-label",t.localizedStrings.commonFixedTitle)),this.applyStyles(e,this.styles)}}},{key:"addMobileStyles",value:function(t){t&&this.applyStyles(t,this.mobileStyles)}},{key:"addHeaderIcon",value:function(e){var i=this.createElement("DIV");i.innerHTML=t.svgStaticPolicy.createHTML(lt.headerIcon),i.slot="header-icon",e.appendChild(i)}},{key:"createTextElement",value:function(t,e,i,n){var o=document.createElement("P");return o.innerText=t,n&&o.setAttribute("style",n),o.classList.add(e),o.classList.add(i),o}},{key:"createElement",value:function(t,e,i){var n=document.createElement(t);if(e)for(var o in e)e.hasOwnProperty(o)&&n.setAttribute(o,e[o]);return i&&(n.innerText=i),n}},{key:"getValueFromKey",value:function(e,i){return t.currentDevice!==nt.Desktop||t.testLocally?e===st.couponSectionPluralTitle?i+" coupons":e===st.foundComparisons?"We've found "+i+" comparisons!":e===st.privacyConsent?"I consent to the placement of cookies on my browser to facilitate billing with the coupon provider as per ":e===st.savingsHeader?"You can save "+i:e===st.sectionSeeMore?"See more ("+i+")":e===st.FREFooter?"You can turn this off anytime in "+i:"":loadTimeData.getStringF(e,i)}},{key:"createModuleStructure",value:function(e,i,n,o,r,a,s,c,l){if(t.currentDevice===nt.Desktop?e.accordion=o:e.accordion=!1,e.title=n,e.expanded=a,i.length>r){var u=document.createElement("fluent-button");u.slot="action",u.className+="action",u.appearance="lightweight",u.textContent=this.getValueFromKey(st.sectionSeeMore,i.length-r),e.appendChild(u),u&&u.addEventListener("click",(function(t){0===t.screenX&&0===t.screenY?c(i,e,s,!0,!0,l):c(i,e,s,!0,!1,l),u.remove(),q.a.queueUpdate((function(){var t=e.shadowRoot;if(t){var i=t.querySelector(".action");i&&(i.style.display="none")}}))}))}else q.a.queueUpdate((function(){var t=e.shadowRoot;if(t){var i=t.querySelector(".action");i&&(i.style.display="none")}}))}},{key:"initializeTrapFocus",value:function(t){ut.getInstance().initialize(t)}},{key:"createCookieConsent",value:function(e,i){if(e){var n=this.createElement("DIV",{id:"divCookies",style:"margin-bottom: 12px;"}),o="";t.currentTheme===ot.Dark&&(o="color: #FFFFFF");var r=new t,a=r.createElement("P");a.style.fontSize="12px",a.style.color=o,a.style.display="inline",a.textContent=this.getValueFromKey(st.privacyConsent,"");var s=document.createElement("fluent-anchor");s.id="aPrivacy",s.setAttribute("href","#"),s.className+=" txtSmall",s.style.fontSize="12px",s.textContent=t.localizedStrings.commonMicrosoftPolicy,s.setAttribute("appearance","hypertext"),s.style.display="inline";var c=r.createElement("P");c.style.fontSize="12px",c.style.color=o,c.style.display="inline",c.textContent=".",a.appendChild(s),a.appendChild(c),n.appendChild(a),s&&s.addEventListener("click",(function(){if(t.currentDevice===nt.Desktop)i&&i.navigateToPrivacyStatement();else{var e=window;t.isAndroidBridgeAvailable()?e.couponsUIWebViewBridge.visitURLNewTab("https://privacy.microsoft.com/en-us/privacystatement"):t.isIOSBridgeAvailable()&&e.webkit.messageHandlers.couponsUIWebViewBridgeVisitURLNewTab.postMessage("https://privacy.microsoft.com/en-us/privacystatement")}})),e.appendChild(n)}}},{key:"createContextMenu",value:function(e,i,n,o){var r=document.createElement("fluent-button");r.slot="header-actions",r.id="divMenuButton",r.ariaHaspopup="menu",r.ariaControls="divMenuContent",r.ariaLabel=t.localizedStrings.commonMoreOptionsLabel;var a=this.createElement("DIV");a.setAttribute("aria-hidden","true"),t.svgStaticPolicy&&(a.innerHTML=t.svgStaticPolicy.createHTML(lt.menuIcon)),r.appearance="stealth",r.appendChild(a);var s=document.createElement("fluent-menu");s.slot="header-actions",s.id="divMenuContent",s.setAttribute("aria-labelledby","divMenuButton"),this.addAutoShowItem(s,n,o);var c,l=document.createElement("fluent-menu-item"),u=document.createElement("DIV");t.svgStaticPolicy&&(u.innerHTML=t.svgStaticPolicy.createHTML(lt.settings)),u.slot="start",u.setAttribute("aria-hidden","true"),l.textContent=t.localizedStrings.commonContextMenuManage,l.className+="defaultFontFamily",l.appendChild(u),o&&l.addEventListener("change",(function(){if(loadTimeData.getValue("isFRE")){var t=new Z.a(W.d.FREManage);At.a.GetLogModule().logClientEvent(W.h.Information,W.j.ButtonClick,"FRE Menu Manage",t,o,ct.FRE_MANAGE)}else{var e=new Z.a(W.d.FlyoutManage);At.a.GetLogModule().logClientEvent(W.h.Information,W.j.ButtonClick,"Flyout Menu Manage",e,o,ct.MANAGE)}o.navigateToSettings()})),s.appendChild(l),c=t.currentDirection===rt.RTL?"35px":"50px",s.setAttribute("style","position: absolute; top: 40px; left: "+c+"; z-index: 100; right: 10px;");var A=this.createElement("DIV",{style:"display: flex; flex-direction: column;"}),g=this.createTextElement("Microsoft may earn an affiliate commission if you use coupon codes on this page",tt.Small,et.Content,"margin-left: 15px; margin-right: 15px; margin-bottom: 5px;"),h=this.createElement("HR",{style:"border: 1px solid #c4c4c4; border-bottom-style: hidden; margin-top: 2px; width: 100%;"});t.currentTheme===ot.Dark&&(g.style.color="#FFFFFF"),A.appendChild(h),A.appendChild(g),s.appendChild(A),s.tabIndex=-1,s.addEventListener("keydown",(function(t){"Escape"===t.key&&(r.style.backgroundColor="transparent",s.className="hide",r.ariaExpanded=void 0,r.focus(),t.preventDefault())})),r.addEventListener("keydown",(function(t){"Escape"===t.key&&"hide"!==s.className&&(r.style.backgroundColor="transparent",s.className="hide",r.ariaExpanded=void 0,r.focus(),t.preventDefault())})),s.className="hide",r.addEventListener("click",(function(t){if(e){if(s.classList.toggle("hide"),"hide"===s.className)r.style.backgroundColor="transparent",r.ariaExpanded="true";else{r.style.backgroundColor="rgba(0, 0, 0, 0.07)";var i=s.querySelectorAll("*")[0];i&&0===t.screenX&&0===t.screenY&&i.focus(),r.ariaExpanded=void 0}t.stopPropagation()}})),i.appendChild(s),i.appendChild(r),i.addEventListener("click",(function(){"hide"!==s.className&&(r.style.backgroundColor="transparent",s.className="hide",r.ariaExpanded=void 0)}))}},{key:"createCloseButton",value:function(e,i){var n=document.createElement("fluent-button");n.slot="header-actions",n.ariaLabel=t.localizedStrings.commonClose;var o=this.createElement("DIV");o.setAttribute("aria-hidden","true"),t.svgStaticPolicy&&(o.innerHTML=t.svgStaticPolicy.createHTML(lt.closeButton)),n.appearance="stealth",n.appendChild(o),n.addEventListener("click",(function(){if(i){if(loadTimeData.getValue("isFRE")){var t=new Z.a(W.d.FREDismiss);At.a.GetLogModule().logClientEvent(W.h.Information,W.j.ButtonClick,"FRE Dismissed",t,i,ct.FRE_DISMISSED)}else{var e=new Z.a(W.d.FlyoutDismiss);At.a.GetLogModule().logClientEvent(W.h.Information,W.j.ButtonClick,"Flyout Dismissed",e,i,ct.DISMISSED)}i.dismissPopup()}})),e.appendChild(n)}},{key:"applyStyles",value:function(t,e){var i=this.createElement("STYLE");i.textContent=e,i.type="text/css",i.rel="stylesheet",t.appendChild(i)}},{key:"addAutoShowItem",value:function(e,i,n){var o=document.createElement("fluent-menu-item"),r=document.createElement("DIV");i?(t.svgStaticPolicy&&(r.innerHTML=t.svgStaticPolicy.createHTML(lt.disableAutoOpen)),r.setAttribute("aria-hidden","true"),o.id="Enabled",o.className+="defaultFontFamily",o.textContent=t.localizedStrings.commonContextMenuAutoShowDisable):(t.svgStaticPolicy&&(r.innerHTML=t.svgStaticPolicy.createHTML(lt.enableAutoOpen)),r.setAttribute("aria-hidden","true"),o.id="Disabled",o.className+="defaultFontFamily",o.textContent=t.localizedStrings.commonContextMenuAutoShowEnable),r.slot="start",o.appendChild(r),o.addEventListener("change",(function(e){if("Enabled"===o.id){o.textContent=t.localizedStrings.commonContextMenuAutoShowEnable,o.id="Disabled";var i=document.createElement("DIV");if(i.slot="start",t.svgStaticPolicy&&(i.innerHTML=t.svgStaticPolicy.createHTML(lt.enableAutoOpen)),i.setAttribute("aria-hidden","true"),o.appendChild(i),n){var r=new Z.a(W.d.AutoShowTurnedOff);At.a.GetLogModule().logClientEvent(W.h.Information,W.j.ButtonClick,"Auto Show turned off",r,n,ct.TURN_OFF_AUTO_SHOW),n.allowAutoShow(!1)}}else{o.textContent=t.localizedStrings.commonContextMenuAutoShowDisable,o.id="Enabled";var a=document.createElement("DIV");if(a.slot="start",t.svgStaticPolicy&&(a.innerHTML=t.svgStaticPolicy.createHTML(lt.disableAutoOpen)),a.setAttribute("aria-hidden","true"),o.appendChild(a),n){var s=new Z.a(W.d.AutoShowTurnedOn);At.a.GetLogModule().logClientEvent(W.h.Information,W.j.ButtonClick,"Auto Show turned on",s,n,ct.TURN_ON_AUTO_SHOW),n.allowAutoShow(!0)}}e.stopPropagation()})),o.addEventListener("click",(function(t){t.stopPropagation()})),e.appendChild(o)}}],[{key:"isAndroidBridgeAvailable",value:function(){return!!window.couponsUIWebViewBridge}},{key:"isIOSBridgeAvailable",value:function(){var t=window;return!!t.webkit&&!!t.webkit.messageHandlers}}]),t}();c()(dt,"localizedStrings",void 0),c()(dt,"currentDevice",nt.Desktop),c()(dt,"currentTheme",ot.Light),c()(dt,"currentDirection",rt.LTR),c()(dt,"testLocally",!1),c()(dt,"svgStaticPolicy",void 0);e.k=dt},function(t,e,i){"use strict";var n,o,r,a,s,c,l,u,A,g,h,d,I,M,C;i.d(e,"m",(function(){return n})),i.d(e,"h",(function(){return a})),i.d(e,"j",(function(){return s})),i.d(e,"d",(function(){return c})),i.d(e,"f",(function(){return l})),i.d(e,"b",(function(){return u})),i.d(e,"e",(function(){return A})),i.d(e,"i",(function(){return g})),i.d(e,"g",(function(){return d})),i.d(e,"k",(function(){return I})),i.d(e,"l",(function(){return M})),i.d(e,"a",(function(){return C})),i.d(e,"c",(function(){return p})),function(t){t[t.Domains=0]="Domains",t[t.Deals=1]="Deals",t[t.Popup=2]="Popup",t[t.Overlay=3]="Overlay",t[t.BaseUXUrl=4]="BaseUXUrl",t[t.EventLogUrl=5]="EventLogUrl",t[t.PriceComparison=6]="PriceComparison",t[t.PriceNotification=7]="PriceNotification",t[t.ImageResize=8]="ImageResize",t[t.EdgeMobileOverlay=9]="EdgeMobileOverlay"}(n||(n={})),function(t){t.Get="GET",t.Post="POST"}(o||(o={})),function(t){t.Json="application/json",t.Text="text/plain"}(r||(r={})),function(t){t.Trace="Trace",t.Debug="Debug",t.Information="Information",t.Warning="Warning",t.Error="Error",t.Critical="Critical",t.None="None"}(a||(a={})),function(t){t.AutoApply="AutoApply",t.ExtentionInstallation="ExtentionInstallation",t.ButtonClick="ButtonClick",t.ClientError="ClientError",t.DebugMessage="DebugMessage",t.EdgeFlyoutStatus="EdgeFlyoutStatus",t.CheckoutPageValidation="CheckoutPageValidation"}(s||(s={})),function(t){t.AutoApply="AutoApply",t.PopupClose="PopupClose",t.CouponCopied="CouponCopied",t.SeeMoreCoupons="SeeMoreCoupons",t.SeeMoreRetailers="SeeMoreRetailers",t.FREManage="FREManage",t.FlyoutManage="FlyoutManage",t.FREDismiss="FREDismiss",t.FlyoutDismiss="FlyoutDismiss",t.AutoShowTurnedOff="AutoShowTurnedOff",t.AutoShowTurnedOn="AutoShowTurnedOn",t.FREGotIt="FREGotIt",t.FRENoThanks="FRENoThanks",t.NavigatedToAnotherRetailer="NavigatedToAnotherRetailer",t.ProceedToCheckoutWithImport="ProceedToCheckoutWithImport",t.ProceedToCheckoutWithOutImport="ProceedToCheckoutWithOutImport",t.ProceedToCheckout="ProceedToCheckout"}(c||(c={})),function(t){t.OpenedInCheckout="OpenedInCheckout",t.EmptyCoupons="EmptyCoupons",t.AutoTrigger="AutoTrigger",t.UserTrigger="UserTrigger",t.OnErrorInvoked="OnErrorInvoked",t.BestPrice="BestPrice",t.YouCanSave="YouCanSave"}(l||(l={})),function(t){t.Error="Error",t.Negative="Negative",t.Positive="Positive",t.Cancelled="Cancelled"}(u||(u={})),function(t){t.Valid="Valid",t.NotApplicable="NotApplicable"}(A||(A={})),function(t){t[t.ApplyCouponsMessage=1]="ApplyCouponsMessage",t[t.AutoApplyCompletedMessage=2]="AutoApplyCompletedMessage",t[t.CheckoutPageMessage=3]="CheckoutPageMessage",t[t.CloseOverlayMessage=4]="CloseOverlayMessage",t[t.ClosedPopupMessage=5]="ClosedPopupMessage",t[t.CountElementsResponse=6]="CountElementsResponse",t[t.CreateOverlayMessage=7]="CreateOverlayMessage",t[t.CreatePopupMessage=8]="CreatePopupMessage",t[t.CreatePriceComparisonMessage=9]="CreatePriceComparisonMessage",t[t.EdgeApplyCouponsMessage=10]="EdgeApplyCouponsMessage",t[t.EmptyResponse=11]="EmptyResponse",t[t.ErrorResponse=12]="ErrorResponse",t[t.GetCompetingPricesMessage=13]="GetCompetingPricesMessage",t[t.GetCompetingPricesResponse=14]="GetCompetingPricesResponse",t[t.GetTextValueResponse=15]="GetTextValueResponse",t[t.GetUIStateMessage=16]="GetUIStateMessage",t[t.GetUIStateResponse=17]="GetUIStateResponse",t[t.MoveProgressBarMessage=18]="MoveProgressBarMessage",t[t.ShowResultMessage=19]="ShowResultMessage",t[t.TelemetryMessage=20]="TelemetryMessage",t[t.UpdateCurrentCouponMessage=21]="UpdateCurrentCouponMessage",t[t.OpalMessage=22]="OpalMessage",t[t.ProductPageMessage=23]="ProductPageMessage",t[t.ProductPageResponse=24]="ProductPageResponse",t[t.EdgeCreateOverlayMessage=25]="EdgeCreateOverlayMessage",t[t.EdgeShowResultMessage=26]="EdgeShowResultMessage",t[t.VerifySelectorsMessage=27]="VerifySelectorsMessage",t[t.VerifySelectorsResponse=28]="VerifySelectorsResponse",t[t.AutoApplyCancelledMessage=29]="AutoApplyCancelledMessage",t[t.ContentInitMessage=30]="ContentInitMessage",t[t.ClearDataMessage=31]="ClearDataMessage",t[t.DOMMutationMessage=32]="DOMMutationMessage",t[t.UpdateCurrencyMessage=33]="UpdateCurrencyMessage"}(g||(g={})),function(t){t[t.IOS=0]="IOS",t[t.Android=1]="Android"}(h||(h={})),function(t){t[t.PlainString=0]="PlainString",t[t.JsonString=1]="JsonString"}(d||(d={})),function(t){t[t.FromBackground=1]="FromBackground",t[t.FromContent=2]="FromContent"}(I||(I={})),function(t){t.HandleBackgroundMessage="HandleBackgroundMessage",t.HandleContentMessage="HandleContentMessage"}(M||(M={})),function(t){t.Opal="Opal",t.Extension="Extension",t.Edge="Edge",t.EdgeMobile="EdgeMobile",t.Sapphire="Sapphire"}(C||(C={}));var p="autoApplyRuntime"},function(t,e,i){"use strict";i.d(e,"a",(function(){return n}));function n(t,e,i,n){var o,r=arguments.length,a=r<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,i):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,i,n);else for(var s=t.length-1;s>=0;s--)(o=t[s])&&(a=(r<3?o(a):r>3?o(e,i,a):o(e,i))||a);return r>3&&a&&Object.defineProperty(e,i,a),a}},function(t,e,i){"use strict";i.d(e,"c",(function(){return s})),i.d(e,"b",(function(){return c})),i.d(e,"e",(function(){return l})),i.d(e,"d",(function(){return u})),i.d(e,"i",(function(){return A})),i.d(e,"h",(function(){return g})),i.d(e,"g",(function(){return h})),i.d(e,"a",(function(){return d})),i.d(e,"f",(function(){return I}));var n,o=i(27),r=i(2),a=i(1);function s(t,e){return i=>{if(!Object(a.g)(e))return-1;const n=Object(o.a)(t,i),r=n.indexOf(e);return-1!==r?r:n.findIndex(t=>Object(a.g)(t)&&Object(a.c)(e,t))}}function c(t,e){return i=>{const n=Object(o.a)(t,i),r=Object(o.a)(e,i),c=s(n,r)(i);let l;if(-1!==c)return c;try{l=Object(a.h)(r)}catch(t){l=-1}return-1===l?0:n.map((t,e)=>({luminance:Object(a.h)(t),index:e})).reduce((t,e)=>Math.abs(e.luminance-l)<Math.abs(t.luminance-l)?e:t).index}}function l(t){return Object(a.h)(Object(r.k)(t))<=(-.1+Math.sqrt(.21))/2}function u(t,e){return"function"==typeof t?i=>e(i)[Object(a.b)(t(i),0,e(i).length-1)]:e[Object(a.b)(t,0,e.length-1)]}function A(t){return(e,i)=>n=>u(l(n)?Object(o.a)(i,n):Object(o.a)(e,n),t(n))}function g(t){return e=>i=>n=>r=>s=>{const c=Object(o.a)(t,s),l=Object(o.a)(e,s),u=l.length,A=Object(a.b)(i(c,l,s),0,u-1),g=n(A,l,s);const h=[].concat(l),d=u-1;let I=A;return-1===g&&(h.reverse(),I=d-I),function t(e,i,n=0,o=e.length-1){if(o===n)return e[n];const r=Math.floor((o-n)/2)+n;return i(e[r])?t(e,i,n,r):t(e,i,r+1,o)}(h,(function(t){return r(Object(a.e)(c,t))}),I,d)}}function h(t,e,i){return c(e,t)(i)}function d(t){return c(r.O,Object(r.k)(t))(t)}function I(t){return e=>e>=t}!function(t){t.neutral="neutral",t.accent="accent"}(n||(n={}))},function(t,e){t.exports=function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}},function(t,e,i){"use strict";var n=i(5),o=i(30),r=i(21),a=i(131),s=i(27),c=i(130),l=i(9),u=i(110),A=i(24),g=i(18);class h{constructor(){this.queue=new Set,this.customPropertyTarget=null,this._owner=null,this.ticking=!1,this.cssCustomPropertyDefinitions=new Map}get owner(){return this._owner}register(t){const e=this.cssCustomPropertyDefinitions.get(t.name);e?e.count+=1:(this.cssCustomPropertyDefinitions.set(t.name,Object.assign(Object.assign({},t),{count:1})),this.set(t))}unregister(t){const e=this.cssCustomPropertyDefinitions.get(t);e&&(e.count-=1,0===e.count&&(this.cssCustomPropertyDefinitions.delete(t),this.remove(t)))}set(t){this.owner&&(this.customPropertyTarget?this.customPropertyTarget.setProperty("--"+t.name,this.owner.evaluate(t)):this.queue.add(this.set.bind(this,t)))}remove(t){this.customPropertyTarget?this.customPropertyTarget.removeProperty("--"+t):this.queue.add(this.remove.bind(this,t))}setAll(){this.ticking||(this.ticking=!0,g.a.queueUpdate(()=>{this.ticking=!1,this.cssCustomPropertyDefinitions.forEach(t=>{this.set(t)})}))}}class d extends h{constructor(t){super(),this.subscribers=new Set,this.sheet=t,this.styles=o.a.create([t]),this.customPropertyTarget=t.cssRules[t.insertRule(":host{}")].style}subscribe(t){this.subscribers.add(t),1===this.subscribers.size&&(this._owner=t),t.cssCustomPropertyDefinitions.forEach(t=>{this.register(t)}),t.$fastController.addStyles(this.styles)}unsubscribe(t){this.subscribers.delete(t),t.cssCustomPropertyDefinitions.forEach(t=>this.unregister(t.name)),this.owner===t&&(this._owner=this.subscribers.size?this.subscribers.values().next().value:null),!this.sheet.ownerNode&&this.styles&&t.$fastController.removeStyles(this.styles)}isSubscribed(t){return this.subscribers.has(t)}}class I extends h{constructor(t,e){super(),this._sheet=null,this.handleConnection={handleChange:()=>{var t;this._sheet=this.styles.sheet;const e=this.sheet.insertRule(":host{}");this.customPropertyTarget=this.sheet.rules[e].style,A.a.getNotifier(null===(t=this._owner)||void 0===t?void 0:t.$fastController).unsubscribe(this.handleConnection,"isConnected")}};const i=e.$fastController;i.addStyles(t),this.styles=t,this._owner=e,e.isConnected?this.handleConnection.handleChange():A.a.getNotifier(i).subscribe(this.handleConnection,"isConnected"),e.cssCustomPropertyDefinitions.forEach(t=>{this.register(t)})}get sheet(){return this._sheet}customPropertyTargetChanged(t,e){!t&&this.queue.size&&(this.queue.forEach(t=>t()),this.queue.clear())}}function M(t){const e=t.parentElement;if(e)return e;{const e=t.getRootNode();if(e.host instanceof HTMLElement)return e.host}return null}Object(l.a)([A.c],I.prototype,"customPropertyTarget",void 0);const C="adoptedStyleSheets"in window.ShadowRoot.prototype;function p(t){const e=t.provider;return null!=e&&D.isDesignSystemProvider(e)}const f={bind(t){t.provider=D.findProvider(t)},unbind(t){}};class D extends u.a{constructor(){super(),this.isDesignSystemProvider=!0,this.designSystem={},this.useDefaults=!1,this.provider=null,this.cssCustomPropertyDefinitions=new Map,this.attributeChangeHandler={handleChange:(t,e)=>{const i=this[e],n=this.customPropertyManager;if(this.isValidDesignSystemValue(i)){this.designSystem[e]=i;const t=this.designSystemProperties[e];t&&t.cssCustomProperty&&n&&n.set({name:t.cssCustomProperty,value:i})}else{this.syncDesignSystemWithProvider();const t=this.designSystemProperties[e].cssCustomProperty;n&&("string"==typeof t&&n.remove(t),n.setAll())}}},this.localDesignSystemChangeHandler={handleChange:()=>{const t=this.customPropertyManager;t&&t.owner===this&&t.setAll()}},this.providerDesignSystemChangeHandler={handleChange:(t,e)=>{t[e]===this.designSystem[e]||this.isValidDesignSystemValue(this[e])||(this.designSystem[e]=t[e])}},this.customPropertyManager=C?new d(new CSSStyleSheet):new I(document.createElement("style"),this),this.$fastController.addBehaviors([f])}static get tagNames(){return D._tagNames}static isDesignSystemProvider(t){return t.isDesignSystemProvider||-1!==D.tagNames.indexOf(t.tagName)}static findProvider(t){if(p(t))return t.provider;let e=M(t);for(;null!==e;){if(D.isDesignSystemProvider(e))return t.provider=e,e;if(p(e))return t.provider=e.provider,e.provider;e=M(e)}return null}static registerTagName(t){const e=t.toUpperCase();-1===D.tagNames.indexOf(e)&&D._tagNames.push(e)}useDefaultsChanged(){if(this.useDefaults){const t=this.designSystemProperties;Object.keys(t).forEach(e=>{void 0===this[e]&&(this[e]=t[e].default)})}}providerChanged(t,e){if(t instanceof HTMLElement){const e=A.a.getNotifier(t.designSystem);A.a.getAccessors(t.designSystem).forEach(t=>{e.unsubscribe(this.providerDesignSystemChangeHandler,t.name)})}if(e instanceof HTMLElement&&D.isDesignSystemProvider(e)){const t=A.a.getNotifier(e.designSystem);A.a.getAccessors(e.designSystem).forEach(e=>{t.subscribe(this.providerDesignSystemChangeHandler,e.name)}),this.syncDesignSystemWithProvider()}}customPropertyManagerChanged(t,e){t&&t.unsubscribe&&t.unsubscribe(this),e.subscribe&&e.subscribe(this)}connectedCallback(){super.connectedCallback(),this.customPropertyManager.subscribe&&this.customPropertyManager.isSubscribed&&!this.customPropertyManager.isSubscribed(this)&&this.customPropertyManager.subscribe(this);const t=A.a.getNotifier(this),e=A.a.getNotifier(this.designSystem);if(Object.keys(this.designSystemProperties).forEach(i=>{Object(A.c)(this.designSystem,i),t.subscribe(this.attributeChangeHandler,i),e.subscribe(this.localDesignSystemChangeHandler,i);const n=this[i];if(this.isValidDesignSystemValue(n)){this.designSystem[i]=n;const{cssCustomProperty:t}=this.designSystemProperties[i];"string"==typeof t&&this.customPropertyManager&&this.customPropertyManager.owner===this&&this.customPropertyManager.set({name:t,value:this[i]})}}),Array.isArray(this.disconnectedCSSCustomPropertyRegistry)){for(let t=0;t<this.disconnectedCSSCustomPropertyRegistry.length;t++)this.registerCSSCustomProperty(this.disconnectedCSSCustomPropertyRegistry[t]);delete this.disconnectedCSSCustomPropertyRegistry}if(Array.isArray(this.disconnectedRegistry)){for(let t=0;t<this.disconnectedRegistry.length;t++)this.disconnectedRegistry[t](this);delete this.disconnectedRegistry}}disconnectedCallback(){super.disconnectedCallback(),this.customPropertyManager.unsubscribe&&this.customPropertyManager.unsubscribe(this)}registerCSSCustomProperty(t){this.cssCustomPropertyDefinitions.set(t.name,t),this.customPropertyManager.register(t)}unregisterCSSCustomProperty(t){this.cssCustomPropertyDefinitions.delete(t.name),this.customPropertyManager.unregister(t.name)}evaluate(t){return"function"==typeof t.value?t.value(Object.assign({},this.designSystem)):t.value}syncDesignSystemWithProvider(){if(this.provider){const t=A.a.getAccessors(this.designSystem).reduce((t,e)=>(t[e.name]=e,t),{});A.a.getAccessors(this.provider.designSystem).forEach(e=>{var i;this.designSystemProperties.hasOwnProperty(e.name)&&this.isValidDesignSystemValue(this[e.name])||!this.isValidDesignSystemValue(null===(i=this.provider)||void 0===i?void 0:i.designSystem[e.name])||(t[e.name]||A.a.defineProperty(this.designSystem,e.name),this.designSystem[e.name]=this.provider.designSystem[e.name])})}}isValidDesignSystemValue(t){return null!=t}}D._tagNames=[],Object(l.a)([Object(r.b)({attribute:"use-defaults",mode:"boolean"})],D.prototype,"useDefaults",void 0),Object(l.a)([A.c],D.prototype,"provider",void 0),Object(l.a)([A.c],D.prototype,"customPropertyManager",void 0);const y=function(t){return e=>{Object(u.b)(t)(e),e.registerTagName("string"==typeof t?t:t.name)}};function b(t){return(e,i)=>{((t,e,i)=>{const{cssCustomProperty:n,attribute:o}=i;t.designSystemProperties||(t.designSystemProperties={}),!1===o?Object(A.c)(t,e):(void 0===i.mode&&(i=Object.assign(Object.assign({},i),{mode:"fromView"})),Object(r.b)(i)(t,e)),t.designSystemProperties[e]={cssCustomProperty:!1!==n&&("string"==typeof n?n:"string"==typeof o?o:e),default:i.default}})(e,i,t)}}const j=i(93).a`
    <slot></slot>
`;var E=i(103);const m=o.b`
  ${Object(E.a)("block")};
`;i.d(e,"a",(function(){return w}));const N=new c.a("neutral-foreground-rest",a.b,t=>t),O=o.b`
  :host {
    background-color: var(--background-color);
    color: ${N.var};
  }
`.withBehaviors(N);let w=class extends D{constructor(){super(...arguments),this.noPaint=!1}noPaintChanged(){this.noPaint||void 0===this.backgroundColor?this.$fastController.removeStyles(O):this.$fastController.addStyles(O)}backgroundColorChanged(){this.noPaintChanged()}};Object(n.a)([Object(r.b)({attribute:"no-paint",mode:"boolean"})],w.prototype,"noPaint",void 0),Object(n.a)([b({attribute:"background-color",default:s.b.backgroundColor})],w.prototype,"backgroundColor",void 0),Object(n.a)([b({attribute:"accent-base-color",cssCustomProperty:!1,default:s.b.accentBaseColor})],w.prototype,"accentBaseColor",void 0),Object(n.a)([b({attribute:!1,cssCustomProperty:!1,default:s.b.neutralPalette})],w.prototype,"neutralPalette",void 0),Object(n.a)([b({attribute:!1,cssCustomProperty:!1,default:s.b.accentPalette})],w.prototype,"accentPalette",void 0),Object(n.a)([b({default:s.b.density,converter:r.c})],w.prototype,"density",void 0),Object(n.a)([b({attribute:"design-unit",converter:r.c,default:s.b.designUnit})],w.prototype,"designUnit",void 0),Object(n.a)([b({attribute:"direction",cssCustomProperty:!1,default:s.b.direction})],w.prototype,"direction",void 0),Object(n.a)([b({attribute:"base-height-multiplier",default:s.b.baseHeightMultiplier,converter:r.c})],w.prototype,"baseHeightMultiplier",void 0),Object(n.a)([b({attribute:"base-horizontal-spacing-multiplier",converter:r.c,default:s.b.baseHorizontalSpacingMultiplier})],w.prototype,"baseHorizontalSpacingMultiplier",void 0),Object(n.a)([b({attribute:"corner-radius",converter:r.c,default:s.b.cornerRadius})],w.prototype,"cornerRadius",void 0),Object(n.a)([b({attribute:"elevated-corner-radius",converter:r.c,default:s.b.elevatedCornerRadius})],w.prototype,"elevatedCornerRadius",void 0),Object(n.a)([b({attribute:"outline-width",converter:r.c,default:s.b.outlineWidth})],w.prototype,"outlineWidth",void 0),Object(n.a)([b({attribute:"focus-outline-width",converter:r.c,default:s.b.focusOutlineWidth})],w.prototype,"focusOutlineWidth",void 0),Object(n.a)([b({attribute:"disabled-opacity",converter:r.c,default:s.b.disabledOpacity})],w.prototype,"disabledOpacity",void 0),Object(n.a)([b({attribute:"type-ramp-minus-2-font-size",default:"10px"})],w.prototype,"typeRampMinus2FontSize",void 0),Object(n.a)([b({attribute:"type-ramp-minus-2-line-height",default:"16px"})],w.prototype,"typeRampMinus2LineHeight",void 0),Object(n.a)([b({attribute:"type-ramp-minus-1-font-size",default:"12px"})],w.prototype,"typeRampMinus1FontSize",void 0),Object(n.a)([b({attribute:"type-ramp-minus-1-line-height",default:"16px"})],w.prototype,"typeRampMinus1LineHeight",void 0),Object(n.a)([b({attribute:"type-ramp-base-font-size",default:"14px"})],w.prototype,"typeRampBaseFontSize",void 0),Object(n.a)([b({attribute:"type-ramp-base-line-height",default:"20px"})],w.prototype,"typeRampBaseLineHeight",void 0),Object(n.a)([b({attribute:"type-ramp-plus-1-font-size",default:"16px"})],w.prototype,"typeRampPlus1FontSize",void 0),Object(n.a)([b({attribute:"type-ramp-plus-1-line-height",default:"24px"})],w.prototype,"typeRampPlus1LineHeight",void 0),Object(n.a)([b({attribute:"type-ramp-plus-2-font-size",default:"20px"})],w.prototype,"typeRampPlus2FontSize",void 0),Object(n.a)([b({attribute:"type-ramp-plus-2-line-height",default:"28px"})],w.prototype,"typeRampPlus2LineHeight",void 0),Object(n.a)([b({attribute:"type-ramp-plus-3-font-size",default:"28px"})],w.prototype,"typeRampPlus3FontSize",void 0),Object(n.a)([b({attribute:"type-ramp-plus-3-line-height",default:"36px"})],w.prototype,"typeRampPlus3LineHeight",void 0),Object(n.a)([b({attribute:"type-ramp-plus-4-font-size",default:"34px"})],w.prototype,"typeRampPlus4FontSize",void 0),Object(n.a)([b({attribute:"type-ramp-plus-4-line-height",default:"44px"})],w.prototype,"typeRampPlus4LineHeight",void 0),Object(n.a)([b({attribute:"type-ramp-plus-5-font-size",default:"46px"})],w.prototype,"typeRampPlus5FontSize",void 0),Object(n.a)([b({attribute:"type-ramp-plus-5-line-height",default:"56px"})],w.prototype,"typeRampPlus5LineHeight",void 0),Object(n.a)([b({attribute:"type-ramp-plus-6-font-size",default:"60px"})],w.prototype,"typeRampPlus6FontSize",void 0),Object(n.a)([b({attribute:"type-ramp-plus-6-line-height",default:"72px"})],w.prototype,"typeRampPlus6LineHeight",void 0),Object(n.a)([b({attribute:"accent-fill-rest-delta",converter:r.c,cssCustomProperty:!1,default:s.b.accentFillRestDelta})],w.prototype,"accentFillRestDelta",void 0),Object(n.a)([b({attribute:"accent-fill-hover-delta",converter:r.c,cssCustomProperty:!1,default:s.b.accentFillHoverDelta})],w.prototype,"accentFillHoverDelta",void 0),Object(n.a)([b({attribute:"accent-fill-active-delta",cssCustomProperty:!1,converter:r.c,default:s.b.accentFillActiveDelta})],w.prototype,"accentFillActiveDelta",void 0),Object(n.a)([b({attribute:"accent-fill-focus-delta",converter:r.c,cssCustomProperty:!1,default:s.b.accentFillFocusDelta})],w.prototype,"accentFillFocusDelta",void 0),Object(n.a)([b({attribute:"accent-fill-selected-delta",converter:r.c,cssCustomProperty:!1,default:s.b.accentFillSelectedDelta})],w.prototype,"accentFillSelectedDelta",void 0),Object(n.a)([b({attribute:"accent-foreground-rest-delta",converter:r.c,cssCustomProperty:!1,default:s.b.accentForegroundRestDelta})],w.prototype,"accentForegroundRestDelta",void 0),Object(n.a)([b({attribute:"accent-foreground-hover-delta",converter:r.c,cssCustomProperty:!1,default:s.b.accentForegroundHoverDelta})],w.prototype,"accentForegroundHoverDelta",void 0),Object(n.a)([b({attribute:"accent-foreground-active-delta",converter:r.c,cssCustomProperty:!1,default:s.b.accentForegroundActiveDelta})],w.prototype,"accentForegroundActiveDelta",void 0),Object(n.a)([b({attribute:"accent-foreground-focus-delta",converter:r.c,cssCustomProperty:!1,default:s.b.accentForegroundFocusDelta})],w.prototype,"accentForegroundFocusDelta",void 0),Object(n.a)([b({attribute:"neutral-fill-rest-delta",converter:r.c,cssCustomProperty:!1,default:s.b.neutralFillRestDelta})],w.prototype,"neutralFillRestDelta",void 0),Object(n.a)([b({attribute:"neutral-fill-hover-delta",converter:r.c,cssCustomProperty:!1,default:s.b.neutralFillHoverDelta})],w.prototype,"neutralFillHoverDelta",void 0),Object(n.a)([b({attribute:"neutral-fill-active-delta",converter:r.c,cssCustomProperty:!1,default:s.b.neutralFillActiveDelta})],w.prototype,"neutralFillActiveDelta",void 0),Object(n.a)([b({attribute:"neutral-fill-focus-delta",converter:r.c,cssCustomProperty:!1,default:s.b.neutralFillFocusDelta})],w.prototype,"neutralFillFocusDelta",void 0),Object(n.a)([b({attribute:"neutral-fill-selected-delta",converter:r.c,cssCustomProperty:!1,default:s.b.neutralFillSelectedDelta})],w.prototype,"neutralFillSelectedDelta",void 0),Object(n.a)([b({attribute:"neutral-fill-input-rest-delta",converter:r.c,cssCustomProperty:!1,default:s.b.neutralFillInputRestDelta})],w.prototype,"neutralFillInputRestDelta",void 0),Object(n.a)([b({attribute:"neutral-fill-input-hover-delta",converter:r.c,cssCustomProperty:!1,default:s.b.neutralFillInputHoverDelta})],w.prototype,"neutralFillInputHoverDelta",void 0),Object(n.a)([b({attribute:"neutral-fill-input-active-delta",converter:r.c,cssCustomProperty:!1,default:s.b.neutralFillInputActiveDelta})],w.prototype,"neutralFillInputActiveDelta",void 0),Object(n.a)([b({attribute:"neutral-fill-input-focus-delta",converter:r.c,cssCustomProperty:!1,default:s.b.neutralFillInputFocusDelta})],w.prototype,"neutralFillInputFocusDelta",void 0),Object(n.a)([b({attribute:"neutral-fill-input-selected-delta",converter:r.c,cssCustomProperty:!1,default:s.b.neutralFillInputSelectedDelta})],w.prototype,"neutralFillInputSelectedDelta",void 0),Object(n.a)([b({attribute:"neutral-fill-stealth-rest-delta",converter:r.c,cssCustomProperty:!1,default:s.b.neutralFillStealthRestDelta})],w.prototype,"neutralFillStealthRestDelta",void 0),Object(n.a)([b({attribute:"neutral-fill-stealth-hover-delta",converter:r.c,cssCustomProperty:!1,default:s.b.neutralFillStealthHoverDelta})],w.prototype,"neutralFillStealthHoverDelta",void 0),Object(n.a)([b({attribute:"neutral-fill-stealth-active-delta",converter:r.c,cssCustomProperty:!1,default:s.b.neutralFillStealthActiveDelta})],w.prototype,"neutralFillStealthActiveDelta",void 0),Object(n.a)([b({attribute:"neutral-fill-stealth-focus-delta",converter:r.c,cssCustomProperty:!1,default:s.b.neutralFillStealthFocusDelta})],w.prototype,"neutralFillStealthFocusDelta",void 0),Object(n.a)([b({attribute:"neutral-fill-stealth-selected-delta",converter:r.c,cssCustomProperty:!1,default:s.b.neutralFillStealthSelectedDelta})],w.prototype,"neutralFillStealthSelectedDelta",void 0),Object(n.a)([b({attribute:"neutral-fill-toggle-hover-delta",converter:r.c,cssCustomProperty:!1,default:s.b.neutralFillToggleHoverDelta})],w.prototype,"neutralFillToggleHoverDelta",void 0),Object(n.a)([b({attribute:"neutral-fill-toggle-hover-active",converter:r.c,cssCustomProperty:!1,default:s.b.neutralFillToggleActiveDelta})],w.prototype,"neutralFillToggleActiveDelta",void 0),Object(n.a)([b({attribute:"neutral-fill-toggle-hover-focus",converter:r.c,cssCustomProperty:!1,default:s.b.neutralFillToggleFocusDelta})],w.prototype,"neutralFillToggleFocusDelta",void 0),Object(n.a)([b({attribute:"base-layer-luminance",converter:r.c,cssCustomProperty:!1,default:s.b.baseLayerLuminance})],w.prototype,"baseLayerLuminance",void 0),Object(n.a)([b({attribute:"neutral-fill-card-delta",converter:r.c,cssCustomProperty:!1,default:s.b.neutralFillCardDelta})],w.prototype,"neutralFillCardDelta",void 0),Object(n.a)([b({attribute:"neutral-foreground-hover-delta",converter:r.c,cssCustomProperty:!1,default:s.b.neutralForegroundHoverDelta})],w.prototype,"neutralForegroundHoverDelta",void 0),Object(n.a)([b({attribute:"neutral-foreground-active-delta",converter:r.c,cssCustomProperty:!1,default:s.b.neutralForegroundActiveDelta})],w.prototype,"neutralForegroundActiveDelta",void 0),Object(n.a)([b({attribute:"neutral-foreground-focus-delta",converter:r.c,cssCustomProperty:!1,default:s.b.neutralForegroundFocusDelta})],w.prototype,"neutralForegroundFocusDelta",void 0),Object(n.a)([b({attribute:"neutral-divider-rest-delta",converter:r.c,cssCustomProperty:!1,default:s.b.neutralDividerRestDelta})],w.prototype,"neutralDividerRestDelta",void 0),Object(n.a)([b({attribute:"neutral-outline-rest-delta",converter:r.c,cssCustomProperty:!1,default:s.b.neutralOutlineRestDelta})],w.prototype,"neutralOutlineRestDelta",void 0),Object(n.a)([b({attribute:"neutral-outline-hover-delta",converter:r.c,cssCustomProperty:!1,default:s.b.neutralOutlineHoverDelta})],w.prototype,"neutralOutlineHoverDelta",void 0),Object(n.a)([b({attribute:"neutral-outline-active-delta",converter:r.c,cssCustomProperty:!1,default:s.b.neutralOutlineActiveDelta})],w.prototype,"neutralOutlineActiveDelta",void 0),Object(n.a)([b({attribute:"neutral-outline-focus-delta",converter:r.c,cssCustomProperty:!1,default:s.b.neutralOutlineFocusDelta})],w.prototype,"neutralOutlineFocusDelta",void 0),w=Object(n.a)([y({name:"fluent-design-system-provider",template:j,styles:m,shadowOptions:{mode:"closed"}})],w)},function(t,e,i){"use strict";i.d(e,"a",(function(){return n}));function n(t,e,i,n){var o,r=arguments.length,a=r<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,i):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,i,n);else for(var s=t.length-1;s>=0;s--)(o=t[s])&&(a=(r<3?o(a):r>3?o(e,i,a):o(e,i))||a);return r>3&&a&&Object.defineProperty(e,i,a),a}},function(t,e,i){"use strict";function n(t,e,i){return isNaN(t)||t<=e?e:t>=i?i:t}function o(t,e,i){return isNaN(t)||t<=e?0:t>=i?1:t/(i-e)}function r(t,e,i){return isNaN(t)?e:e+t*(i-e)}function a(t){return t*(Math.PI/180)}function s(t){return t*(180/Math.PI)}function c(t){const e=Math.round(n(t,0,255)).toString(16);return 1===e.length?"0"+e:e}i.d(e,"a",(function(){return n})),i.d(e,"e",(function(){return o})),i.d(e,"c",(function(){return r})),i.d(e,"b",(function(){return a})),i.d(e,"f",(function(){return s})),i.d(e,"d",(function(){return c})),i.d(e,"g",(function(){return l}));Math.PI;function l(t,e){const i=Math.pow(10,e);return Math.round(t*i)/i}},function(t,e,i){"use strict";var n=i(130),o=i(131),r=i(38),a=i(1),s=i(2),c=i(52);const l=Object(a.d)(Object(c.a)(s.O,4.5,0,s.G,s.E,s.F)),u=Object(a.i)(a.a.rest,l),A=(Object(a.i)(a.a.hover,l),Object(a.i)(a.a.active,l),Object(a.i)(a.a.focus,l),(t,e)=>Object(a.e)(r.b,t)>=e?r.b:r.a);function g(t){return function(e){return"function"==typeof e?i=>A(e(i),t):A(u(e),t)}}const h=g(4.5),d=g(3);function I(t){return Object(c.a)(s.O,t,0,0,0,0)}const M=Object(a.i)(a.a.rest,Object(a.d)(I(4.5))),C=Object(a.i)(a.a.rest,Object(a.d)(I(3)));var p=i(6);function f(t){return e=>{const i=Object(s.j)(e),n=Object(s.a)(e),o=Object(p.b)(s.j,n)(e),r={rest:Object(s.i)(e),hover:Object(s.h)(e),active:Object(s.f)(e),focus:Object(s.g)(e)},a=Object(p.e)(e)?-1:1,c=o+(1===a?Math.min(r.rest,r.hover):Math.max(a*r.rest,a*r.hover)),l=Object(p.h)(s.k)(s.j)(()=>c)(()=>a)(e=>e>=t)(e),u=Object(p.c)(s.j,l)(e),A=u+a*Math.abs(r.rest-r.hover),g=1===a?r.rest<r.hover:a*r.rest>a*r.hover,h=g?u:A,d=g?A:u,I=h+a*r.active,M=h+a*r.focus;return{rest:Object(p.d)(h,i),hover:Object(p.d)(d,i),active:Object(p.d)(I,i),focus:Object(p.d)(M,i)}}}const D=Object(a.d)(f(4.5)),y=Object(a.d)(f(3)),b=(Object(a.i)(a.a.rest,D),Object(a.i)(a.a.hover,D),Object(a.i)(a.a.active,D),Object(a.i)(a.a.focus,D),Object(a.i)(a.a.rest,y),Object(a.i)(a.a.hover,y),Object(a.i)(a.a.active,y),Object(a.i)(a.a.focus,y),(t,e)=>Object(a.e)(r.b,t)>=e?r.b:r.a);function j(t){return function(e){return"function"==typeof e?i=>b(e(i),t):b(Object(s.a)(e),t)}}const E=j(4.5),m=(j(3),Object(a.f)(s.x,s.r,s.o,s.q));function N(t){return e=>{const i=Object(p.a)(e),n=i>=m(e)?-1:1;return Object(p.d)(i+n*t(e),Object(s.O)(e))}}const O=Object(a.d)(N(s.x)),w=Object(a.d)(N(s.r)),v=Object(a.d)(N(s.o)),Q=Object(a.d)(N(s.q)),x=Object(a.d)(N(s.y)),B=Object(a.d)(t=>({rest:O(t),hover:w(t),active:v(t),focus:Q(t),selected:x(t)})),L=Object(a.f)(s.x,s.r,s.o,s.q,s.C,s.B,s.z,s.A);function S(t){return e=>{const i=Object(p.a)(e),n=i>=L(e)?-1:1;return Object(p.d)(i+n*t(e),Object(s.O)(e))}}const k=Object(a.d)(S(s.C)),T=Object(a.d)(S(s.B)),z=Object(a.d)(S(s.z)),F=Object(a.d)(S(s.A)),U=Object(a.d)(S(s.D)),Y=Object(a.d)(t=>({rest:k(t),hover:T(t),active:z(t),focus:F(t),selected:U(t)}));function H(t){return e=>{const i=Object(p.e)(e)?-1:1;return Object(p.d)(Object(p.a)(e)-t(e)*i,Object(s.O)(e))}}const R=Object(a.d)(H(s.v)),P=Object(a.d)(H(s.u)),G=Object(a.d)(H(s.s)),K=Object(a.d)(H(s.t)),J=Object(a.d)(H(s.w)),q=Object(a.d)(t=>({rest:R(t),hover:P(t),active:G(t),focus:K(t),selected:J(t)}));var Z=i(138);const W=Object(a.f)(s.x,s.r,s.o);function V(t){return e=>{const i=Object(s.j)(e),n=i.length,o=Object(s.a)(e),r=E(Object.assign({},e,{backgroundColor:o})),c=Object(s.d)(e),l=Object(p.a)(e)>=W(e)?-1:1,u=n-1,A=Object(p.b)(s.j,o)(e);let g=0;for(;g<l*c&&Object(Z.a)(A+g+l,0,n)&&Object(a.e)(i[A+g+l],r)>=t&&Object(Z.a)(A+g+l+l,0,u);)g+=l;const h=A+g,d=h+-1*l*c,I=d+l*Object(s.b)(e),M=d+l*Object(s.c)(e);return{rest:Object(p.d)(d,i),hover:Object(p.d)(h,i),active:Object(p.d)(I,i),focus:Object(p.d)(M,i),selected:Object(p.d)(d+(Object(p.e)(e)?-1*Object(s.e)(e):Object(s.e)(e)),i)}}}const X=Object(a.d)(V(4.5)),$=Object(a.d)(V(3)),_=(Object(a.i)(a.a.rest,X),Object(a.i)(a.a.hover,X),Object(a.i)(a.a.active,X),Object(a.i)(a.a.focus,X),Object(a.i)(a.a.selected,X),Object(a.i)(a.a.rest,$),Object(a.i)(a.a.hover,$),Object(a.i)(a.a.active,$),Object(a.i)(a.a.focus,$),Object(a.i)(a.a.selected,$),t=>{const e=Object(s.p)(t),i=Object(p.b)(s.O,Object(s.k)(t))(t);return Object(p.d)(i-(i<e?-1*e:e),Object(s.O)(t))});const tt=Object(a.d)(t=>{const e=Object(s.O)(t),i=Object(p.a)(t),n=Object(p.e)(t)?-1:1,o=Object(s.N)(t),r=i+n*o,a=r+n*(Object(s.M)(t)-o),c=r+n*(Object(s.K)(t)-o),l=r+n*(Object(s.L)(t)-o);return{rest:Object(p.d)(r,e),hover:Object(p.d)(a,e),active:Object(p.d)(c,e),focus:Object(p.d)(l,e)}}),et=(Object(a.i)(a.a.rest,tt),Object(a.i)(a.a.hover,tt),Object(a.i)(a.a.active,tt),Object(a.i)(a.a.focus,tt),Object(a.d)(t=>{const e=Object(s.O)(t),i=Object(p.a)(t),n=Object(s.n)(t),o=i+(Object(p.e)(t)?-1:1)*n;return Object(p.d)(o,e)}));function it(t){return(...e)=>i=>{const n=e[0];let o="function"==typeof n?n(i):n;for(let n=1;n<e.length;n++){const r=e[n];o=t(o,"function"==typeof r?r(i):r)}return o}}const nt=it((t,e)=>t+e),ot=it((t,e)=>t-e),rt=it((t,e)=>t*e);it((t,e)=>t/e);function at(...t){return nt.apply(this,t)}function st(...t){return ot.apply(this,t)}function ct(...t){return rt.apply(this,t)}var lt,ut,At,gt=i(33),ht=i(10);function dt(t,e){return i=>-1===Object(s.l)(i)?e(i):t(i)}!function(t){t[t.L1=0]="L1",t[t.L1Alt=3]="L1Alt",t[t.L2=10]="L2",t[t.L3=13]="L3",t[t.L4=16]="L4"}(lt||(lt={})),function(t){t[t.L1=76]="L1",t[t.L1Alt=76]="L1Alt",t[t.L2=79]="L2",t[t.L3=82]="L3",t[t.L4=85]="L4"}(ut||(ut={})),function(t){t[t.LightMode=1]="LightMode",t[t.DarkMode=.23]="DarkMode"}(At||(At={}));const It=Object(p.b)(s.O,t=>{const e=Object(s.l)(t);return new gt.a(e,e,e,1).toStringHexRGB()}),Mt=t=>Object(ht.a)(st(It,s.p)(t),0,Object(s.O)(t).length-1),Ct=Object(a.f)(s.x,s.r,s.o),pt=Object(a.f)(at(It,s.p),Ct),ft=t=>{const e=new gt.a(.14,.14,.14,1);return Object(p.b)(s.O,e.toStringHexRGB())(t)},Dt=Object(a.d)(dt(Object(p.d)(st(Mt,s.p),s.O),Object(p.i)(s.O)(0,st(ft,ct(s.p,5))))),yt=Object(a.d)(dt(Object(p.d)(Mt,s.O),Object(p.i)(s.O)(0,st(ft,ct(s.p,4))))),bt=Object(a.d)(dt(Object(p.d)(at(Mt,s.p),s.O),Object(p.i)(s.O)(s.p,st(ft,ct(s.p,3))))),jt=Object(a.d)(dt(Object(p.d)(It,s.O),Object(p.i)(s.O)(0,st(ft,ct(s.p,3))))),Et=bt,mt=Object(a.d)(dt(Object(p.d)(pt,s.O),Object(p.i)(s.O)(Ct,st(ft,ct(s.p,2))))),Nt=Object(a.d)(dt(Object(p.d)(at(pt,s.p),s.O),Object(p.i)(s.O)(at(Ct,s.p),st(ft,s.p)))),Ot=Object(a.d)(dt(Object(p.d)(at(pt,ct(s.p,2)),s.O),Object(p.i)(s.O)(at(Ct,ct(s.p,2)),ft)));function wt(t){return t>3.5}const vt=Object(p.h)(s.k)(s.O)((function(t,e,i){return Object(p.b)(s.O,t)(i)}))((function(t,e,i){return Object(p.e)(i)?-1:1}))(wt),Qt=Object(a.d)(vt);function xt(t,e,i){return Object(p.e)(i)?1:-1}var Bt=i(133),Lt=i(8);i.d(e,"v",(function(){return St})),i.d(e,"u",(function(){return kt})),i.d(e,"r",(function(){return Tt})),i.d(e,"s",(function(){return zt})),i.d(e,"t",(function(){return Ft})),i.d(e,"g",(function(){return Ut})),i.d(e,"f",(function(){return Yt})),i.d(e,"d",(function(){return Ht})),i.d(e,"e",(function(){return Rt})),i.d(e,"l",(function(){return Pt})),i.d(e,"k",(function(){return Gt})),i.d(e,"i",(function(){return Kt})),i.d(e,"j",(function(){return Jt})),i.d(e,"o",(function(){return qt})),i.d(e,"n",(function(){return Zt})),i.d(e,"m",(function(){return Wt})),i.d(e,"c",(function(){return Vt})),i.d(e,"b",(function(){return Xt})),i.d(e,"a",(function(){return $t})),i.d(e,"A",(function(){return _t})),i.d(e,"z",(function(){return te})),i.d(e,"y",(function(){return ee})),i.d(e,"h",(function(){return ie})),i.d(e,"w",(function(){return ne})),i.d(e,"x",(function(){return oe})),i.d(e,"p",(function(){return re})),i.d(e,"q",(function(){return ae}));const St=Object(n.b)("neutral-foreground-rest",t=>Object(o.a)(t).rest,Lt.a.findProvider),kt=Object(n.b)("neutral-foreground-hover",t=>Object(o.a)(t).hover,Lt.a.findProvider),Tt=Object(n.b)("neutral-foreground-active",t=>Object(o.a)(t).active,Lt.a.findProvider),zt=Object(n.b)("neutral-foreground-focus",t=>Object(o.a)(t).focus,Lt.a.findProvider),Ft=(Object(n.b)("neutral-foreground-toggle",h,Lt.a.findProvider),Object(n.b)("neutral-foreground-toggle-large",d,Lt.a.findProvider),Object(n.b)("neutral-foreground-hint",M,Lt.a.findProvider)),Ut=(Object(n.b)("neutral-foreground-hint-large",C,Lt.a.findProvider),Object(n.b)("accent-foreground-rest",t=>D(t).rest,Lt.a.findProvider)),Yt=Object(n.b)("accent-foreground-hover",t=>D(t).hover,Lt.a.findProvider),Ht=Object(n.b)("accent-foreground-active",t=>D(t).active,Lt.a.findProvider),Rt=(Object(n.b)("accent-foreground-focus",t=>D(t).focus,Lt.a.findProvider),Object(n.b)("accent-foreground-cut-rest",t=>E(t),Lt.a.findProvider)),Pt=(Object(n.b)("accent-foreground-large-rest",t=>y(t).rest,Lt.a.findProvider),Object(n.b)("accent-foreground-large-hover",t=>y(t).hover,Lt.a.findProvider),Object(n.b)("accent-foreground-large-active",t=>y(t).active,Lt.a.findProvider),Object(n.b)("accent-foreground-large-focus",t=>y(t).focus,Lt.a.findProvider),Object(n.b)("neutral-fill-rest",t=>B(t).rest,Lt.a.findProvider)),Gt=Object(n.b)("neutral-fill-hover",t=>B(t).hover,Lt.a.findProvider),Kt=Object(n.b)("neutral-fill-active",t=>B(t).active,Lt.a.findProvider),Jt=Object(n.b)("neutral-fill-focus",t=>B(t).focus,Lt.a.findProvider),qt=(Object(n.b)("neutral-fill-selected",t=>B(t).selected,Lt.a.findProvider),Object(n.b)("neutral-fill-stealth-rest",t=>Y(t).rest,Lt.a.findProvider)),Zt=Object(n.b)("neutral-fill-stealth-hover",t=>Y(t).hover,Lt.a.findProvider),Wt=Object(n.b)("neutral-fill-stealth-active",t=>Y(t).active,Lt.a.findProvider),Vt=(Object(n.b)("neutral-fill-stealth-focus",t=>Y(t).focus,Lt.a.findProvider),Object(n.b)("neutral-fill-stealth-selected",t=>Y(t).selected,Lt.a.findProvider),Object(n.b)("neutral-fill-toggle-rest",t=>l(t).rest,Lt.a.findProvider),Object(n.b)("neutral-fill-toggle-hover",t=>l(t).hover,Lt.a.findProvider),Object(n.b)("neutral-fill-toggle-active",t=>l(t).active,Lt.a.findProvider),Object(n.b)("neutral-fill-toggle-focus",t=>l(t).focus,Lt.a.findProvider),Object(n.b)("neutral-fill-input-rest",t=>q(t).rest,Lt.a.findProvider),Object(n.b)("neutral-fill-input-hover",t=>q(t).hover,Lt.a.findProvider),Object(n.b)("neutral-fill-input-active",t=>q(t).active,Lt.a.findProvider),Object(n.b)("neutral-fill-input-focus",t=>q(t).focus,Lt.a.findProvider),Object(n.b)("accent-fill-rest",t=>X(t).rest,Lt.a.findProvider)),Xt=Object(n.b)("accent-fill-hover",t=>X(t).hover,Lt.a.findProvider),$t=Object(n.b)("accent-fill-active",t=>X(t).active,Lt.a.findProvider),_t=(Object(n.b)("accent-fill-focus",t=>X(t).focus,Lt.a.findProvider),Object(n.b)("accent-fill-selected",t=>X(t).selected,Lt.a.findProvider),Object(n.b)("accent-fill-large-rest",t=>$(t).rest,Lt.a.findProvider),Object(n.b)("accent-fill-large-hover",t=>$(t).hover,Lt.a.findProvider),Object(n.b)("accent-fill-large-active",t=>$(t).active,Lt.a.findProvider),Object(n.b)("accent-fill-large-focus",t=>$(t).focus,Lt.a.findProvider),Object(n.b)("accent-fill-large-selected",t=>$(t).selected,Lt.a.findProvider),Object(n.b)("neutral-fill-card-rest",t=>{return"function"==typeof(e=t)?t=>_(Object.assign({},t,{backgroundColor:e(t)})):_(e);var e},Lt.a.findProvider),Object(n.b)("neutral-outline-rest",t=>tt(t).rest,Lt.a.findProvider)),te=Object(n.b)("neutral-outline-hover",t=>tt(t).hover,Lt.a.findProvider),ee=Object(n.b)("neutral-outline-active",t=>tt(t).active,Lt.a.findProvider),ie=(Object(n.b)("neutral-outline-focus",t=>tt(t).focus,Lt.a.findProvider),Object(n.b)("neutral-divider-rest",et,Lt.a.findProvider)),ne=Object(n.b)("neutral-layer-floating",Dt,Lt.a.findProvider),oe=(Object(n.b)("neutral-layer-card",yt,Lt.a.findProvider),Object(n.b)("neutral-layer-card-container",bt,Lt.a.findProvider),Object(n.b)("neutral-layer-l1",jt,Lt.a.findProvider)),re=(Object(n.b)("neutral-layer-l1-alt",Et,Lt.a.findProvider),Object(n.b)("neutral-layer-l2",mt,Lt.a.findProvider),Object(n.b)("neutral-layer-l3",Nt,Lt.a.findProvider),Object(n.b)("neutral-layer-l4",Ot,Lt.a.findProvider),Object(n.b)("neutral-focus",Qt,Lt.a.findProvider)),ae=Object(n.b)("neutral-focus-inner-accent",(se=s.a,Object(p.h)(Qt)(s.j)(function(t){return(e,i,n)=>i.indexOf(t(n))}(se))(xt)(wt)),Lt.a.findProvider);var se;Object(n.b)("inline-start",t=>Object(s.m)(t)===Bt.a.ltr?"left":"right",Lt.a.findProvider),Object(n.b)("inline-end",t=>Object(s.m)(t)===Bt.a.ltr?"right":"left",Lt.a.findProvider)},function(t,e,i){"use strict";var n;i.d(e,"a",(function(){return n})),function(t){t.Canvas="Canvas",t.CanvasText="CanvasText",t.LinkText="LinkText",t.VisitedText="VisitedText",t.ActiveText="ActiveText",t.ButtonFace="ButtonFace",t.ButtonText="ButtonText",t.Field="Field",t.FieldText="FieldText",t.Highlight="Highlight",t.HighlightText="HighlightText",t.GrayText="GrayText"}(n||(n={}))},,function(t,e){function i(t,e){for(var i=0;i<e.length;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}t.exports=function(t,e,n){return e&&i(t.prototype,e),n&&i(t,n),t}},function(t,e){t.exports=function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}},,function(t,e){function i(e){return t.exports=i=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)},i(e)}t.exports=i},function(t,e,i){"use strict";i.d(e,"c",(function(){return l})),i.d(e,"b",(function(){return u})),i.d(e,"a",(function(){return A}));var n=i(86);const o=[],r=n.a.trustedTypes.createPolicy("fast-html",{createHTML:t=>t});let a=r;function s(){let t=0;for(;t<o.length;){if(o[t].call(),t++,t>1024){for(let e=0,i=o.length-t;e<i;e++)o[e]=o[e+t];o.length-=t,t=0}}o.length=0}const c="fast-"+Math.random().toString(36).substring(2,8),l=c+"{",u="}"+c,A=Object.freeze({supportsAdoptedStyleSheets:Array.isArray(document.adoptedStyleSheets)&&"replace"in CSSStyleSheet.prototype,setHTMLPolicy(t){if(a!==r)throw new Error("The HTML policy can only be set once.");a=t},createHTML:t=>a.createHTML(t),isMarker:t=>t&&8===t.nodeType&&t.data.startsWith(c),extractDirectiveIndexFromMarker:t=>parseInt(t.data.replace(c+":","")),createInterpolationPlaceholder:t=>`${l}${t}${u}`,createCustomAttributePlaceholder(t,e){return`${t}="${this.createInterpolationPlaceholder(e)}"`},createBlockPlaceholder:t=>`\x3c!--${c}:${t}--\x3e`,queueUpdate(t){o.length<1&&window.requestAnimationFrame(s),o.push(t)},nextUpdate:()=>new Promise(t=>{A.queueUpdate(t)}),setAttribute(t,e,i){null==i?t.removeAttribute(e):t.setAttribute(e,i)},setBooleanAttribute(t,e,i){i?t.setAttribute(e,""):t.removeAttribute(e)},removeChildNodes(t){for(let e=t.firstChild;null!==e;e=t.firstChild)t.removeChild(e)},createTemplateWalker:t=>document.createTreeWalker(t,133,null,!1)})},function(t,e,i){"use strict";i.d(e,"a",(function(){return n}));function n(t,e,i,n){var o,r=arguments.length,a=r<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,i):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,i,n);else for(var s=t.length-1;s>=0;s--)(o=t[s])&&(a=(r<3?o(a):r>3?o(e,i,a):o(e,i))||a);return r>3&&a&&Object.defineProperty(e,i,a),a}},,function(t,e,i){"use strict";i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){return s})),i.d(e,"b",(function(){return c}));var n=i(24),o=i(18);const r={toView:t=>t?"true":"false",fromView:t=>null!=t&&"false"!==t&&!1!==t&&0!==t},a={toView(t){if(null==t)return null;const e=1*t;return isNaN(e)?null:e.toString()},fromView(t){if(null==t)return null;const e=1*t;return isNaN(e)?null:e}};class s{constructor(t,e,i=e.toLowerCase(),n="reflect",o){this.guards=new Set,this.Owner=t,this.name=e,this.attribute=i,this.mode=n,this.converter=o,this.fieldName="_"+e,this.callbackName=e+"Changed",this.hasCallback=this.callbackName in t.prototype,"boolean"===n&&void 0===o&&(this.converter=r)}setValue(t,e){const i=t[this.fieldName],n=this.converter;void 0!==n&&(e=n.fromView(e)),i!==e&&(t[this.fieldName]=e,this.tryReflectToAttribute(t),this.hasCallback&&t[this.callbackName](i,e),t.$fastController.notify(this.name))}getValue(t){return n.a.track(t,this.name),t[this.fieldName]}onAttributeChangedCallback(t,e){this.guards.has(t)||(this.guards.add(t),this.setValue(t,e),this.guards.delete(t))}tryReflectToAttribute(t){const e=this.mode,i=this.guards;i.has(t)||"fromView"===e||o.a.queueUpdate(()=>{i.add(t);const n=t[this.fieldName];switch(e){case"reflect":const e=this.converter;o.a.setAttribute(t,this.attribute,void 0!==e?e.toView(n):n);break;case"boolean":o.a.setBooleanAttribute(t,this.attribute,n)}i.delete(t)})}static collect(t,...e){const i=[];e.push(t.attributes);for(let n=0,o=e.length;n<o;++n){const o=e[n];if(void 0!==o)for(let e=0,n=o.length;e<n;++e){const n=o[e];"string"==typeof n?i.push(new s(t,n)):i.push(new s(t,n.property,n.attribute,n.mode,n.converter))}}return i}}function c(t,e){let i;function n(t,e){arguments.length>1&&(i.property=e);const n=t.constructor.attributes||(t.constructor.attributes=[]);n.push(i)}return arguments.length>1?(i={},void n(t,e)):(i=void 0===t?{}:t,n)}},function(t,e,i){var n=i(96);t.exports=function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&n(t,e)}},function(t,e,i){var n=i(97),o=i(15);t.exports=function(t,e){return!e||"object"!==n(e)&&"function"!=typeof e?o(t):e}},function(t,e,i){"use strict";i.d(e,"a",(function(){return A})),i.d(e,"c",(function(){return d})),i.d(e,"d",(function(){return M})),i.d(e,"b",(function(){return p}));var n=i(18),o=i(59);const r=/(\:|\&\&|\|\||if)/,a=new WeakMap,s=new WeakMap;let c=void 0,l=t=>{throw new Error("Must call enableArrayObservation before observing arrays.")};class u{constructor(t){this.name=t,this.field="_"+t,this.callback=t+"Changed"}getValue(t){return void 0!==c&&c.watch(t,this.name),t[this.field]}setValue(t,e){const i=this.field,n=t[i];if(n!==e){t[i]=e;const o=t[this.callback];"function"==typeof o&&o.call(t,n,e),g(t).notify(this.name)}}}const A=Object.freeze({setArrayObserverFactory(t){l=t},getNotifier(t){let e=t.$fastController||a.get(t);return void 0===e&&(Array.isArray(t)?e=l(t):a.set(t,e=new o.a(t))),e},track(t,e){void 0!==c&&c.watch(t,e)},trackVolatile(){void 0!==c&&(c.needsRefresh=!0)},notify(t,e){g(t).notify(e)},defineProperty(t,e){"string"==typeof e&&(e=new u(e)),this.getAccessors(t).push(e),Reflect.defineProperty(t,e.name,{enumerable:!0,get:function(){return e.getValue(this)},set:function(t){e.setValue(this,t)}})},getAccessors(t){let e=s.get(t);if(void 0===e){let i=Reflect.getPrototypeOf(t);for(;void 0===e&&null!==i;)e=s.get(i),i=Reflect.getPrototypeOf(i);e=void 0===e?[]:e.slice(0),s.set(t,e)}return e},binding(t,e,i=this.isVolatileBinding(t)){return new f(t,e,i)},isVolatileBinding:t=>r.test(t.toString())}),g=A.getNotifier,h=(A.trackVolatile,n.a.queueUpdate);function d(t,e){A.defineProperty(t,e)}let I=null;function M(t){I=t}class C{constructor(){this.index=0,this.length=0,this.parent=null,this.parentContext=null}get event(){return I}get isEven(){return this.index%2==0}get isOdd(){return this.index%2!=0}get isFirst(){return 0===this.index}get isInMiddle(){return!this.isFirst&&!this.isLast}get isLast(){return this.index===this.length-1}}A.defineProperty(C.prototype,"index"),A.defineProperty(C.prototype,"length");const p=Object.seal(new C);class f extends o.b{constructor(t,e,i=!1){super(t,e),this.binding=t,this.isVolatileBinding=i,this.needsRefresh=!0,this.needsQueue=!0,this.first=this,this.last=null,this.propertySource=void 0,this.propertyName=void 0,this.notifier=void 0,this.next=void 0}observe(t,e){this.needsRefresh&&null!==this.last&&this.disconnect();const i=c;c=this.needsRefresh?this:void 0,this.needsRefresh=this.isVolatileBinding;const n=this.binding(t,e);return c=i,n}disconnect(){if(null!==this.last){let t=this.first;for(;void 0!==t;)t.notifier.unsubscribe(this,t.propertyName),t=t.next;this.last=null,this.needsRefresh=!0}}watch(t,e){const i=this.last,n=g(t),o=null===i?this.first:{};if(o.propertySource=t,o.propertyName=e,o.notifier=n,n.subscribe(this,e),null!==i){if(!this.needsRefresh){c=void 0;const e=i.propertySource[i.propertyName];c=this,t===e&&(this.needsRefresh=!0)}i.next=o}this.last=o}handleChange(){this.needsQueue&&(this.needsQueue=!1,h(this))}call(){null!==this.last&&(this.needsQueue=!0,this.notify(this))}}},function(t,e,i){"use strict";var n=i(7),o=i.n(n),r=i(14),a=i.n(r),s=i(0),c=i.n(s),l=i(83),u=i(84),A=i(4),g=i(47),h=function(){function t(){o()(this,t),c()(this,"jsVersion",void 0),c()(this,"appClientInfoIname",void 0),c()(this,"impressionId",void 0),c()(this,"domainName",void 0),this.jsVersion="1.1",this.appClientInfoIname=A.a.Edge}return a()(t,[{key:"setData",value:function(t,e){t===g.a.ImpressionId?this.impressionId=e:t===g.a.DomainName?this.domainName=e:t===g.a.Client&&(this.appClientInfoIname=e)}},{key:"logClientEvent",value:function(t,e,i,n,o,r){if(o){this.domainName&&(n.Domain=this.domainName);var a="";this.impressionId&&(a=this.impressionId);var s=new l.a(this.appClientInfoIname,this.jsVersion),c=new u.a(t,e,JSON.stringify(n),i,a,s);o.sendBingTelemetry(c.ToString(),"-1.0"),void 0!==r&&o.recordAsUserAction(r)}}}]),t}(),d=function(){function t(){o()(this,t)}return a()(t,null,[{key:"GetLogModule",value:function(){return this.logModule}}]),t}();c()(d,"logModule",new h);e.a=d},function(t,e,i){"use strict";e.a=function(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}},function(t,e,i){"use strict";var n=i(133),o=(i(132),i(43));var r=function(){this.__data__=new o.a,this.size=0};var a=function(t){var e=this.__data__,i=e.delete(t);return this.size=e.size,i};var s=function(t){return this.__data__.get(t)};var c=function(t){return this.__data__.has(t)},l=i(65),u=i(61);var A=function(t,e){var i=this.__data__;if(i instanceof o.a){var n=i.__data__;if(!l.a||n.length<199)return n.push([t,e]),this.size=++i.size,this;i=this.__data__=new u.a(n)}return i.set(t,e),this.size=i.size,this};function g(t){var e=this.__data__=new o.a(t);this.size=e.size}g.prototype.clear=r,g.prototype.delete=a,g.prototype.get=s,g.prototype.has=c,g.prototype.set=A;var h=g,d=i(55),I=function(){try{var t=Object(d.a)(Object,"defineProperty");return t({},"",{}),t}catch(t){}}();var M=function(t,e,i){"__proto__"==e&&I?I(t,e,{configurable:!0,enumerable:!0,value:i,writable:!0}):t[e]=i},C=i(41);var p=function(t,e,i){(void 0!==i&&!Object(C.a)(t[e],i)||void 0===i&&!(e in t))&&M(t,e,i)},f=i(72),D=i(89),y=i(35).a.Uint8Array;var b=function(t){var e=new t.constructor(t.byteLength);return new y(e).set(new y(t)),e};var j=function(t,e){var i=e?b(t.buffer):t.buffer;return new t.constructor(i,t.byteOffset,t.length)};var E=function(t,e){var i=-1,n=t.length;for(e||(e=Array(n));++i<n;)e[i]=t[i];return e},m=i(26),N=Object.create,O=function(){function t(){}return function(e){if(!Object(m.a)(e))return{};if(N)return N(e);t.prototype=e;var i=new t;return t.prototype=void 0,i}}(),w=i(70),v=Object(w.a)(Object.getPrototypeOf,Object),Q=i(54);var x=function(t){return"function"!=typeof t.constructor||Object(Q.a)(t)?{}:O(v(t))},B=i(62),L=i(49),S=i(42),k=i(32);var T=function(t){return Object(k.a)(t)&&Object(S.a)(t)},z=i(69),F=i(51),U=i(36),Y=Function.prototype,H=Object.prototype,R=Y.toString,P=H.hasOwnProperty,G=R.call(Object);var K=function(t){if(!Object(k.a)(t)||"[object Object]"!=Object(U.a)(t))return!1;var e=v(t);if(null===e)return!0;var i=P.call(e,"constructor")&&e.constructor;return"function"==typeof i&&i instanceof i&&R.call(i)==G},J=i(71);var q=function(t,e){if(("constructor"!==e||"function"!=typeof t[e])&&"__proto__"!=e)return t[e]},Z=Object.prototype.hasOwnProperty;var W=function(t,e,i){var n=t[e];Z.call(t,e)&&Object(C.a)(n,i)&&(void 0!==i||e in t)||M(t,e,i)};var V=function(t,e,i,n){var o=!i;i||(i={});for(var r=-1,a=e.length;++r<a;){var s=e[r],c=n?n(i[s],t[s],s,i,t):void 0;void 0===c&&(c=t[s]),o?M(i,s,c):W(i,s,c)}return i},X=i(73);var $=function(t){var e=[];if(null!=t)for(var i in Object(t))e.push(i);return e},_=Object.prototype.hasOwnProperty;var tt=function(t){if(!Object(m.a)(t))return $(t);var e=Object(Q.a)(t),i=[];for(var n in t)("constructor"!=n||!e&&_.call(t,n))&&i.push(n);return i};var et=function(t){return Object(S.a)(t)?Object(X.a)(t,!0):tt(t)};var it=function(t){return V(t,et(t))};var nt=function(t,e,i,n,o,r,a){var s=q(t,i),c=q(e,i),l=a.get(c);if(l)p(t,i,l);else{var u=r?r(s,c,i+"",t,e,a):void 0,A=void 0===u;if(A){var g=Object(L.a)(c),h=!g&&Object(z.a)(c),d=!g&&!h&&Object(J.a)(c);u=c,g||h||d?Object(L.a)(s)?u=s:T(s)?u=E(s):h?(A=!1,u=Object(D.a)(c,!0)):d?(A=!1,u=j(c,!0)):u=[]:K(c)||Object(B.a)(c)?(u=s,Object(B.a)(s)?u=it(s):Object(m.a)(s)&&!Object(F.a)(s)||(u=x(c))):A=!1}A&&(a.set(c,u),o(u,c,n,r,a),a.delete(c)),p(t,i,u)}};var ot=function t(e,i,n,o,r){e!==i&&Object(f.a)(i,(function(a,s){if(r||(r=new h),Object(m.a)(a))nt(e,i,s,n,t,o,r);else{var c=o?o(q(e,s),a,s+"",e,i,r):void 0;void 0===c&&(c=a),p(e,s,c)}}),et)},rt=i(53);var at=function(t,e,i){switch(i.length){case 0:return t.call(e);case 1:return t.call(e,i[0]);case 2:return t.call(e,i[0],i[1]);case 3:return t.call(e,i[0],i[1],i[2])}return t.apply(e,i)},st=Math.max;var ct=function(t,e,i){return e=st(void 0===e?t.length-1:e,0),function(){for(var n=arguments,o=-1,r=st(n.length-e,0),a=Array(r);++o<r;)a[o]=n[e+o];o=-1;for(var s=Array(e+1);++o<e;)s[o]=n[o];return s[e]=i(a),at(t,this,s)}},lt=i(66),ut=I?function(t,e){return I(t,"toString",{configurable:!0,enumerable:!1,value:Object(lt.a)(e),writable:!0})}:rt.a,At=Date.now;var gt=function(t){var e=0,i=0;return function(){var n=At(),o=16-(n-i);if(i=n,o>0){if(++e>=800)return arguments[0]}else e=0;return t.apply(void 0,arguments)}}(ut);var ht=function(t,e){return gt(ct(t,e,rt.a),t+"")},dt=i(68);var It=function(t,e,i){if(!Object(m.a)(i))return!1;var n=typeof e;return!!("number"==n?Object(S.a)(i)&&Object(dt.a)(e,i.length):"string"==n&&e in i)&&Object(C.a)(i[e],t)};Mt=function(t,e,i,n){ot(t,e,i,n)},ht((function(t,e){var i=-1,n=e.length,o=n>1?e[n-1]:void 0,r=n>2?e[2]:void 0;for(o=Mt.length>3&&"function"==typeof o?(n--,o):void 0,r&&It(e[0],e[1],r)&&(o=n<3?void 0:o,n=1),t=Object(t);++i<n;){var a=e[i];a&&Mt(t,a,i,o)}return t}));var Mt;var Ct=i(38);i.d(e,"a",(function(){return ft}));const pt={backgroundColor:Ct.b,contrast:0,density:0,designUnit:4,baseHeightMultiplier:8,baseHorizontalSpacingMultiplier:3,direction:n.a.ltr,cornerRadius:2,elevatedCornerRadius:4,focusOutlineWidth:2,fontWeight:{light:100,semilight:200,normal:400,semibold:600,bold:700},disabledOpacity:.3,outlineWidth:1,neutralPalette:["#FFFFFF","#FCFCFC","#FAFAFA","#F7F7F7","#F5F5F5","#F2F2F2","#EFEFEF","#EDEDED","#EAEAEA","#E8E8E8","#E5E5E5","#E2E2E2","#E0E0E0","#DDDDDD","#DBDBDB","#D8D8D8","#D6D6D6","#D3D3D3","#D0D0D0","#CECECE","#CBCBCB","#C9C9C9","#C6C6C6","#C3C3C3","#C1C1C1","#BEBEBE","#BCBCBC","#B9B9B9","#B6B6B6","#B4B4B4","#B1B1B1","#AFAFAF","#ACACAC","#A9A9A9","#A7A7A7","#A4A4A4","#A2A2A2","#9F9F9F","#9D9D9D","#9A9A9A","#979797","#959595","#929292","#909090","#8D8D8D","#8A8A8A","#888888","#858585","#838383","#808080","#7D7D7D","#7B7B7B","#787878","#767676","#737373","#717171","#6E6E6E","#6B6B6B","#696969","#666666","#646464","#616161","#5F5F5F","#5C5C5C","#5A5A5A","#575757","#545454","#525252","#4F4F4F","#4D4D4D","#4A4A4A","#484848","#454545","#424242","#404040","#3D3D3D","#3B3B3B","#383838","#363636","#333333","#313131","#2E2E2E","#2B2B2B","#292929","#262626","#242424","#212121","#1E1E1E","#1B1B1B","#181818","#151515","#121212","#101010","#000000"],accentPalette:["#FFFFFF","#FBFDFE","#F6FAFE","#F2F8FD","#EEF6FC","#E9F4FB","#E5F1FB","#E1EFFA","#DCEDF9","#D8EAF8","#D4E8F8","#CFE6F7","#CBE4F6","#C7E1F6","#C2DFF5","#BEDDF4","#BADAF3","#B6D8F3","#B1D6F2","#ADD4F1","#A9D1F0","#A4CFF0","#A0CDEF","#9CCAEE","#97C8EE","#93C6ED","#8FC4EC","#8AC1EB","#86BFEB","#82BDEA","#7DBAE9","#79B8E8","#75B6E8","#70B3E7","#6CB1E6","#68AFE5","#63ADE5","#5FAAE4","#5BA8E3","#56A6E3","#52A3E2","#4EA1E1","#499FE0","#459DE0","#419ADF","#3D98DE","#3896DD","#3493DD","#3091DC","#2B8FDB","#278DDB","#238ADA","#1E88D9","#1A86D8","#1683D8","#1181D7","#0D7FD6","#097DD5","#047AD5","#0078D4","#0075CF","#0072C9","#006FC4","#006CBE","#0069B9","#0066B4","#0063AE","#0060A9","#005CA3","#00599E","#005699","#005393","#00508E","#004D88","#004A83","#00477D","#004478","#004173","#003E6D","#003B68","#003862","#00355D","#003258","#002F52","#002B4D","#002847","#002542","#00223C","#001F36","#001B30","#00182B","#001525","#00121F","#000000"],accentBaseColor:"#0078D4",accentFillRestDelta:0,accentFillHoverDelta:4,accentFillActiveDelta:-5,accentFillFocusDelta:0,accentFillSelectedDelta:12,accentForegroundRestDelta:0,accentForegroundHoverDelta:6,accentForegroundActiveDelta:-4,accentForegroundFocusDelta:0,neutralFillRestDelta:7,neutralFillHoverDelta:10,neutralFillActiveDelta:5,neutralFillFocusDelta:0,neutralFillSelectedDelta:7,neutralFillInputRestDelta:0,neutralFillInputHoverDelta:0,neutralFillInputActiveDelta:0,neutralFillInputFocusDelta:0,neutralFillInputSelectedDelta:0,neutralFillStealthRestDelta:0,neutralFillStealthHoverDelta:5,neutralFillStealthActiveDelta:3,neutralFillStealthFocusDelta:0,neutralFillStealthSelectedDelta:7,neutralFillToggleHoverDelta:8,neutralFillToggleActiveDelta:-5,neutralFillToggleFocusDelta:0,baseLayerLuminance:-1,neutralFillCardDelta:3,neutralForegroundDarkIndex:93,neutralForegroundLightIndex:0,neutralForegroundHoverDelta:0,neutralForegroundActiveDelta:0,neutralForegroundFocusDelta:0,neutralDividerRestDelta:8,neutralOutlineRestDelta:25,neutralOutlineHoverDelta:40,neutralOutlineActiveDelta:16,neutralOutlineFocusDelta:25};function ft(t,e){return Object(F.a)(t)?t(e):t}e.b=pt},function(t,e,i){"use strict";i.d(e,"a",(function(){return o}));var n=i(105);const o=Object(n.a)()?"focus-visible":"focus"},,function(t,e,i){"use strict";i.d(e,"a",(function(){return r})),i.d(e,"b",(function(){return A}));var n=i(18);const o=new Map;class r{constructor(){this.behaviors=null}withBehaviors(...t){return this.behaviors=null===this.behaviors?t:this.behaviors.concat(t),this}withKey(t){return o.set(t,this),this}static find(t){return o.get(t)||null}}function a(t){return t.map(t=>t instanceof r?a(t.styles):[t]).reduce((t,e)=>t.concat(e),[])}function s(t){return t.map(t=>t instanceof r?t.behaviors:null).reduce((t,e)=>null===e?t:(null===t&&(t=[]),t.concat(e)),null)}r.create=(()=>{if(n.a.supportsAdoptedStyleSheets){const t=new Map;return e=>new c(e,t)}return t=>new u(t)})();class c extends r{constructor(t,e){super(),this.styles=t,this.behaviors=null,this.behaviors=s(t),this.styleSheets=a(t).map(t=>{if(t instanceof CSSStyleSheet)return t;let i=e.get(t);return void 0===i&&(i=new CSSStyleSheet,i.replaceSync(t),e.set(t,i)),i})}addStylesTo(t){t.adoptedStyleSheets=[...t.adoptedStyleSheets,...this.styleSheets]}removeStylesFrom(t){const e=this.styleSheets;t.adoptedStyleSheets=t.adoptedStyleSheets.filter(t=>-1===e.indexOf(t))}}let l=0;class u extends r{constructor(t){super(),this.styles=t,this.behaviors=null,this.behaviors=s(t),this.styleSheets=a(t),this.styleClass="fast-style-class-"+ ++l}addStylesTo(t){const e=this.styleSheets,i=this.styleClass;t===document&&(t=document.body);for(let n=e.length-1;n>-1;--n){const o=document.createElement("style");o.innerHTML=e[n],o.className=i,t.prepend(o)}}removeStylesFrom(t){t===document&&(t=document.body);const e=t.querySelectorAll("."+this.styleClass);for(let i=0,n=e.length;i<n;++i)t.removeChild(e[i])}}function A(t,...e){const i=[];let n="";for(let o=0,a=t.length-1;o<a;++o){n+=t[o];const a=e[o];a instanceof r||a instanceof CSSStyleSheet?(""!==n.trim()&&(i.push(n),n=""),i.push(a)):n+=a}return n+=t[t.length-1],""!==n.trim()&&i.push(n),r.create(i)}},function(t,e,i){"use strict";var n=i(7),o=i.n(n),r=i(15),a=i.n(r),s=i(22),c=i.n(s),l=i(23),u=i.n(l),A=i(17),g=i.n(A),h=i(0),d=i.n(h);function I(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}();return function(){var i,n=g()(t);if(e){var o=g()(this).constructor;i=Reflect.construct(n,arguments,o)}else i=n.apply(this,arguments);return u()(this,i)}}var M=function(t){c()(i,t);var e=I(i);function i(t,n){var r;return o()(this,i),r=e.call(this),d()(a()(r),"ButtonClicked",void 0),d()(a()(r),"Domain",void 0),r.ButtonClicked=t,r.Domain=n,r}return i}(i(58).a);e.a=M},function(t,e,i){"use strict";e.a=function(t){return null!=t&&"object"==typeof t}},function(t,e,i){"use strict";i.d(e,"a",(function(){return o}));var n=i(10);class o{constructor(t,e,i,n){this.r=t,this.g=e,this.b=i,this.a="number"!=typeof n||isNaN(n)?1:n}static fromObject(t){return!t||isNaN(t.r)||isNaN(t.g)||isNaN(t.b)?null:new o(t.r,t.g,t.b,t.a)}equalValue(t){return this.r===t.r&&this.g===t.g&&this.b===t.b&&this.a===t.a}toStringHexRGB(){return"#"+[this.r,this.g,this.b].map(this.formatHexValue).join("")}toStringHexRGBA(){return this.toStringHexRGB()+this.formatHexValue(this.a)}toStringHexARGB(){return"#"+[this.a,this.r,this.g,this.b].map(this.formatHexValue).join("")}toStringWebRGB(){return`rgb(${Math.round(Object(n.c)(this.r,0,255))},${Math.round(Object(n.c)(this.g,0,255))},${Math.round(Object(n.c)(this.b,0,255))})`}toStringWebRGBA(){return`rgba(${Math.round(Object(n.c)(this.r,0,255))},${Math.round(Object(n.c)(this.g,0,255))},${Math.round(Object(n.c)(this.b,0,255))},${Object(n.a)(this.a,0,1)})`}roundToPrecision(t){return new o(Object(n.g)(this.r,t),Object(n.g)(this.g,t),Object(n.g)(this.b,t),Object(n.g)(this.a,t))}clamp(){return new o(Object(n.a)(this.r,0,1),Object(n.a)(this.g,0,1),Object(n.a)(this.b,0,1),Object(n.a)(this.a,0,1))}toObject(){return{r:this.r,g:this.g,b:this.b,a:this.a}}formatHexValue(t){return Object(n.d)(Object(n.c)(t,0,255))}}},function(t,e,i){"use strict";i.d(e,"a",(function(){return r})),i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return s}));var n=i(93),o=i(75);class r{handleStartContentChange(){this.startContainer.classList.toggle("start",this.start.assignedNodes().length>0)}handleEndContentChange(){this.endContainer.classList.toggle("end",this.end.assignedNodes().length>0)}}const a=n.a`
    <span part="end" ${Object(o.a)("endContainer")}>
        <slot
            name="end"
            ${Object(o.a)("end")}
            @slotchange="${t=>t.handleEndContentChange()}"
        ></slot>
    </span>
`,s=n.a`
    <span part="start" ${Object(o.a)("startContainer")}>
        <slot
            name="start"
            ${Object(o.a)("start")}
            @slotchange="${t=>t.handleStartContentChange()}"
        ></slot>
    </span>
`},function(t,e,i){"use strict";var n=i(64),o="object"==typeof self&&self&&self.Object===Object&&self,r=n.a||o||Function("return this")();e.a=r},function(t,e,i){"use strict";var n=i(40),o=Object.prototype,r=o.hasOwnProperty,a=o.toString,s=n.a?n.a.toStringTag:void 0;var c=function(t){var e=r.call(t,s),i=t[s];try{t[s]=void 0;var n=!0}catch(t){}var o=a.call(t);return n&&(e?t[s]=i:delete t[s]),o},l=Object.prototype.toString;var u=function(t){return l.call(t)},A=n.a?n.a.toStringTag:void 0;e.a=function(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":A&&A in Object(t)?c(t):u(t)}},,function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"a",(function(){return r}));var n=i(106);const o="#FFFFFF",r="#000000",a={steps:94,clipLight:0,clipDark:0};Object.assign({},a),Object.assign(Object.assign({},a),{baseColor:Object(n.d)("#0078D4")})},function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return a}));var n=i(18);class o{constructor(){this.targetIndex=0}}class r extends o{constructor(){super(...arguments),this.createPlaceholder=n.a.createInterpolationPlaceholder}}class a extends o{constructor(t,e,i){super(),this.name=t,this.behavior=e,this.options=i}createPlaceholder(t){return n.a.createCustomAttributePlaceholder(this.name,t)}createBehavior(t){return new this.behavior(t,this.options)}}},function(t,e,i){"use strict";var n=i(35).a.Symbol;e.a=n},function(t,e,i){"use strict";e.a=function(t,e){return t===e||t!=t&&e!=e}},function(t,e,i){"use strict";var n=i(51),o=i(67);e.a=function(t){return null!=t&&Object(o.a)(t.length)&&!Object(n.a)(t)}},function(t,e,i){"use strict";var n=function(){this.__data__=[],this.size=0},o=i(41);var r=function(t,e){for(var i=t.length;i--;)if(Object(o.a)(t[i][0],e))return i;return-1},a=Array.prototype.splice;var s=function(t){var e=this.__data__,i=r(e,t);return!(i<0)&&(i==e.length-1?e.pop():a.call(e,i,1),--this.size,!0)};var c=function(t){var e=this.__data__,i=r(e,t);return i<0?void 0:e[i][1]};var l=function(t){return r(this.__data__,t)>-1};var u=function(t,e){var i=this.__data__,n=r(i,t);return n<0?(++this.size,i.push([t,e])):i[n][1]=e,this};function A(t){var e=-1,i=null==t?0:t.length;for(this.clear();++e<i;){var n=t[e];this.set(n[0],n[1])}}A.prototype.clear=n,A.prototype.delete=s,A.prototype.get=c,A.prototype.has=l,A.prototype.set=u;e.a=A},,function(t,e,i){"use strict";function n(t,...e){e.forEach(e=>{Object.getOwnPropertyNames(e.prototype).forEach(i=>{Object.defineProperty(t.prototype,i,Object.getOwnPropertyDescriptor(e.prototype,i))})})}i.d(e,"a",(function(){return n}))},,function(t,e,i){"use strict";var n;i.d(e,"a",(function(){return n})),function(t){t.ImpressionId="ImpressionId",t.DomainName="DomainName",t.Client="Client"}(n||(n={}))},,function(t,e,i){"use strict";var n=Array.isArray;e.a=n},function(t,e,i){"use strict";var n=i(7),o=i.n(n),r=i(14),a=i.n(r),s=i(0),c=i.n(s),l=function(){function t(){o()(this,t),c()(this,"name",void 0),c()(this,"supported",void 0)}return a()(t,null,[{key:"Create",value:function(e){var i=[];return e.forEach((function(e){var n=new t;n.name=e.name,n.supported=!1,Object.values(t.ExperimentNames).includes(e.name)&&(n.supported=!0),i.push(n)})),i}}]),t}();c()(l,"ExperimentNames",{expandAtCheckout:"msShoppingExp1",hideEmptyPCIllustration:"msShoppingExp5",hideMainIllustration:"msShoppingExp0",showShoppingEdgeLogo:"msShoppingExp2",showSimilarProducts:"msShoppingExp3"}),e.a=l},function(t,e,i){"use strict";var n=i(36),o=i(26);e.a=function(t){if(!Object(o.a)(t))return!1;var e=Object(n.a)(t);return"[object Function]"==e||"[object GeneratorFunction]"==e||"[object AsyncFunction]"==e||"[object Proxy]"==e}},function(t,e,i){"use strict";i.d(e,"a",(function(){return a}));var n=i(27),o=i(2),r=i(6);function a(t,e,i,a,s,c){return l=>{const u=Object(n.a)(t,l),A=Object(r.e)(l)?-1:1,g=Object(r.h)(o.k)(u)(r.g)(()=>A)(Object(r.f)(Object(n.a)(e,l)))(l),h=Object(r.c)(t,g)(l),d=Object(n.a)(i,l),I=Object(n.a)(a,l),M=Object(n.a)(s,l),C=Object(n.a)(c,l);return function(t,e,i,n,o,a,s){const c=t+i*Math.abs(n-o),l=1===i?n<o:i*n>i*o,u=l?t:c,A=l?c:t,g=u+i*a,h=u+i*s;return{rest:Object(r.d)(u,e),hover:Object(r.d)(A,e),active:Object(r.d)(g,e),focus:Object(r.d)(h,e)}}(h,u,A,d,I,M,C)}}},function(t,e,i){"use strict";e.a=function(t){return t}},function(t,e,i){"use strict";var n=Object.prototype;e.a=function(t){var e=t&&t.constructor;return t===("function"==typeof e&&e.prototype||n)}},function(t,e,i){"use strict";var n,o=i(51),r=i(35).a["__core-js_shared__"],a=(n=/[^.]+$/.exec(r&&r.keys&&r.keys.IE_PROTO||""))?"Symbol(src)_1."+n:"";var s=function(t){return!!a&&a in t},c=i(26),l=Function.prototype.toString;var u=function(t){if(null!=t){try{return l.call(t)}catch(t){}try{return t+""}catch(t){}}return""},A=/^\[object .+?Constructor\]$/,g=Function.prototype,h=Object.prototype,d=g.toString,I=h.hasOwnProperty,M=RegExp("^"+d.call(I).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");var C=function(t){return!(!Object(c.a)(t)||s(t))&&(Object(o.a)(t)?M:A).test(u(t))};var p=function(t,e){return null==t?void 0:t[e]};e.a=function(t,e){var i=p(t,e);return C(i)?i:void 0}},function(t,e,i){"use strict";i.d(e,"a",(function(){return o}));class n extends class{constructor(t){this.listenerCache=new WeakMap,this.query=t}bind(t){const{query:e}=this,i=this.constructListener(t);i.bind(e)(),e.addListener(i),this.listenerCache.set(t,i)}unbind(t){const e=this.listenerCache.get(t);e&&(this.query.removeListener(e),this.listenerCache.delete(t))}}{constructor(t,e){super(t),this.styles=e}static with(t){return e=>new n(t,e)}constructListener(t){let e=!1;const i=this.styles;return function(){const{matches:n}=this;n&&!e?(t.$fastController.addStyles(i),e=n):!n&&e&&(t.$fastController.removeStyles(i),e=n)}}unbind(t){super.unbind(t),t.$fastController.removeStyles(this.styles)}}const o=n.with(window.matchMedia("(forced-colors)"))},function(t,e,i){"use strict";var n=i(7),o=i.n(n),r=i(14),a=i.n(r),s=i(0),c=i.n(s),l=i(3),u=function(){function t(){o()(this,t),c()(this,"couponCode",void 0),c()(this,"title",void 0),c()(this,"attribution",void 0)}return a()(t,null,[{key:"Create",value:function(e){var i=new t;return l.k.currentDevice===l.b.Mobile?(i.couponCode=e.couponCode,i.title=e.title,i.attribution=e.attribution):(i.couponCode=e.coupon_code,i.title=e.title,i.attribution=e.attribution),i}}]),t}();function A(t,e){var i;if("undefined"==typeof Symbol||null==t[Symbol.iterator]){if(Array.isArray(t)||(i=function(t,e){if(!t)return;if("string"==typeof t)return g(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);"Object"===i&&t.constructor&&(i=t.constructor.name);if("Map"===i||"Set"===i)return Array.from(t);if("Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return g(t,e)}(t))||e&&t&&"number"==typeof t.length){i&&(t=i);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var r,a=!0,s=!1;return{s:function(){i=t[Symbol.iterator]()},n:function(){var t=i.next();return a=t.done,t},e:function(t){s=!0,r=t},f:function(){try{a||null==i.return||i.return()}finally{if(s)throw r}}}}function g(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=new Array(e);i<e;i++)n[i]=t[i];return n}var h=function(){function t(){o()(this,t),c()(this,"IsAutoApplyAvailable",void 0),c()(this,"IsCheckoutPage",void 0),c()(this,"Coupons",void 0)}return a()(t,null,[{key:"Create",value:function(e,i,n){var o=new t;o.IsAutoApplyAvailable=i,o.IsCheckoutPage=n,o.Coupons=[];var r,a=A(e);try{for(a.s();!(r=a.n()).done;){var s=r.value;o.Coupons.push(u.Create(s))}}catch(t){a.e(t)}finally{a.f()}return o}}]),t}();c()(h,"resources",{checkPriceComparison:"data:image/svg+xml;base64,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",couponCopied:"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNiIg\n                       aGVpZ2h0PSIxNiIgdmlld0JveD0iMCAwIDE2IDE2IiBmaWxsPSJub25lIj4KICAgICAgICA8cGF0aCBkPSJNOCAwQzguNz\n                       M0MzggMCA5LjQ0MjcxIDAuMDk2MzU0MiAxMC4xMjUgMC4yODkwNjJDMTAuODA3MyAwLjQ3NjU2MiAxMS40NDI3IDAuNzQ0\n                       NzkyIDEyLjAzMTIgMS4wOTM3NUMxMi42MjUgMS40NDI3MSAxMy4xNjQxIDEuODYxOTggMTMuNjQ4NCAyLjM1MTU2QzE0Lj\n                       EzOCAyLjgzNTk0IDE0LjU1NzMgMy4zNzUgMTQuOTA2MiAzLjk2ODc1QzE1LjI1NTIgNC41NTcyOSAxNS41MjM0IDUuMTky\n                       NzEgMTUuNzEwOSA1Ljg3NUMxNS45MDM2IDYuNTU3MjkgMTYgNy4yNjU2MiAxNiA4QzE2IDguNzM0MzggMTUuOTAzNiA5Lj\n                       Q0MjcxIDE1LjcxMDkgMTAuMTI1QzE1LjUyMzQgMTAuODA3MyAxNS4yNTUyIDExLjQ0NTMgMTQuOTA2MiAxMi4wMzkxQzE0\n                       LjU1NzMgMTIuNjI3NiAxNC4xMzggMTMuMTY2NyAxMy42NDg0IDEzLjY1NjJDMTMuMTY0MSAxNC4xNDA2IDEyLjYyNSAxNC\n                       41NTczIDEyLjAzMTIgMTQuOTA2MkMxMS40NDI3IDE1LjI1NTIgMTAuODA3MyAxNS41MjYgMTAuMTI1IDE1LjcxODhDOS40\n                       NDI3MSAxNS45MDYyIDguNzM0MzggMTYgOCAxNkM3LjI2NTYyIDE2IDYuNTU3MjkgMTUuOTA2MiA1Ljg3NSAxNS43MTg4Qz\n                       UuMTkyNzEgMTUuNTI2IDQuNTU0NjkgMTUuMjU1MiAzLjk2MDk0IDE0LjkwNjJDMy4zNzI0IDE0LjU1NzMgMi44MzMzMyAx\n                       NC4xNDA2IDIuMzQzNzUgMTMuNjU2MkMxLjg1OTM4IDEzLjE2NjcgMS40NDI3MSAxMi42Mjc2IDEuMDkzNzUgMTIuMDM5MU\n                       MwLjc0NDc5MiAxMS40NDUzIDAuNDczOTU4IDEwLjgwNzMgMC4yODEyNSAxMC4xMjVDMC4wOTM3NSA5LjQ0MjcxIDAgOC43\n                       MzQzOCAwIDhDMCA3LjI2NTYyIDAuMDkzNzUgNi41NTcyOSAwLjI4MTI1IDUuODc1QzAuNDczOTU4IDUuMTkyNzEgMC43ND\n                       Q3OTIgNC41NTcyOSAxLjA5Mzc1IDMuOTY4NzVDMS40NDI3MSAzLjM3NSAxLjg1OTM4IDIuODM1OTQgMi4zNDM3NSAyLjM1\n                       MTU2QzIuODMzMzMgMS44NjE5OCAzLjM3MjQgMS40NDI3MSAzLjk2MDk0IDEuMDkzNzVDNC41NTQ2OSAwLjc0NDc5MiA1Lj\n                       E5MjcxIDAuNDc2NTYyIDUuODc1IDAuMjg5MDYyQzYuNTU3MjkgMC4wOTYzNTQyIDcuMjY1NjIgMCA4IDBaTTEyLjE3OTcg\n                       NS44ODI4MUMxMi40NzMxIDUuNTg5NDEgMTIuNDczMSA1LjExMzcxIDEyLjE3OTcgNC44MjAzMUMxMS44ODYzIDQuNTI2OT\n                       EgMTEuNDEwNiA0LjUyNjkxIDExLjExNzIgNC44MjAzMUw2LjUgOS40Mzc1TDQuODgyODEgNy44MjAzMUM0LjU4OTQxIDcu\n                       NTI2OTEgNC4xMTM3MSA3LjUyNjkxIDMuODIwMzEgNy44MjAzMUMzLjUyNjkxIDguMTEzNzEgMy41MjY5MSA4LjU4OTQxID\n                       MuODIwMzEgOC44ODI4MUw2LjUgMTEuNTYyNUwxMi4xNzk3IDUuODgyODFaIiBmaWxsPSIjOEJCNzNBIi8+CiAgICAgICAgPC9zdmc+",couponsEmpty:"data:image/svg+xml;base64,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",couponsFound:"data:image/gif;base64,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",couponsFoundDark:'<svg xmlns="http://www.w3.org/2000/svg" width="308" height="110" viewBox="0 0 308 110" fill="none">\n    <g clip-path="url(#clip0)">\n    <path d="M4.18213 109.26C4.18213 109.26 135.542 8.77995 194.082 0.919947C262.062 -8.21005 196.872 106.4 258.762 76.96C309.212 52.96 313.062 91.0299 290.632 109.25H4.18213V109.26Z" fill="#3B3B3B"/>\n    <path d="M243.672 41.8906H246.672V44.8906" stroke="#484848" stroke-linecap="round" stroke-linejoin="round"/>\n    <path d="M246.672 51.9307V76.5707" stroke="#484848" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="7.04 7.04"/>\n    <path d="M246.672 80.0908V83.0908H243.672" stroke="#484848" stroke-linecap="round" stroke-linejoin="round"/>\n    <path d="M237.802 83.0908H158.562" stroke="#484848" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="5.87 5.87"/>\n    <path d="M155.632 83.0908H152.632V80.0908" stroke="#484848" stroke-linecap="round" stroke-linejoin="round"/>\n    <path d="M152.632 73.0502V48.4102" stroke="#484848" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="7.04 7.04"/>\n    <path d="M152.632 44.8906V41.8906H155.632" stroke="#484848" stroke-linecap="round" stroke-linejoin="round"/>\n    <path d="M161.502 41.8906H240.742" stroke="#484848" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="5.87 5.87"/>\n    <path d="M251.792 37.8203V78.0203C251.792 78.5803 251.392 79.0203 250.912 79.0203H157.822C157.332 79.0203 156.942 78.5803 156.942 78.0203V37.8203C156.942 37.2703 157.332 36.8203 157.822 36.8203H250.912C251.392 36.8203 251.792 37.2703 251.792 37.8203Z" fill="#FFA726"/>\n    <path d="M251.792 57.5603V78.0203C251.792 78.5803 251.392 79.0203 250.912 79.0203H157.822C157.332 79.0203 156.942 78.5803 156.942 78.0203V60.8303C164.952 53.1603 174.602 44.3803 184.102 36.8203H228.642C229.352 52.7303 225.952 71.8603 240.962 61.9103C245.462 58.9303 249.042 57.6403 251.792 57.5603Z" fill="url(#paint0_linear)"/>\n    <path d="M251.792 46.48V49.66C250.532 49.94 248.552 50.03 246.822 50.99C243.552 52.81 242.462 57.07 240.902 54.29C239.352 51.5 240.742 47.77 244.012 45.95C246.742 44.43 249.912 44.72 251.792 46.48Z" fill="url(#paint1_linear)"/>\n    <path d="M104.302 108.791C104.302 108.791 120.032 70.0705 115.712 60.2105C113.002 54.0205 108.602 58.5405 106.342 62.2805C103.812 66.4805 99.242 61.6905 103.022 54.8505C105.822 49.7805 108.182 40.9405 104.292 38.3005C100.412 35.6605 97.382 41.0805 94.152 38.8505C91.212 36.8205 94.112 31.0305 93.392 27.3205C92.212 21.2805 85.152 22.0305 83.012 25.9905C79.532 32.4405 87.872 35.8405 86.122 39.7105C84.452 43.4005 81.942 40.4605 79.082 39.9605C75.022 39.2605 73.032 44.4205 75.752 48.6905C79.622 54.7705 83.452 51.4905 84.462 55.7505C85.472 60.0105 81.952 61.8405 78.442 60.9105C72.212 59.2505 69.162 67.2305 75.012 75.3305C85.672 90.0805 91.512 108.781 91.512 108.781H104.302V108.791Z" fill="#2286C3"/>\n    <path d="M99.9321 90.0907C99.9321 90.0907 107.442 72.3407 115.712 60.2207" stroke="#3B3B3B" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>\n    <path d="M86.5322 23.2002C86.5322 23.2002 99.1422 72.8402 101.942 108.79" stroke="#3B3B3B" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>\n    <path d="M82.1719 28.751C82.1719 28.751 86.6119 31.921 89.1719 34.161" stroke="#3B3B3B" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>\n    <path d="M91.5122 44.6106C91.5122 44.6106 92.7622 41.4906 94.1622 38.8506" stroke="#3B3B3B" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>\n    <path d="M92.9322 51.5106C92.9322 51.5106 85.0522 47.4006 74.5122 43.8506" stroke="#3B3B3B" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>\n    <path d="M94.9619 61.0608C94.9619 61.0608 97.8319 50.3908 104.302 38.3008" stroke="#3B3B3B" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>\n    <path d="M83.6621 59.8203C83.6621 59.8203 90.6021 63.0403 95.9721 67.3403" stroke="#3B3B3B" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>\n    <path d="M102.772 63.2607C102.772 63.2607 99.8719 67.6907 97.4019 74.6207" stroke="#3B3B3B" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>\n    <path d="M98.8521 83.3702C98.8521 83.3702 83.8221 72.7202 72.1621 67.9902" stroke="#3B3B3B" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>\n    <path d="M113.102 73.4101L115.082 68.6201L119.822 70.7401L117.842 75.5301L113.102 73.4101Z" fill="#9BE7FF"/>\n    <path d="M127.952 65.9997L124.492 66.9497L123.562 63.3797L127.022 62.4297L127.952 65.9997Z" fill="#FFA726"/>\n    <path d="M130.032 72.1397L128.812 70.6797L127.402 71.8597L128.622 73.3197L130.032 72.1397Z" fill="#0A3358"/>\n    <path d="M121.432 54.8398L119.822 56.1798L121.202 57.8298L122.812 56.4898L121.432 54.8398Z" fill="#64B5F6"/>\n    <path d="M122.942 46.5605L123.432 47.7805L124.692 47.2805L124.202 46.0605L122.942 46.5605Z" fill="#FFA726"/>\n    <path d="M128.082 51.8006L129.892 49.6406L131.972 51.4506L130.172 53.6106L128.082 51.8006Z" fill="#484848"/>\n    <path d="M136.612 109.07H100.262L105.362 73.2297C105.402 72.9397 105.652 72.7197 105.952 72.7197H131.432L136.612 109.07Z" fill="#64B5F6"/>\n    <path d="M135.142 102.06H143.502L144.482 109.07H135.142V102.06Z" fill="#FFB677"/>\n    <path d="M144.492 109.079L141.252 106.219L140.232 105.319L139.992 105.109L139.772 105.369L139.062 106.199V106.219L136.612 109.079L131.432 72.7295L135.522 75.6795L135.682 75.7995L135.902 75.9595L136.142 75.7195L136.382 75.4995L138.532 73.5095C138.882 73.1895 139.452 73.3895 139.522 73.8595L144.492 109.079Z" fill="#9BE7FF"/>\n    <path d="M140.682 108.56L140.212 108.63L139.762 105.37L135.672 75.7997L135.902 75.9597L136.142 75.7197L140.232 105.32L140.682 108.56Z" fill="#0A3358"/>\n    <path d="M107.922 78.1299C107.922 78.1299 113.802 103.64 127.952 78.1299" stroke="#9BE7FF" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>\n    <path d="M149.762 109.08H130.382L135.302 90.1402C135.372 89.8802 135.602 89.7002 135.872 89.7002H147.002L149.762 109.08Z" fill="#2286C3"/>\n    <path d="M153.942 109.18L149.762 109.08L147.002 89.7002H147.572L153.942 109.18Z" fill="#9BE7FF"/>\n    <path d="M136.622 92.1699C136.622 92.1699 136.332 98.4599 139.762 98.5999C143.192 98.7399 145.472 92.5499 145.472 92.5499" stroke="#9BE7FF" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>\n    <path d="M97.6921 98.8804C98.9821 102.34 101.302 104.04 102.352 104.66C102.642 104.83 103.002 104.69 103.102 104.37C103.462 103.22 104.022 100.44 102.652 97.0104C101.652 94.5104 97.4121 88.9604 95.1021 86.0204C94.7521 85.5704 94.0321 85.9204 94.1721 86.4704C95.0821 89.9704 96.7621 96.3804 97.6921 98.8804Z" fill="#9BE7FF"/>\n    <path d="M103.532 109.26C103.532 109.26 102.782 103.48 100.982 99.0002C99.7823 96.0102 98.3623 93.6602 98.3623 93.6602" stroke="#2286C3" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>\n    <path d="M255.812 36.4897C256.092 35.6397 256.762 34.9797 257.612 34.6897L258.602 34.3597C258.692 34.3297 258.752 34.2497 258.752 34.1497C258.752 34.0497 258.692 33.9697 258.602 33.9397L258.032 33.7497C256.922 33.3797 256.042 32.5097 255.672 31.3997L255.482 30.8297C255.452 30.7397 255.372 30.6797 255.272 30.6797C255.182 30.6797 255.092 30.7397 255.062 30.8297L254.872 31.4097C254.502 32.5197 253.632 33.3897 252.522 33.7597L251.952 33.9497C251.862 33.9797 251.802 34.0597 251.802 34.1597C251.802 34.2497 251.862 34.3397 251.952 34.3697L252.532 34.5597C253.642 34.9297 254.512 35.7997 254.882 36.9097L255.072 37.4897C255.102 37.5797 255.182 37.6397 255.282 37.6397C255.382 37.6397 255.462 37.5797 255.492 37.4897L255.812 36.4897Z" fill="url(#paint2_linear)"/>\n    <path d="M144.962 72.7504C145.212 72.0004 145.802 71.4104 146.552 71.1604L147.432 70.8704C147.512 70.8404 147.562 70.7704 147.562 70.6904C147.562 70.6104 147.512 70.5304 147.432 70.5004L146.932 70.3304C145.942 70.0004 145.172 69.2304 144.842 68.2504L144.672 67.7404C144.642 67.6604 144.572 67.6104 144.492 67.6104C144.412 67.6104 144.332 67.6604 144.312 67.7404L144.142 68.2504C143.812 69.2304 143.042 70.0004 142.062 70.3304L141.552 70.5004C141.472 70.5304 141.422 70.6004 141.422 70.6904C141.422 70.7704 141.472 70.8504 141.552 70.8704L142.062 71.0404C143.042 71.3704 143.812 72.1404 144.142 73.1204L144.312 73.6304C144.342 73.7104 144.412 73.7604 144.492 73.7604C144.582 73.7604 144.652 73.7104 144.672 73.6304L144.962 72.7504Z" fill="url(#paint3_linear)"/>\n    <path d="M153.962 90.3503C154.102 89.9403 154.412 89.6303 154.822 89.4903L155.292 89.3303C155.332 89.3203 155.362 89.2803 155.362 89.2303C155.362 89.1803 155.332 89.1403 155.292 89.1303L155.022 89.0403C154.492 88.8603 154.072 88.4503 153.892 87.9103L153.802 87.6403C153.792 87.6003 153.752 87.5703 153.702 87.5703C153.662 87.5703 153.622 87.6003 153.602 87.6403L153.512 87.9203C153.332 88.4503 152.922 88.8703 152.382 89.0403L152.112 89.1303C152.072 89.1403 152.042 89.1803 152.042 89.2303C152.042 89.2703 152.072 89.3103 152.112 89.3303L152.392 89.4203C152.922 89.6003 153.342 90.0103 153.522 90.5503L153.612 90.8303C153.622 90.8703 153.662 90.9003 153.712 90.9003C153.762 90.9003 153.802 90.8703 153.812 90.8303L153.962 90.3503Z" fill="url(#paint4_linear)"/>\n    <path fill-rule="evenodd" clip-rule="evenodd" d="M170.842 51.9301C169.742 51.6401 168.622 52.2901 168.332 53.3501C168.042 54.4201 168.682 55.5301 169.782 55.8201C170.882 56.1101 172.002 55.4601 172.292 54.4001C172.582 53.3301 171.942 52.2201 170.842 51.9301ZM166.352 52.8301C166.942 50.6501 169.202 49.3701 171.382 49.9501C173.562 50.5301 174.872 52.7501 174.282 54.9301C173.692 57.1101 171.432 58.3901 169.252 57.8101C167.072 57.2301 165.762 55.0101 166.352 52.8301Z" fill="#2286C3"/>\n    <path fill-rule="evenodd" clip-rule="evenodd" d="M180.832 59.3598C179.732 59.0698 178.612 59.7198 178.322 60.7798C178.032 61.8498 178.672 62.9598 179.772 63.2498C180.872 63.5398 181.992 62.8898 182.282 61.8298C182.572 60.7698 181.932 59.6498 180.832 59.3598ZM176.342 60.2598C176.932 58.0798 179.192 56.7998 181.372 57.3798C183.552 57.9598 184.862 60.1798 184.272 62.3598C183.682 64.5398 181.422 65.8198 179.242 65.2398C177.062 64.6598 175.752 62.4398 176.342 60.2598Z" fill="#2286C3"/>\n    <path fill-rule="evenodd" clip-rule="evenodd" d="M182.342 51.0203C182.702 51.4503 182.662 52.1203 182.242 52.5003L168.992 64.7003C168.572 65.0803 167.942 65.0403 167.582 64.6103C167.222 64.1803 167.262 63.5103 167.682 63.1303L180.932 50.9303C181.342 50.5403 181.982 50.5803 182.342 51.0203Z" fill="#2286C3"/>\n    <path d="M236.942 58.2904H191.582C191.002 58.2904 190.532 57.8204 190.532 57.2404C190.532 56.6604 191.002 56.1904 191.582 56.1904H236.942C237.522 56.1904 237.992 56.6604 237.992 57.2404C237.992 57.8204 237.522 58.2904 236.942 58.2904Z" fill="#2286C3"/>\n    <path d="M216.172 64.6703H191.572C190.992 64.6703 190.522 64.2003 190.522 63.6203C190.522 63.0403 190.992 62.5703 191.572 62.5703H216.172C216.752 62.5703 217.222 63.0403 217.222 63.6203C217.222 64.2003 216.752 64.6703 216.172 64.6703Z" fill="#2286C3"/>\n    <path d="M222.692 51.9203H191.582C191.002 51.9203 190.532 51.4503 190.532 50.8703C190.532 50.2903 191.002 49.8203 191.582 49.8203H222.692C223.272 49.8203 223.742 50.2903 223.742 50.8703C223.742 51.4503 223.272 51.9203 222.692 51.9203Z" fill="#2286C3"/>\n    <path d="M243.152 51.9203H228.082C227.502 51.9203 227.032 51.4503 227.032 50.8703C227.032 50.2903 227.502 49.8203 228.082 49.8203H243.152C243.732 49.8203 244.202 50.2903 244.202 50.8703C244.202 51.4503 243.732 51.9203 243.152 51.9203Z" fill="#2286C3"/>\n    <path d="M244.202 73.2998V75.4598" stroke="#2286C3" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>\n    <path d="M241.142 73.2998V75.4598" stroke="#2286C3" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>\n    <path d="M238.072 73.2998V75.4598" stroke="#2286C3" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>\n    <path d="M235.012 73.2998V75.4598" stroke="#2286C3" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>\n    <path d="M231.942 73.2998V75.4598" stroke="#2286C3" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>\n    <path d="M228.882 73.2998V75.4598" stroke="#2286C3" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>\n    <path d="M225.812 73.2998V75.4598" stroke="#2286C3" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>\n    <path d="M222.752 73.2998V75.4598" stroke="#2286C3" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>\n    <path d="M219.682 73.2998V75.4598" stroke="#2286C3" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>\n    <path d="M216.622 73.2998V75.4598" stroke="#2286C3" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>\n    <path d="M0.521973 109.26H307.362" stroke="#3B3B3B" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>\n    </g>\n    <defs>\n    <linearGradient id="paint0_linear" x1="168.5" y1="71" x2="197.483" y2="24.3792" gradientUnits="userSpaceOnUse">\n    <stop stop-color="#FFA726"/>\n    <stop offset="0.9935" stop-color="#FFECB3"/>\n    </linearGradient>\n    <linearGradient id="paint1_linear" x1="245.25" y1="57.125" x2="246.343" y2="43.1207" gradientUnits="userSpaceOnUse">\n    <stop stop-color="#FFA726"/>\n    <stop offset="0.9935" stop-color="#FFECB3"/>\n    </linearGradient>\n    <linearGradient id="paint2_linear" x1="251.65" y1="37.8444" x2="256.256" y2="33.1762" gradientUnits="userSpaceOnUse">\n    <stop stop-color="#FFA726"/>\n    <stop offset="0.9935" stop-color="#FFECB3"/>\n    </linearGradient>\n    <linearGradient id="paint3_linear" x1="141.288" y1="73.9413" x2="145.358" y2="69.8171" gradientUnits="userSpaceOnUse">\n    <stop stop-color="#FFA726"/>\n    <stop offset="0.9935" stop-color="#FFECB3"/>\n    </linearGradient>\n    <linearGradient id="paint4_linear" x1="151.969" y1="90.9983" x2="154.173" y2="88.7682" gradientUnits="userSpaceOnUse">\n    <stop stop-color="#FFA726"/>\n    <stop offset="0.9935" stop-color="#FFECB3"/>\n    </linearGradient>\n    <clipPath id="clip0">\n    <rect width="307.84" height="109.35" fill="white" transform="translate(0.0219727 0.410156)"/>\n    </clipPath>\n    </defs>\n    </svg>'});e.a=h},function(t,e,i){"use strict";var n=i(7),o=i.n(n),r=i(14),a=i.n(r),s=function(){function t(){o()(this,t)}return a()(t,[{key:"ToString",value:function(){return JSON.stringify(this)}}]),t}();e.a=s},function(t,e,i){"use strict";function n(t){const e=this.spillover;-1===e.indexOf(t)&&e.push(t)}function o(t){const e=this.spillover,i=e.indexOf(t);-1!==i&&e.splice(i,1)}function r(t){const e=this.spillover,i=this.source;for(let n=0,o=e.length;n<o;++n)e[n].handleChange(i,t)}function a(t){return-1!==this.spillover.indexOf(t)}i.d(e,"b",(function(){return s})),i.d(e,"a",(function(){return c}));class s{constructor(t,e){this.sub1=void 0,this.sub2=void 0,this.spillover=void 0,this.source=t,this.sub1=e}has(t){return this.sub1===t||this.sub2===t}subscribe(t){this.has(t)||(void 0!==this.sub1?void 0!==this.sub2?(this.spillover=[this.sub1,this.sub2,t],this.subscribe=n,this.unsubscribe=o,this.notify=r,this.has=a,this.sub1=void 0,this.sub2=void 0):this.sub2=t:this.sub1=t)}unsubscribe(t){this.sub1===t?this.sub1=void 0:this.sub2===t&&(this.sub2=void 0)}notify(t){const e=this.sub1,i=this.sub2,n=this.source;void 0!==e&&e.handleChange(n,t),void 0!==i&&i.handleChange(n,t)}}class c{constructor(t){this.subscribers={},this.source=t}notify(t){const e=this.subscribers[t];void 0!==e&&e.notify(t)}subscribe(t,e){let i=this.subscribers[e];void 0===i&&(this.subscribers[e]=i=new s(this.source)),i.subscribe(t)}unsubscribe(t,e){const i=this.subscribers[e];void 0!==i&&i.unsubscribe(t)}}},function(t,e,i){"use strict";var n;i.d(e,"a",(function(){return n})),function(t){t.menuitem="menuitem",t.menuitemcheckbox="menuitemcheckbox",t.menuitemradio="menuitemradio"}(n||(n={}))},function(t,e,i){"use strict";var n=i(55),o=Object(n.a)(Object,"create");var r=function(){this.__data__=o?o(null):{},this.size=0};var a=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e},s=Object.prototype.hasOwnProperty;var c=function(t){var e=this.__data__;if(o){var i=e[t];return"__lodash_hash_undefined__"===i?void 0:i}return s.call(e,t)?e[t]:void 0},l=Object.prototype.hasOwnProperty;var u=function(t){var e=this.__data__;return o?void 0!==e[t]:l.call(e,t)};var A=function(t,e){var i=this.__data__;return this.size+=this.has(t)?0:1,i[t]=o&&void 0===e?"__lodash_hash_undefined__":e,this};function g(t){var e=-1,i=null==t?0:t.length;for(this.clear();++e<i;){var n=t[e];this.set(n[0],n[1])}}g.prototype.clear=r,g.prototype.delete=a,g.prototype.get=c,g.prototype.has=u,g.prototype.set=A;var h=g,d=i(43),I=i(65);var M=function(){this.size=0,this.__data__={hash:new h,map:new(I.a||d.a),string:new h}};var C=function(t){var e=typeof t;return"string"==e||"number"==e||"symbol"==e||"boolean"==e?"__proto__"!==t:null===t};var p=function(t,e){var i=t.__data__;return C(e)?i["string"==typeof e?"string":"hash"]:i.map};var f=function(t){var e=p(this,t).delete(t);return this.size-=e?1:0,e};var D=function(t){return p(this,t).get(t)};var y=function(t){return p(this,t).has(t)};var b=function(t,e){var i=p(this,t),n=i.size;return i.set(t,e),this.size+=i.size==n?0:1,this};function j(t){var e=-1,i=null==t?0:t.length;for(this.clear();++e<i;){var n=t[e];this.set(n[0],n[1])}}j.prototype.clear=M,j.prototype.delete=f,j.prototype.get=D,j.prototype.has=y,j.prototype.set=b;e.a=j},function(t,e,i){"use strict";var n=i(36),o=i(32);var r=function(t){return Object(o.a)(t)&&"[object Arguments]"==Object(n.a)(t)},a=Object.prototype,s=a.hasOwnProperty,c=a.propertyIsEnumerable,l=r(function(){return arguments}())?r:function(t){return Object(o.a)(t)&&s.call(t,"callee")&&!c.call(t,"callee")};e.a=l},,function(t,e,i){"use strict";(function(t){var i="object"==typeof t&&t&&t.Object===Object&&t;e.a=i}).call(this,i(82))},function(t,e,i){"use strict";var n=i(55),o=i(35),r=Object(n.a)(o.a,"Map");e.a=r},function(t,e,i){"use strict";e.a=function(t){return function(){return t}}},function(t,e,i){"use strict";e.a=function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=9007199254740991}},function(t,e,i){"use strict";var n=/^(?:0|[1-9]\d*)$/;e.a=function(t,e){var i=typeof t;return!!(e=null==e?9007199254740991:e)&&("number"==i||"symbol"!=i&&n.test(t))&&t>-1&&t%1==0&&t<e}},function(t,e,i){"use strict";(function(t){var n=i(35),o=i(88),r="object"==typeof exports&&exports&&!exports.nodeType&&exports,a=r&&"object"==typeof t&&t&&!t.nodeType&&t,s=a&&a.exports===r?n.a.Buffer:void 0,c=(s?s.isBuffer:void 0)||o.a;e.a=c}).call(this,i(79)(t))},function(t,e,i){"use strict";e.a=function(t,e){return function(i){return t(e(i))}}},function(t,e,i){"use strict";var n=i(36),o=i(67),r=i(32),a={};a["[object Float32Array]"]=a["[object Float64Array]"]=a["[object Int8Array]"]=a["[object Int16Array]"]=a["[object Int32Array]"]=a["[object Uint8Array]"]=a["[object Uint8ClampedArray]"]=a["[object Uint16Array]"]=a["[object Uint32Array]"]=!0,a["[object Arguments]"]=a["[object Array]"]=a["[object ArrayBuffer]"]=a["[object Boolean]"]=a["[object DataView]"]=a["[object Date]"]=a["[object Error]"]=a["[object Function]"]=a["[object Map]"]=a["[object Number]"]=a["[object Object]"]=a["[object RegExp]"]=a["[object Set]"]=a["[object String]"]=a["[object WeakMap]"]=!1;var s=function(t){return Object(r.a)(t)&&Object(o.a)(t.length)&&!!a[Object(n.a)(t)]};var c=function(t){return function(e){return t(e)}},l=i(80),u=l.a&&l.a.isTypedArray,A=u?c(u):s;e.a=A},function(t,e,i){"use strict";var n=function(t){return function(e,i,n){for(var o=-1,r=Object(e),a=n(e),s=a.length;s--;){var c=a[t?s:++o];if(!1===i(r[c],c,r))break}return e}}();e.a=n},function(t,e,i){"use strict";var n=function(t,e){for(var i=-1,n=Array(t);++i<t;)n[i]=e(i);return n},o=i(62),r=i(49),a=i(69),s=i(68),c=i(71),l=Object.prototype.hasOwnProperty;e.a=function(t,e){var i=Object(r.a)(t),u=!i&&Object(o.a)(t),A=!i&&!u&&Object(a.a)(t),g=!i&&!u&&!A&&Object(c.a)(t),h=i||u||A||g,d=h?n(t.length,String):[],I=d.length;for(var M in t)!e&&!l.call(t,M)||h&&("length"==M||A&&("offset"==M||"parent"==M)||g&&("buffer"==M||"byteLength"==M||"byteOffset"==M)||Object(s.a)(M,I))||d.push(M);return d}},function(t,e,i){"use strict";var n=i(7),o=i.n(n),r=i(14),a=i.n(r),s=i(0),c=i.n(s),l=i(31),u=i(4),A=i(3),g=i(25),h=function(){function t(){o()(this,t),c()(this,"styles","\n        ::part(header-title) {\n            font-size: 24px;\n            font-weight: 600;\n        }\n        ::part(header-sub-title) {\n            font-size: 14px;\n            font-weight: normal;\n        }\n        #btnStartShopping {\n            height: 36px;\n            width: 160px;\n            align-self: center;\n            background: #0078D4;\n            box-sizing: border-box;\n            border-radius: 2px;\n            color: #FFFFFF;\n            text-align: center;\n            font-weight:400;\n            cursor: pointer;\n        }\n\n        #anchorDismiss {\n            font-weight: 600;\n            text-align: center;\n            margin-left: 90px;\n            margin-right: 90px;\n            margin-bottom:28px;\n        }\n\n        #anchorSettings {\n            font-weight: 600;\n            display:inline;\n            margin-left:2px;\n        }\n    ")}return a()(t,[{key:"renderFREModule",value:function(t){(new A.k).applyStyles(t,this.styles);var e=document.createElement("DIV");A.k.svgStaticPolicy&&(e.innerHTML=A.k.svgStaticPolicy.createHTML(A.f.shoppingImage)),e.slot="illustration",e.style.marginBottom="20px",e.style.marginTop="10px",e.id="mainImage",e.setAttribute("aria-hidden","true"),t.appendChild(e),t.title=A.k.localizedStrings.freTitle,t.subTitle=A.k.localizedStrings.freSubTitle}},{key:"createStartShoppingButton",value:function(t,e,i){var n=document.createElement("fluent-button");n.style.marginBottom="22px",n.id="btnStartShopping",n.className+="footer-button txtSmall",n.appearance="accent",n.textContent=A.k.localizedStrings.freButton,e.appendChild(n),i===A.e.Keyboard&&n.setAttribute("autofocus",""),n.addEventListener("click",(function(e){if(A.k.currentDevice===A.b.Desktop){var i=new l.a(u.d.FREGotIt);g.a.GetLogModule().logClientEvent(u.h.Information,u.j.ButtonClick,"FRE Got it button, Flyout should open",i,t,A.g.FRE_GOT_IT),0===e.screenX&&0===e.screenY?t.startShopping("keyboard"):t.startShopping("mouse")}else{var n=window;A.k.isAndroidBridgeAvailable()?n.couponsUIWebViewBridge.agreeOnFRE():A.k.isIOSBridgeAvailable()&&n.webkit.messageHandlers.couponsFREWebViewBridge.postMessage("Agree")}}))}},{key:"createDismissAnchor",value:function(t,e){var i=document.createElement("fluent-button");if(i.className+="txtSmall",i.appearance="lightweight",i.style.alignSelf="center",i.textContent=A.k.localizedStrings.freDismiss,i.addEventListener("click",(function(){if(A.k.currentDevice===A.b.Desktop){var e=new l.a(u.d.FRENoThanks);g.a.GetLogModule().logClientEvent(u.h.Information,u.j.ButtonClick,"Edge shopping dismissed",e,t,A.g.FRE_NO_THANKS),t.dismissPopup()}else{var i=window;A.k.isAndroidBridgeAvailable()?i.couponsUIWebViewBridge.disagreeOnFRE():A.k.isIOSBridgeAvailable()&&i.webkit.messageHandlers.couponsFREWebViewBridge.postMessage("Disagree")}})),e.appendChild(i),A.k.currentDevice===A.b.Mobile){var n=document.createElement("fluent-anchor");n.href="#",n.className="txtSmall",n.appearance="lightweight",n.textContent=A.k.localizedStrings.commonMicrosoftPolicy,n.addEventListener("click",(function(){var t=window;A.k.isAndroidBridgeAvailable()?t.couponsUIWebViewBridge.visitURLNewTab("https://privacy.microsoft.com/en-us/privacystatement"):A.k.isIOSBridgeAvailable()&&t.webkit.messageHandlers.couponsUIWebViewBridgeVisitURLNewTab.postMessage("https://privacy.microsoft.com/en-us/privacystatement")})),e.appendChild(n)}}}]),t}();c()(h,"resources",{shoppingImage:'<svg xmlns="http://www.w3.org/2000/svg" width="356" height="119" viewBox="0 0 356 119" fill="none">\n        <g clip-path="url(#clip0)">\n        <path d="M339.9 117.6C339.9 117.6 344 91.8996 293.5 85.9996C223.9 77.8996 169.4 4.59957 134.5 0.399566C60.7999 -8.50043 131.5 114.5 64.4999 82.5996C9.99992 56.6996 5.79992 97.8996 30.0999 117.6H339.9Z" fill="#EBECEF"/>\n        <path d="M77.7997 77.2993C76.1997 81.6993 70.9997 76.2993 63.8997 73.6993C56.7997 71.0993 50.6997 72.4993 52.3997 67.9993C54.0997 63.4993 60.9997 61.9993 67.9997 64.5993C74.9997 67.1993 79.3997 72.8993 77.7997 77.2993Z" fill="#EBECEF"/>\n        <path d="M222.9 72.3998L193.8 76.8998C192.8 77.0998 191.9 76.3998 191.7 75.3998L190.2 65.8998C190 64.8998 190.7 63.9998 191.7 63.7998L220.8 59.2998C221.8 59.0998 222.7 59.7998 222.9 60.7998L224.4 70.2998C224.5 71.2998 223.8 72.1998 222.9 72.3998Z" fill="#0A3371"/>\n        <path d="M222.9 60.8999L190.2 65.9999V65.7999C190.1 64.8999 190.7 63.9999 191.6 63.8999L220.9 59.2999C221.8 59.1999 222.7 59.7999 222.8 60.6999L222.9 60.8999Z" fill="#025AC6"/>\n        <path d="M219.7 53.1003L191 57.6003C189.9 57.8003 189.2 58.8003 189.3 59.9003L189.7 62.2003C189.9 63.3003 190.9 64.0003 192 63.9003L220.7 59.4003C221.8 59.2003 222.5 58.2003 222.4 57.1003L222 54.8003C221.8 53.6003 220.8 52.9003 219.7 53.1003Z" fill="#0A3371"/>\n        <path d="M187.4 78.1L155 81.5L156.1 48.5L179.3 46L187.4 78.1Z" fill="#025AC6"/>\n        <path d="M185.9 71.9006L192.9 71.1006L194.5 77.3006L187.5 78.1006L185.9 71.9006Z" fill="#0A3371"/>\n        <path d="M194.5 77.3004L191.3 75.0004L190.3 74.3004L190.1 74.1004L189.9 74.4004L189.3 75.2004L187.4 78.0004L179.3 46.0004L183.2 48.3004L183.4 48.4004L183.6 48.5004L183.8 48.3004L184 48.1004L186.4 45.4004L194.5 77.3004Z" fill="white"/>\n        <path d="M191 77.2008L190.6 77.3008L189.9 74.4008L183.4 48.4008L183.6 48.5008L183.8 48.3008L190.3 74.4008L191 77.2008Z" fill="#0A3371"/>\n        <path d="M167.1 62.7006C165.4 62.9006 163.9 62.5006 162.6 61.4006C159.4 58.8006 158.8 53.4006 158.8 53.1006L159.6 53.0006C159.6 53.0006 160.2 58.4006 163.1 60.8006C164.2 61.7006 165.5 62.1006 167 61.9006C173.5 61.2006 175.9 51.3006 175.9 51.2006L176.7 51.1006V51.3006C176.6 51.8006 174.1 62.0006 167.1 62.7006Z" fill="white"/>\n        <path d="M223.5 29.9998L229.2 26.7998C229.8 26.4998 230.6 26.9998 230.4 27.6998L229.5 30.8998C229.4 31.2998 228.9 31.5998 228.5 31.3998L223.5 29.9998Z" fill="#0A3371"/>\n        <path d="M227 31.0002L223.8 25.3002C223.5 24.7002 222.5 24.8002 222.3 25.5002L221.4 28.7002C221.3 29.1002 221.5 29.6002 222 29.7002L227 31.0002Z" fill="#0A3371"/>\n        <path d="M222.9 64.4004L208.5 60.0004C208 59.8004 207.7 59.3004 207.8 58.8004L214.8 35.4004L231.1 40.3004L224.1 63.7004C224 64.2004 223.5 64.5004 222.9 64.4004Z" fill="#025AC6"/>\n        <path d="M235.1 33.4006L215.1 27.4006C214.6 27.2006 214 27.5006 213.9 28.1006L212.3 33.6006C212.1 34.1006 212.4 34.7006 213 34.8006L233 40.8006C233.5 41.0006 234.1 40.7006 234.2 40.1006L235.8 34.6006C235.9 34.1006 235.6 33.6006 235.1 33.4006Z" fill="#025AC6"/>\n        <path d="M218.9 63.2002L212.5 61.3002L222 29.6002L222.4 28.4002C222.4 28.4002 223.4 28.1002 225.6 28.7002C227.9 29.4002 228.8 30.3002 228.8 30.3002L228.4 31.5002L218.9 63.2002Z" fill="#0A3371"/>\n        <path opacity="0.5" d="M231.182 40.2723L214.898 35.3916L214.439 36.9242L230.723 41.8049L231.182 40.2723Z" fill="#0A3358"/>\n        <path d="M170.7 111.1C170.3 114.3 167.4 116.9 164.2 116.9C161 116.9 158.7 114.3 159.1 111.1C159.5 107.9 162.4 105.3 165.6 105.3C166.9 105.3 168.1 105.8 169 106.5C169.7 107.1 170.2 107.8 170.5 108.7C170.7 109.4 170.7 110.2 170.7 111.1Z" fill="#FE7002"/>\n        <path d="M167.6 114C169.4 112.3 169.7 109.5 168.2 107.9C166.7 106.3 163.9 106.3 162.1 108.1C160.3 109.8 160 112.6 161.5 114.2C163.1 115.8 165.8 115.7 167.6 114Z" fill="#F9F9F9"/>\n        <path d="M170.5 108.699L166 111.599L168 107.599L168.7 106.399H169C169.7 107.099 170.2 107.799 170.5 108.699Z" fill="#5B5B5B"/>\n        <path d="M211.7 111.1C211.3 114.3 208.4 116.9 205.2 116.9C202 116.9 199.7 114.3 200.1 111.1C200.5 107.9 203.4 105.3 206.6 105.3C207.9 105.3 209.1 105.8 210 106.5C210.7 107.1 211.2 107.8 211.5 108.7C211.7 109.4 211.8 110.2 211.7 111.1Z" fill="#FE7002"/>\n        <path d="M208.7 114C210.5 112.3 210.8 109.5 209.3 107.9C207.8 106.3 205 106.3 203.2 108.1C201.4 109.8 201.1 112.6 202.6 114.2C204.1 115.8 206.8 115.7 208.7 114Z" fill="#F9F9F9"/>\n        <path d="M211.5 108.699L207 111.599L209 107.599L209.7 106.399H210C210.7 107.099 211.2 107.799 211.5 108.699Z" fill="#5B5B5B"/>\n        <path d="M177.3 117.8C162 117.4 155 108 146.7 107.3C140.9 106.8 142.5 111.9 144 115C132.2 108.8 135.5 117.2 135.5 117.2L148.2 117.4L177.3 117.8Z" fill="#0A3371"/>\n        <path d="M208.8 117.2H193.1C193.1 117.2 193 110.8 200.4 114.4C203.1 115.7 206.2 117.1 208.8 117.2Z" fill="#0A3371"/>\n        <path d="M170.4 103.6H165.9L166.2 101.1H170.7L170.4 103.6Z" fill="#FE7002"/>\n        <path d="M165 112.2C164.2 112.2 163.6 111.5 163.7 110.7L164.5 104.5C164.6 104 165 103.6 165.5 103.6H170.6C171.3 103.6 171.6 104.3 171.2 104.9L166.6 111.4C166 111.9 165.5 112.2 165 112.2Z" fill="#FE7002"/>\n        <path d="M211.1 103.6H206.6L206.9 101.1H211.4L211.1 103.6Z" fill="#FE7002"/>\n        <path d="M205.7 112.2C204.9 112.2 204.3 111.5 204.4 110.7L205.2 104.5C205.3 104 205.7 103.6 206.2 103.6H211.3C212 103.6 212.3 104.3 211.9 104.9L207.3 111.4C206.8 111.9 206.2 112.2 205.7 112.2Z" fill="#FE7002"/>\n        <path d="M163.2 81.6996L158.4 36.8996L160.1 36.5996L164.9 81.2996L163.2 81.6996Z" fill="url(#paint0_linear)"/>\n        <path d="M177.1 79.8L173.2 37.8L174.9 37.5L178.8 79.4L177.1 79.8Z" fill="url(#paint1_linear)"/>\n        <path d="M191.2 78.3002L187.7 38.5002L189.4 38.2002L192.9 78.0002L191.2 78.3002Z" fill="url(#paint2_linear)"/>\n        <path d="M204.1 76.6996L203.3 39.3996L205 39.0996L205.8 76.3996L204.1 76.6996Z" fill="url(#paint3_linear)"/>\n        <path d="M217.6 74.7994H215.9L220.1 40.3994H221.8L217.6 74.7994Z" fill="url(#paint4_linear)"/>\n        <path d="M154 83.6994L151 35.3994L228 39.9994C229.2 40.0994 230.2 40.5994 230.8 41.4994C231.5 42.3994 231.7 43.5994 231.4 44.7994L225.2 70.4994C224.6 72.7994 222.6 74.6994 220.2 74.9994L154 83.6994ZM152.9 37.1994L155.7 81.6994L220.3 73.1994C221.9 72.9994 223.2 71.7994 223.6 70.1994L229.8 44.4994C230 43.7994 229.9 43.0994 229.5 42.4994C229.1 41.9994 228.5 41.5994 227.8 41.5994L152.9 37.1994Z" fill="url(#paint5_linear)"/>\n        <path d="M151.5 42.6998L150.7 30.3998L143.8 28.9998L144.3 27.2998L152.3 28.8998L153.2 42.2998L151.5 42.6998Z" fill="#FE7002"/>\n        <path d="M228.6 53.2H152.8L153 51.5H228.8L228.6 53.2Z" fill="url(#paint6_linear)"/>\n        <path d="M153.9 69.2002V67.5002L226 61.2002V62.9002L153.9 69.2002Z" fill="url(#paint7_linear)"/>\n        <path d="M220.6 106.299C220.5 106.299 220.3 106.299 220.2 106.199L218.9 105.499C214.6 103.199 209.5 101.999 204.4 101.999H162.6C157.9 101.999 154.4 98.5993 154.3 93.9993L154 82.8993C154 82.3993 154.4 81.9993 154.8 81.8993C155.3 81.7993 155.7 82.1993 155.7 82.5993L156 93.6993C156.1 97.4993 159 100.199 162.8 100.199H204.6C210 100.199 215.3 101.499 219.9 103.899L221.2 104.599C221.6 104.799 221.7 105.299 221.4 105.699C221.2 106.099 220.9 106.299 220.6 106.299Z" fill="url(#paint8_linear)"/>\n        <path d="M178.9 90.3H154.9C154.4 90.3 154.1 89.9 154.1 89.4C154.2 88.9 154.6 88.5 155.1 88.5H179.1C179.6 88.5 179.9 88.9 179.9 89.4C179.8 89.9 179.3 90.3 178.9 90.3Z" fill="url(#paint9_linear)"/>\n        <path d="M146.4 30.2998L140.9 29.3998C140.2 29.2998 139.7 28.5998 140 27.7998L140.2 27.1998C140.4 26.3998 141.2 25.8998 141.9 26.0998L147.4 26.9998C148.1 27.0998 148.6 27.7998 148.3 28.5998L148.1 29.1998C147.9 29.8998 147.1 30.3998 146.4 30.2998Z" fill="#FE7002"/>\n        <path d="M120.5 56.5994H63.4002C61.6002 56.5994 60.2002 55.1994 60.2002 53.3994V41.5994C60.2002 39.7994 61.6002 38.3994 63.4002 38.3994H120.6C122.4 38.3994 123.8 39.7994 123.8 41.5994V53.3994C123.7 55.1994 122.3 56.5994 120.5 56.5994Z" fill="white" stroke="#EBECEF"/>\n        <path d="M120.5 57.0994H83.8003V37.8994H120.5C122.5 37.8994 124.2 39.5994 124.2 41.5994V53.3994C124.2 55.4994 122.6 57.0994 120.5 57.0994Z" fill="#0656F8"/>\n        <path d="M85.3003 37.8994H83.8003V57.1994H85.3003V37.8994Z" fill="#0A3371"/>\n        <path d="M122.1 42.9996C119.8 42.9996 117.9 41.0996 117.9 38.7996C117.9 36.4996 119.8 34.5996 122.1 34.5996C124.4 34.5996 126.3 36.4996 126.3 38.7996C126.2 41.0996 124.4 42.9996 122.1 42.9996Z" fill="white"/>\n        <path d="M124.1 37.1992C123.6 36.6992 122.8 36.6992 122.3 37.1992L122.2 37.2992L122.1 37.1992C121.6 36.6992 120.8 36.6992 120.3 37.1992C119.8 37.6992 119.8 38.4992 120.3 38.9992L121.8 40.4992C121.9 40.5992 122.1 40.6992 122.2 40.6992C122.3 40.6992 122.5 40.5992 122.6 40.4992L124.1 38.9992C124.6 38.4992 124.6 37.6992 124.1 37.1992Z" fill="#FE7002"/>\n        <path d="M88.4004 50.8994H118.5" stroke="#EBECEF" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>\n        <path d="M88.4004 47.5H118.5" stroke="#EBECEF" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>\n        <path d="M88.4004 44.0996H103.4" stroke="#EBECEF" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>\n        <path d="M79.4003 44.5992L76.3003 46.8992V52.9992C76.3003 53.5992 75.8003 53.9992 75.3003 53.9992H68.2003C67.6003 53.9992 67.2003 53.4992 67.2003 52.9992V46.7992L64.0003 44.5992C63.7003 44.1992 63.8003 43.5992 64.3003 43.2992L67.6003 40.7992C68.0003 40.4992 68.5003 40.4992 68.8003 40.7992L70.6003 42.0992C71.3003 42.5992 72.3003 42.5992 73.0003 42.0992L74.4003 41.0992L74.8003 40.7992C75.2003 40.4992 75.7003 40.4992 76.0003 40.7992L77.5003 41.9992L79.3003 43.3992C79.6003 43.5992 79.7003 44.0992 79.4003 44.5992Z" fill="#0656F8"/>\n        <path d="M77.4001 41.8996L68.8001 53.9996H68.1001C67.5001 53.9996 67.1001 53.4996 67.1001 52.9996V51.0996L74.3001 40.9996L74.7001 40.6996C75.1001 40.3996 75.6001 40.3996 75.9001 40.6996L77.4001 41.8996Z" fill="#025AC6"/>\n        <path d="M140.4 93.0002H83.2C81.4 93.0002 80 91.6002 80 89.8002V77.9002C80 76.1002 81.4 74.7002 83.2 74.7002H140.4C142.2 74.7002 143.6 76.1002 143.6 77.9002V89.7002C143.6 91.5002 142.2 93.0002 140.4 93.0002Z" fill="white" stroke="#EBECEF"/>\n        <path d="M140.4 93.5002H103.7V74.2002H140.4C142.4 74.2002 144.1 75.9002 144.1 77.9002V89.7002C144.1 91.8002 142.4 93.5002 140.4 93.5002Z" fill="#0656F8"/>\n        <path d="M105.2 74.2002H103.7V93.5002H105.2V74.2002Z" fill="#0A3371"/>\n        <path d="M96.8 89.701L96.3 78.501H86.5L86 89.701C86 90.201 86.4 90.701 86.9 90.701H89.2C89.7 90.701 90.1 90.301 90.1 89.801L91.1 82.601C91.1 82.401 91.5 82.401 91.5 82.601L92.5 89.801C92.5 90.301 92.9 90.701 93.4 90.701H95.7C96.4 90.701 96.9 90.201 96.8 89.701Z" fill="#0656F8"/>\n        <path d="M96.4001 77.1006H86.6001V78.5006H96.4001V77.1006Z" fill="#025AC6"/>\n        <path d="M141.9 79.3004C139.6 79.3004 137.7 77.4004 137.7 75.1004C137.7 72.8004 139.6 70.9004 141.9 70.9004C144.2 70.9004 146.1 72.8004 146.1 75.1004C146.1 77.5004 144.2 79.3004 141.9 79.3004Z" fill="white"/>\n        <path d="M143.8 73C143.3 73 142.9 73.3 142.8 73.8L142.1 73.6C141.9 73.5 141.7 73.6 141.6 73.7L139.8 75.1C139.5 75.3 139.5 75.7 139.7 75.9L140.7 77.3C140.9 77.6 141.3 77.6 141.5 77.4L143.3 76C143.4 75.9 143.5 75.7 143.5 75.5V75.1C143.6 75.1 143.6 75.1 143.7 75.1C144.3 75.1 144.8 74.6 144.8 74C144.8 73.4 144.4 73 143.8 73ZM143.8 75C143.7 75 143.7 75 143.6 75V74.6C143.6 74.3 143.4 74.1 143.2 74H143C143.1 73.7 143.4 73.4 143.8 73.4C144.3 73.4 144.6 73.8 144.6 74.2C144.6 74.6 144.2 75 143.8 75Z" fill="#FE7002"/>\n        <path d="M109.5 87.7002H139.6" stroke="#EBECEF" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>\n        <path d="M109.5 84.3008H139.6" stroke="#EBECEF" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>\n        <path d="M109.5 80.9004H124.5" stroke="#EBECEF" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>\n        <path d="M291.8 82.6004H234.6C232.8 82.6004 231.4 81.2004 231.4 79.4004V67.6004C231.4 65.8004 232.8 64.4004 234.6 64.4004H291.8C293.6 64.4004 295 65.8004 295 67.6004V79.4004C295 81.2004 293.5 82.6004 291.8 82.6004Z" fill="white" stroke="#EBECEF"/>\n        <path d="M291.8 83.1004H255.1V63.9004H291.8C293.8 63.9004 295.5 65.6004 295.5 67.6004V79.4004C295.5 81.5004 293.8 83.1004 291.8 83.1004Z" fill="#0656F8"/>\n        <path d="M256.6 63.9004H255.1V83.2004H256.6V63.9004Z" fill="#0A3371"/>\n        <path d="M293.3 68.9C291 68.9 289.1 67 289.1 64.7C289.1 62.4 291 60.5 293.3 60.5C295.6 60.5 297.5 62.4 297.5 64.7C297.5 67.1 295.6 68.9 293.3 68.9Z" fill="white"/>\n        <path d="M295.3 63.2002C294.8 62.7002 294 62.7002 293.5 63.2002L293.4 63.3002L293.3 63.2002C292.8 62.7002 292 62.7002 291.5 63.2002C291 63.7002 291 64.5002 291.5 65.0002L293 66.5002C293.1 66.6002 293.3 66.7002 293.4 66.7002C293.6 66.7002 293.7 66.6002 293.8 66.5002L295.3 65.0002C295.8 64.5002 295.8 63.7002 295.3 63.2002Z" fill="#FE7002"/>\n        <path d="M260.3 76.8994H290.4" stroke="#EBECEF" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>\n        <path d="M260.3 73.5H290.4" stroke="#EBECEF" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>\n        <path d="M260.3 70.0996H275.3" stroke="#EBECEF" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>\n        <path d="M243 81.1004H241.6C240.6 81.1004 239.7 80.3004 239.7 79.2004V67.8004C239.7 66.8004 240.5 65.9004 241.6 65.9004H243C244 65.9004 244.9 66.7004 244.9 67.8004V79.2004C244.9 80.2004 244 81.1004 243 81.1004Z" fill="#0656F8"/>\n        <path d="M242.3 78.4006C245 78.4006 247.2 76.2006 247.2 73.5006C247.2 70.8006 245 68.6006 242.3 68.6006C239.6 68.6006 237.4 70.8006 237.4 73.5006C237.4 76.2006 239.6 78.4006 242.3 78.4006Z" fill="#025AC6"/>\n        <path d="M242.3 77.3002C244.4 77.3002 246.1 75.6002 246.1 73.5002C246.1 71.4002 244.4 69.7002 242.3 69.7002C240.2 69.7002 238.5 71.4002 238.5 73.5002C238.5 75.6002 240.2 77.3002 242.3 77.3002Z" fill="white"/>\n        <path d="M242.3 71.5V73.5H244.3" stroke="#025AC6" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>\n        <path d="M160.2 16.0002C159.8 14.1002 158.3 12.5002 156.4 12.2002C156.2 12.2002 156.2 11.9002 156.4 11.9002C158.3 11.5002 159.9 10.0002 160.2 8.1002C160.2 7.9002 160.5 7.9002 160.5 8.1002C160.9 10.0002 162.4 11.6002 164.3 11.9002C164.5 11.9002 164.5 12.2002 164.3 12.2002C162.4 12.6002 160.8 14.1002 160.5 16.0002C160.5 16.2002 160.2 16.2002 160.2 16.0002Z" fill="url(#paint10_linear)"/>\n        <path d="M169.3 20.7C169.1 19.7 168.3 19 167.4 18.8C167.3 18.8 167.3 18.7 167.4 18.6C168.4 18.4 169.1 17.6 169.3 16.7C169.3 16.6 169.4 16.6 169.5 16.7C169.7 17.7 170.5 18.4 171.4 18.6C171.5 18.6 171.5 18.7 171.4 18.8C170.4 19 169.7 19.8 169.5 20.7C169.4 20.8 169.3 20.8 169.3 20.7Z" fill="url(#paint11_linear)"/>\n        <path d="M237.4 55.0002C237.3 54.5002 236.9 54.1002 236.4 54.0002C236.3 54.0002 236.3 53.9002 236.4 53.9002C236.9 53.8002 237.3 53.4002 237.4 52.9002C237.4 52.8002 237.5 52.8002 237.5 52.9002C237.6 53.4002 238 53.8002 238.5 53.9002C238.6 53.9002 238.6 54.0002 238.5 54.0002C237.9 54.2002 237.6 54.5002 237.4 55.0002C237.4 55.1002 237.4 55.1002 237.4 55.0002Z" fill="url(#paint12_linear)"/>\n        <path d="M145.7 52.2996C145.5 51.2996 144.7 50.5996 143.8 50.3996C143.7 50.3996 143.7 50.2996 143.8 50.1996C144.8 49.9996 145.5 49.1996 145.7 48.2996C145.7 48.1996 145.8 48.1996 145.9 48.2996C146.1 49.2996 146.9 49.9996 147.8 50.1996C147.9 50.1996 147.9 50.2996 147.8 50.3996C146.8 50.5996 146.1 51.3996 145.9 52.2996C145.9 52.3996 145.7 52.3996 145.7 52.2996Z" fill="url(#paint13_linear)"/>\n        <path d="M189.8 91.4998C189.6 90.2998 188.6 89.2998 187.4 89.0998C187.3 89.0998 187.3 88.8998 187.4 88.8998C188.6 88.6998 189.6 87.6998 189.8 86.4998C189.8 86.3998 190 86.3998 190 86.4998C190.2 87.6998 191.2 88.6998 192.4 88.8998C192.5 88.8998 192.5 89.0998 192.4 89.0998C191.2 89.2998 190.2 90.2998 190 91.4998C189.9 91.6998 189.8 91.6998 189.8 91.4998Z" fill="url(#paint14_linear)"/>\n        <path d="M1 117.6H354.9" stroke="#EBECEF" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round"/>\n        </g>\n        <defs>\n        <linearGradient id="paint0_linear" x1="158.425" y1="59.1173" x2="164.918" y2="59.1173" gradientUnits="userSpaceOnUse">\n        <stop stop-color="#FE7002"/>\n        <stop offset="0.9935" stop-color="#F8DA00"/>\n        </linearGradient>\n        <linearGradient id="paint1_linear" x1="173.172" y1="58.6315" x2="178.786" y2="58.6315" gradientUnits="userSpaceOnUse">\n        <stop stop-color="#FE7002"/>\n        <stop offset="0.9935" stop-color="#F8DA00"/>\n        </linearGradient>\n        <linearGradient id="paint2_linear" x1="187.657" y1="58.2488" x2="192.892" y2="58.2488" gradientUnits="userSpaceOnUse">\n        <stop stop-color="#FE7002"/>\n        <stop offset="0.9935" stop-color="#F8DA00"/>\n        </linearGradient>\n        <linearGradient id="paint3_linear" x1="203.322" y1="57.8938" x2="205.813" y2="57.8938" gradientUnits="userSpaceOnUse">\n        <stop stop-color="#FE7002"/>\n        <stop offset="0.9935" stop-color="#F8DA00"/>\n        </linearGradient>\n        <linearGradient id="paint4_linear" x1="216.532" y1="57.3404" x2="220.45" y2="57.7757" gradientUnits="userSpaceOnUse">\n        <stop stop-color="#FE7002"/>\n        <stop offset="0.9935" stop-color="#F8DA00"/>\n        </linearGradient>\n        <linearGradient id="paint5_linear" x1="151.001" y1="59.5238" x2="231.533" y2="59.5238" gradientUnits="userSpaceOnUse">\n        <stop stop-color="#FE7002"/>\n        <stop offset="0.9935" stop-color="#F8DA00"/>\n        </linearGradient>\n        <linearGradient id="paint6_linear" x1="152.818" y1="52.3757" x2="228.802" y2="52.3757" gradientUnits="userSpaceOnUse">\n        <stop stop-color="#FE7002"/>\n        <stop offset="0.9935" stop-color="#F8DA00"/>\n        </linearGradient>\n        <linearGradient id="paint7_linear" x1="153.885" y1="65.2155" x2="226.017" y2="65.2155" gradientUnits="userSpaceOnUse">\n        <stop stop-color="#FE7002"/>\n        <stop offset="0.9935" stop-color="#F8DA00"/>\n        </linearGradient>\n        <linearGradient id="paint8_linear" x1="153.939" y1="94.0605" x2="221.527" y2="94.0605" gradientUnits="userSpaceOnUse">\n        <stop stop-color="#FE7002"/>\n        <stop offset="0.9935" stop-color="#F8DA00"/>\n        </linearGradient>\n        <linearGradient id="paint9_linear" x1="154.138" y1="89.4123" x2="179.83" y2="89.4123" gradientUnits="userSpaceOnUse">\n        <stop stop-color="#FE7002"/>\n        <stop offset="0.9935" stop-color="#F8DA00"/>\n        </linearGradient>\n        <linearGradient id="paint10_linear" x1="156.032" y1="16.3948" x2="161.521" y2="10.8242" gradientUnits="userSpaceOnUse">\n        <stop stop-color="#FE7002"/>\n        <stop offset="0.9935" stop-color="#F8DA00"/>\n        </linearGradient>\n        <linearGradient id="paint11_linear" x1="166.786" y1="21.6554" x2="170.39" y2="17.4774" gradientUnits="userSpaceOnUse">\n        <stop stop-color="#FE7002"/>\n        <stop offset="0.9935" stop-color="#F8DA00"/>\n        </linearGradient>\n        <linearGradient id="paint12_linear" x1="236.129" y1="55.5137" x2="237.931" y2="53.4247" gradientUnits="userSpaceOnUse">\n        <stop stop-color="#FE7002"/>\n        <stop offset="0.9935" stop-color="#F8DA00"/>\n        </linearGradient>\n        <linearGradient id="paint13_linear" x1="143.248" y1="53.2382" x2="146.852" y2="49.0602" gradientUnits="userSpaceOnUse">\n        <stop stop-color="#FE7002"/>\n        <stop offset="0.9935" stop-color="#F8DA00"/>\n        </linearGradient>\n        <linearGradient id="paint14_linear" x1="186.629" y1="92.7505" x2="191.153" y2="87.5071" gradientUnits="userSpaceOnUse">\n        <stop stop-color="#FE7002"/>\n        <stop offset="0.9935" stop-color="#F8DA00"/>\n        </linearGradient>\n        <clipPath id="clip0">\n        <rect width="355.9" height="118.6" fill="white"/>\n        </clipPath>\n        </defs>\n        </svg>'}),e.a=h},function(t,e,i){"use strict";i.d(e,"a",(function(){return r}));var n=i(39);class o{constructor(t,e){this.target=t,this.propertyName=e}bind(t){t[this.propertyName]=this.target}unbind(){}}function r(t){return new n.a("fast-ref",o,t)}},function(t,e,i){"use strict";i.d(e,"a",(function(){return n}));const n="(var(--base-height-multiplier) + var(--density)) * var(--design-unit)"},,function(t,e,i){"use strict";var n=i(36),o=i(32);e.a=function(t){return"symbol"==typeof t||Object(o.a)(t)&&"[object Symbol]"==Object(n.a)(t)}},function(t,e){t.exports=function(t){if(!t.webpackPolyfill){var e=Object.create(t);e.children||(e.children=[]),Object.defineProperty(e,"loaded",{enumerable:!0,get:function(){return e.l}}),Object.defineProperty(e,"id",{enumerable:!0,get:function(){return e.i}}),Object.defineProperty(e,"exports",{enumerable:!0}),e.webpackPolyfill=1}return e}},function(t,e,i){"use strict";(function(t){var n=i(64),o="object"==typeof exports&&exports&&!exports.nodeType&&exports,r=o&&"object"==typeof t&&t&&!t.nodeType&&t,a=r&&r.exports===o&&n.a.process,s=function(){try{var t=r&&r.require&&r.require("util").types;return t||a&&a.binding&&a.binding("util")}catch(t){}}();e.a=s}).call(this,i(79)(t))},,function(t,e){var i;i=function(){return this}();try{i=i||new Function("return this")()}catch(t){"object"==typeof window&&(i=window)}t.exports=i},function(t,e,i){"use strict";var n=i(7),o=i.n(n),r=i(0),a=i.n(r);e.a=function t(e,i){o()(this,t),a()(this,"AppInfoClientName",void 0),a()(this,"JSVersion",void 0),this.AppInfoClientName=e,this.JSVersion=i}},function(t,e,i){"use strict";var n=i(7),o=i.n(n),r=i(14),a=i.n(r),s=i(0),c=i.n(s),l=function(){function t(e,i,n,r,a,s){o()(this,t),c()(this,"EventType",void 0),c()(this,"JsonData",void 0),c()(this,"LogLevel",void 0),c()(this,"Message",void 0),c()(this,"ClientContext",void 0),c()(this,"ImpressionId",void 0),c()(this,"EventTime",void 0),this.LogLevel=e,this.EventType=i,this.JsonData=n,this.Message=r,this.ClientContext=s,this.ImpressionId=a,this.EventTime=(new Date).getTime()}return a()(t,[{key:"ToString",value:function(){return JSON.stringify(this)}}]),t}();e.a=l},,function(t,e,i){"use strict";(function(t){i.d(e,"a",(function(){return n}));const n=function(){if("undefined"!=typeof globalThis)return globalThis;if(void 0!==t)return t;if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;try{return new Function("return this")()}catch(t){return{}}}();void 0===n.trustedTypes&&(n.trustedTypes={createPolicy:(t,e)=>e})}).call(this,i(82))},function(t,e,i){"use strict";i.d(e,"a",(function(){return n}));const n=Object.freeze([])},function(t,e,i){"use strict";e.a=function(){return!1}},function(t,e,i){"use strict";(function(t){var n=i(35),o="object"==typeof exports&&exports&&!exports.nodeType&&exports,r=o&&"object"==typeof t&&t&&!t.nodeType&&t,a=r&&r.exports===o?n.a.Buffer:void 0,s=a?a.allocUnsafe:void 0;e.a=function(t,e){if(e)return t.slice();var i=t.length,n=s?s(i):new t.constructor(i);return t.copy(n),n}}).call(this,i(79)(t))},function(t,e,i){"use strict";var n=i(7),o=i.n(n),r=i(14),a=i.n(r),s=i(0),c=i.n(s),l=i(3),u=function(){function t(){o()(this,t),c()(this,"couponEmptyCouponSubTitle",void 0),c()(this,"couponEmptyCouponTitle",void 0),c()(this,"couponAutoApplyButton",void 0),c()(this,"couponAvailableCoupons",void 0),c()(this,"pcBestPriceHeader",void 0),c()(this,"pcSimilarProductsButton",void 0),c()(this,"couponCannotApplyCoupons",void 0),c()(this,"commonContextMenuAutoShowDisable",void 0),c()(this,"commonContextMenuAutoShowEnable",void 0),c()(this,"commonContextMenuManage",void 0),c()(this,"commonMoreOptionsLabel",void 0),c()(this,"couponCopied",void 0),c()(this,"couponCopyLabel",void 0),c()(this,"couponSectionSingularTitle",void 0),c()(this,"couponTitle",void 0),c()(this,"couponSubtitle",void 0),c()(this,"commonFixedTitle",void 0),c()(this,"commonFrom",void 0),c()(this,"commonMicrosoftPolicy",void 0),c()(this,"commonClose",void 0),c()(this,"pcPriceSectionTitle",void 0),c()(this,"freFooter",void 0),c()(this,"freTitle",void 0),c()(this,"freSubTitle",void 0),c()(this,"freButton",void 0),c()(this,"freDismiss",void 0),c()(this,"freSettings",void 0)}return a()(t,null,[{key:"Create",value:function(){var e=new t;return l.k.currentDevice!==l.b.Desktop||l.k.testLocally?(e.couponEmptyCouponSubTitle="We couldn't find any coupons at this time.",e.couponEmptyCouponTitle="Check back for coupons!",e.couponAutoApplyButton="Try all Coupons",e.couponAvailableCoupons="Coupons available",e.pcBestPriceHeader="You have the best price!",e.pcSimilarProductsButton="Show similar products",e.couponCannotApplyCoupons="We can’t automatically apply coupons right now. Click the promotional codes box on the checkout page and try to apply available coupons.",e.commonContextMenuAutoShowDisable="Don't show this automatically",e.commonContextMenuAutoShowEnable="Show coupons on checkout",e.commonContextMenuManage="Manage",e.couponCopied="Copied",e.couponCopyLabel="Copy code",e.commonMoreOptionsLabel="More options",e.couponSectionSingularTitle="1 coupon",e.couponTitle="Coupons found!",e.couponSubtitle="Let's see if we can find you a coupon",e.commonFixedTitle="Shopping in Microsoft Edge",e.commonFrom="from",e.commonMicrosoftPolicy="Microsoft Privacy Statement",e.pcPriceSectionTitle="Compare with other retailers",e.freFooter="You can turn this off anytime in <a id=anchorSettings class=colorLink txtSmall>Settings</a>",e.freTitle="New in this update",e.freSubTitle="Sit back and shop with confidence! We'll search our shopping service and find you the best coupons across online retailers.",e.freButton="Got it, thanks!",e.freDismiss="Not now",e.freSettings="Settings",e.commonClose="Close"):(e.couponEmptyCouponSubTitle=loadTimeData.valueExists("EmptyCouponSubTitle")?loadTimeData.getValue("EmptyCouponSubTitle"):"",e.couponEmptyCouponTitle=loadTimeData.valueExists("EmptyCouponTitle")?loadTimeData.getValue("EmptyCouponTitle"):"",e.couponAutoApplyButton=loadTimeData.valueExists("autoApplyButton")?loadTimeData.getValue("autoApplyButton"):"",e.couponAvailableCoupons=loadTimeData.valueExists("availableCoupons")?loadTimeData.getValue("availableCoupons"):"",e.pcBestPriceHeader=loadTimeData.valueExists("bestPriceHeader")?loadTimeData.getValue("bestPriceHeader"):"",e.pcSimilarProductsButton=loadTimeData.valueExists("showMoreSellersLabel")?loadTimeData.getValue("showMoreSellersLabel"):"",e.couponCannotApplyCoupons=loadTimeData.valueExists("cannotApplyCoupons")?loadTimeData.getValue("cannotApplyCoupons"):"",e.commonContextMenuAutoShowDisable=loadTimeData.valueExists("contextMenuAutoShowDisable")?loadTimeData.getValue("contextMenuAutoShowDisable"):"",e.commonContextMenuAutoShowEnable=loadTimeData.valueExists("contextMenuAutoShowEnable")?loadTimeData.getValue("contextMenuAutoShowEnable"):"",e.commonContextMenuManage=loadTimeData.valueExists("contextMenuManage")?loadTimeData.getValue("contextMenuManage"):"",e.commonMoreOptionsLabel=loadTimeData.valueExists("moreOptionsLabel")?loadTimeData.getValue("moreOptionsLabel"):"",e.couponCopied=loadTimeData.valueExists("copyCoupon")?loadTimeData.getValue("copyCoupon"):"",e.couponCopyLabel="Copy code",e.couponSectionSingularTitle=loadTimeData.valueExists("couponSectionSingularTitle")?loadTimeData.getValue("couponSectionSingularTitle"):"",e.couponTitle=loadTimeData.valueExists("couponTitle")?loadTimeData.getValue("couponTitle"):"",e.couponSubtitle=loadTimeData.valueExists("couponSubtitle")?loadTimeData.getValue("couponSubtitle"):"",e.commonFixedTitle=loadTimeData.valueExists("fixedTitle")?loadTimeData.getValue("fixedTitle"):"",e.commonFrom=loadTimeData.valueExists("from")?loadTimeData.getValue("from"):"",e.commonMicrosoftPolicy=loadTimeData.valueExists("microsoftPolicy")?loadTimeData.getValue("microsoftPolicy"):"",e.pcPriceSectionTitle=loadTimeData.valueExists("priceSectionTitle")?loadTimeData.getValue("priceSectionTitle"):"",loadTimeData.getValue("isFRE")&&(e.freFooter=loadTimeData.valueExists("FREFooter")?loadTimeData.getValue("FREFooter"):"",e.freTitle=loadTimeData.valueExists("FRETitle")?loadTimeData.getValue("FRETitle"):"",e.freSubTitle=loadTimeData.valueExists("FRESubTitle")?loadTimeData.getValue("FRESubTitle"):"",e.freButton=loadTimeData.valueExists("FREButton")?loadTimeData.getValue("FREButton"):"",e.freDismiss=loadTimeData.valueExists("FREDismiss")?loadTimeData.getValue("FREDismiss"):"",e.freSettings=loadTimeData.valueExists("settings")?loadTimeData.getValue("settings"):""),e.commonClose=loadTimeData.valueExists("closeLabel")?loadTimeData.getValue("closeLabel"):""),e}}]),t}();e.a=u},function(t,e,i){"use strict";i.d(e,"a",(function(){return n}));const n="not-allowed"},,function(t,e,i){"use strict";var n=i(18),o=i(24),r=i(39);function a(t,e){this.source=t,this.context=e,null===this.bindingObserver&&(this.bindingObserver=o.a.binding(this.binding,this,this.isBindingVolatile)),this.updateTarget(this.bindingObserver.observe(t,e))}function s(t,e){this.source=t,this.context=e,this.target.addEventListener(this.targetName,this)}function c(){this.bindingObserver.disconnect(),this.source=null,this.context=null}function l(){this.bindingObserver.disconnect(),this.source=null,this.context=null;const t=this.target.$fastView;void 0!==t&&t.isComposed&&(t.unbind(),t.needsBindOnly=!0)}function u(){this.target.removeEventListener(this.targetName,this),this.source=null,this.context=null}function A(t){n.a.setAttribute(this.target,this.targetName,t)}function g(t){n.a.setBooleanAttribute(this.target,this.targetName,t)}function h(t){if(null==t&&(t=""),t.create){this.target.textContent="";let e=this.target.$fastView;void 0===e?e=t.create():this.target.$fastTemplate!==t&&(e.isComposed&&(e.remove(),e.unbind()),e=t.create()),e.isComposed?e.needsBindOnly&&(e.needsBindOnly=!1,e.bind(this.source,this.context)):(e.isComposed=!0,e.bind(this.source,this.context),e.insertBefore(this.target),this.target.$fastView=e,this.target.$fastTemplate=t)}else{const e=this.target.$fastView;void 0!==e&&e.isComposed&&(e.isComposed=!1,e.remove(),e.needsBindOnly?e.needsBindOnly=!1:e.unbind()),this.target.textContent=t}}function d(t){this.target[this.targetName]=t}function I(t){const e=this.classVersions||Object.create(null),i=this.target;let n=this.version||0;if(null!=t&&t.length){const o=t.split(/\s+/);for(let t=0,r=o.length;t<r;++t){const r=o[t];""!==r&&(e[r]=n,i.classList.add(r))}}if(this.classVersions=e,this.version=n+1,0!==n){n-=1;for(const t in e)e[t]===n&&i.classList.remove(t)}}class M extends r.c{constructor(t){super(),this.binding=t,this.bind=a,this.unbind=c,this.updateTarget=A,this.isBindingVolatile=o.a.isVolatileBinding(this.binding)}get targetName(){return this.originalTargetName}set targetName(t){if(this.originalTargetName=t,void 0!==t)switch(t[0]){case":":if(this.cleanedTargetName=t.substr(1),this.updateTarget=d,"innerHTML"===this.cleanedTargetName){const t=this.binding;this.binding=(e,i)=>n.a.createHTML(t(e,i))}break;case"?":this.cleanedTargetName=t.substr(1),this.updateTarget=g;break;case"@":this.cleanedTargetName=t.substr(1),this.bind=s,this.unbind=u;break;default:this.cleanedTargetName=t,"class"===t&&(this.updateTarget=I)}}targetAtContent(){this.updateTarget=h,this.unbind=l}createBehavior(t){return new C(t,this.binding,this.isBindingVolatile,this.bind,this.unbind,this.updateTarget,this.cleanedTargetName)}}class C{constructor(t,e,i,n,o,r,a){this.source=null,this.context=null,this.bindingObserver=null,this.target=t,this.binding=e,this.isBindingVolatile=i,this.bind=n,this.unbind=o,this.updateTarget=r,this.targetName=a}handleChange(){this.updateTarget(this.bindingObserver.observe(this.source,this.context))}handleEvent(t){Object(o.d)(t);const e=this.binding(this.source,this.context);Object(o.d)(null),!0!==e&&t.preventDefault()}}class p{addFactory(t){t.targetIndex=this.targetIndex,this.behaviorFactories.push(t)}captureContentBinding(t){t.targetAtContent(),this.addFactory(t)}reset(){this.behaviorFactories=[],this.targetIndex=-1}release(){f=this}static borrow(t){const e=f||new p;return e.directives=t,e.reset(),f=null,e}}let f=null;function D(t){if(1===t.length)return t[0];let e;const i=t.length,n=t.map(t=>"string"==typeof t?()=>t:(e=t.targetName||e,t.binding)),o=new M((t,e)=>{let o="";for(let r=0;r<i;++r)o+=n[r](t,e);return o});return o.targetName=e,o}const y=n.b.length;function b(t,e){const i=e.split(n.c);if(1===i.length)return null;const o=[];for(let e=0,r=i.length;e<r;++e){const r=i[e],a=r.indexOf(n.b);let s;if(-1===a)s=r;else{const e=parseInt(r.substring(0,a));o.push(t.directives[e]),s=r.substring(a+y)}""!==s&&o.push(s)}return o}function j(t,e,i=!1){const n=e.attributes;for(let o=0,r=n.length;o<r;++o){const a=n[o],s=a.value,c=b(t,s);let l=null;null===c?i&&(l=new M(()=>s),l.targetName=a.name):l=D(c),null!==l&&(e.removeAttributeNode(a),o--,r--,t.addFactory(l))}}function E(t,e,i){const n=b(t,e.textContent);if(null!==n){let o=e;for(let r=0,a=n.length;r<a;++r){const a=n[r],s=0===r?e:o.parentNode.insertBefore(document.createTextNode(""),o.nextSibling);"string"==typeof a?s.textContent=a:(s.textContent=" ",t.captureContentBinding(a)),o=s,t.targetIndex++,s!==e&&i.nextNode()}t.targetIndex--}}const m=document.createRange();class N{constructor(t,e){this.fragment=t,this.behaviors=e,this.source=null,this.context=null,this.firstChild=t.firstChild,this.lastChild=t.lastChild}appendTo(t){t.appendChild(this.fragment)}insertBefore(t){if(this.fragment.hasChildNodes())t.parentNode.insertBefore(this.fragment,t);else{const e=t.parentNode,i=this.lastChild;let n,o=this.firstChild;for(;o!==i;)n=o.nextSibling,e.insertBefore(o,t),o=n;e.insertBefore(i,t)}}remove(){const t=this.fragment,e=this.lastChild;let i,n=this.firstChild;for(;n!==e;)i=n.nextSibling,t.appendChild(n),n=i;t.appendChild(e)}dispose(){const t=this.firstChild.parentNode,e=this.lastChild;let i,n=this.firstChild;for(;n!==e;)i=n.nextSibling,t.removeChild(n),n=i;t.removeChild(e);const o=this.behaviors,r=this.source;for(let t=0,e=o.length;t<e;++t)o[t].unbind(r)}bind(t,e){const i=this.behaviors;if(this.source!==t)if(null!==this.source){const n=this.source;this.source=t,this.context=e;for(let o=0,r=i.length;o<r;++o){const r=i[o];r.unbind(n),r.bind(t,e)}}else{this.source=t,this.context=e;for(let n=0,o=i.length;n<o;++n)i[n].bind(t,e)}}unbind(){if(null===this.source)return;const t=this.behaviors,e=this.source;for(let i=0,n=t.length;i<n;++i)t[i].unbind(e);this.source=null}static disposeContiguousBatch(t){if(0!==t.length){m.setStartBefore(t[0].firstChild),m.setEndAfter(t[t.length-1].lastChild),m.deleteContents();for(let e=0,i=t.length;e<i;++e){const i=t[e],n=i.behaviors,o=i.source;for(let t=0,e=n.length;t<e;++t)n[t].unbind(o)}}}}i.d(e,"a",(function(){return v}));class O{constructor(t,e){this.behaviorCount=0,this.hasHostBehaviors=!1,this.fragment=null,this.targetOffset=0,this.viewBehaviorFactories=null,this.hostBehaviorFactories=null,this.html=t,this.directives=e}create(t){if(null===this.fragment){let t;const e=this.html;if("string"==typeof e){t=document.createElement("template"),t.innerHTML=n.a.createHTML(e);const i=t.content.firstElementChild;null!==i&&"TEMPLATE"===i.tagName&&(t=i)}else t=e;const i=function(t,e){const i=t.content;document.adoptNode(i);const o=p.borrow(e);j(o,t,!0);const r=o.behaviorFactories;o.reset();const a=n.a.createTemplateWalker(i);let s;for(;s=a.nextNode();)switch(o.targetIndex++,s.nodeType){case 1:j(o,s);break;case 3:E(o,s,a);break;case 8:n.a.isMarker(s)&&o.addFactory(e[n.a.extractDirectiveIndexFromMarker(s)])}let c=0;n.a.isMarker(i.firstChild)&&(i.insertBefore(document.createComment(""),i.firstChild),c=-1);const l=o.behaviorFactories;return o.release(),{fragment:i,viewBehaviorFactories:l,hostBehaviorFactories:r,targetOffset:c}}(t,this.directives);this.fragment=i.fragment,this.viewBehaviorFactories=i.viewBehaviorFactories,this.hostBehaviorFactories=i.hostBehaviorFactories,this.targetOffset=i.targetOffset,this.behaviorCount=this.viewBehaviorFactories.length+this.hostBehaviorFactories.length,this.hasHostBehaviors=this.hostBehaviorFactories.length>0}const e=this.fragment.cloneNode(!0),i=this.viewBehaviorFactories,o=new Array(this.behaviorCount),r=n.a.createTemplateWalker(e);let a=0,s=this.targetOffset,c=r.nextNode();for(let t=i.length;a<t;++a){const t=i[a],e=t.targetIndex;for(;null!==c;){if(s===e){o[a]=t.createBehavior(c);break}c=r.nextNode(),s++}}if(this.hasHostBehaviors){const e=this.hostBehaviorFactories;for(let i=0,n=e.length;i<n;++i,++a)o[a]=e[i].createBehavior(t)}return new N(e,o)}render(t,e,i){"string"==typeof e&&(e=document.getElementById(e)),void 0===i&&(i=e);const n=this.create(i);return n.bind(t,o.b),n.appendTo(e),n}}const w=/([ \x09\x0a\x0c\x0d])([^\0-\x1F\x7F-\x9F "'>=/]+)([ \x09\x0a\x0c\x0d]*=[ \x09\x0a\x0c\x0d]*(?:[^ \x09\x0a\x0c\x0d"'`<>=]*|"[^"]*|'[^']*))$/;function v(t,...e){const i=[];let n="";for(let o=0,a=t.length-1;o<a;++o){const a=t[o];let s=e[o];if(n+=a,s instanceof O){const t=s;s=()=>t}if("function"==typeof s&&(s=new M(s)),s instanceof r.c){const t=w.exec(a);null!==t&&(s.targetName=t[2])}s instanceof r.b?(n+=s.createPlaceholder(i.length),i.push(s)):n+=s}return n+=t[t.length-1],new O(n,i)}},,,function(t,e){function i(e,n){return t.exports=i=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t},i(e,n)}t.exports=i},function(t,e){function i(e){return"function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?t.exports=i=function(t){return typeof t}:t.exports=i=function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i(e)}t.exports=i},,,,,,function(t,e,i){"use strict";i.d(e,"a",(function(){return n}));function n(t){return`:host([hidden]){display:none}:host{display:${t}}`}},function(t,e,i){"use strict";var n=i(19),o=i(110),r=i(21),a=i(24);class s extends o.a{handleFooterContentChange(){this.footerContainer.classList.toggle("footer",this.footer.assignedNodes().length>0)}handleHeaderIconContentChange(){this.headerIconContainer.classList.toggle("header-icon",this.headerIcon.assignedNodes().length>0)}handleStartContentChange(){this.startContentContainer.classList.toggle("start-content",this.startContent.assignedNodes().length>0)}}Object(n.a)([Object(r.b)({attribute:"fixed-title",mode:"fromView"})],s.prototype,"fixedTitle",void 0),Object(n.a)([Object(r.b)({mode:"fromView"})],s.prototype,"title",void 0),Object(n.a)([Object(r.b)({attribute:"sub-title",mode:"fromView"})],s.prototype,"subTitle",void 0),Object(n.a)([a.c],s.prototype,"footerSlottedNodes",void 0),Object(n.a)([a.c],s.prototype,"startContentSlottedNodes",void 0);var c=i(93),l=i(75),u=i(139),A=i(136);const g=c.a`
    <template>
        <header class="header-fixed" part="header-fixed">
            <div class="header-title-wrapper">
                <span part="header-icon" aria-hidden="true" ${Object(l.a)("headerIconContainer")}>
                    <slot
                        name="header-icon"
                        ${Object(l.a)("headerIcon")}
                        @slotchange=${t=>t.handleHeaderIconContentChange()}
                    ></slot>
                </span>
                <h1 class="header-fixed-title" part="header-fixed-title">
                    ${t=>t.fixedTitle}
                </h1>
            </div>
            <div class="header-actions" part="header-actions">
                <slot name="header-actions"></slot>
            </div>
        </header>
        <div class="content" part="content">
            <div part="illustration-container" class="illustration-container">
                <slot name="illustration"></slot>
            </div>
            <div
                class="${t=>t.startContentSlottedNodes&&t.startContentSlottedNodes.length?"start-content":"start-content_hidden"}"
                part="start-content"
                ${Object(l.a)("startContentContainer")}
            >
                <slot
                    name="start-content"
                    ${Object(l.a)("startContent")}
                    ${Object(u.a)("startContentSlottedNodes")}
                    @slotchange="${t=>t.handleStartContentChange()}"
                ></slot>
            </div>
            <div class="header" part="header">
                <h2 class="header-title" part="header-title">${t=>t.title}</h2>
                ${Object(A.a)(t=>t.subTitle,c.a`
                        <p class="header-sub-title" part="header-sub-title">
                            ${t=>t.subTitle}
                        </p>
                    `)}
            </div>
            <div class="sections" part="sections">
                <slot></slot>
            </div>
            <footer
                class="${t=>t.footerSlottedNodes&&t.footerSlottedNodes.length?"footer":"footer_hidden"}"
                part="footer"
                ${Object(l.a)("footerContainer")}
            >
                <slot
                    name="footer"
                    ${Object(l.a)("footer")}
                    ${Object(u.a)("footerSlottedNodes")}
                    @slotchange="${t=>t.handleFooterContentChange()}"
                ></slot>
            </footer>
        </div>
    </template>
`;var h=i(30),d=i(103),I=i(11);const M=h.b`
    ${Object(d.a)("flex")} :host {
        --side-padding: 12px;
        flex-direction: column;
        height: 100%;
        font-size: var(--type-ramp-base-font-size);
        line-height: var(--type-ramp-base-line-height);
    }

    .content {
        overflow: hidden;
        overflow-y: auto;
    }

    .header-fixed {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 40px;
        border-bottom: calc(var(--outline-width) * 1px) solid
            ${I.h.var};
    }

    .header-title-wrapper {
        display: flex;
        align-items: center;
        margin-inline-start: 8px;
    }

    .header-icon {
        display: flex;
        margin-inline-start: 4px;
    }

    .header-fixed-title {
        display: inline-block;
        font-weight: 600;
        margin-inline-start: 8px;
        margin-top: 9px;
        margin-bottom: 11px;
        font-size: var(--type-ramp-base-font-size);
        line-height: var(--type-ramp-base-line-height);
    }

    .header-actions {
        margin: 4px;
    }

    .illustration-container {
        display: flex;
        justify-content: center;
    }

    .header {
        text-align: center;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        margin-bottom: 8px;
    }

    .header-title,
    .header-sub-title {
        display: inline;
    }

    .header-title {
        margin: 16px 16px 8px 16px;
        font-size: var(--type-ramp-plus-1-line-height);
        line-height: var(--type-ramp-plus-2-line-height);
    }

    .header-sub-title {
        margin: 0 32px 8px 32px;
        font-weight: 600;
        font-size: var(--type-ramp-base-font-size);
        line-height: var(--type-ramp-base-line-height);
    }

    .sections {
        padding: 0 var(--side-padding);
    }

    .footer {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 10px 12px;
        border-top: calc(var(--outline-width) * 1px) solid
            ${I.h.var};
    }

    .footer_hidden {
        display: none;
    }

    .start-content {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 10px 12px;
    }

    .start-content_hidden {
        display: none;
    }
`.withBehaviors(I.h);i.d(e,"a",(function(){return C}));let C=class extends s{};C=Object(n.a)([Object(o.b)({name:"msft-edge-shopping",template:g,styles:M})],C)},function(t,e,i){"use strict";function n(){return!("undefined"==typeof window||!window.document||!window.document.createElement)}var o=i(36),r=i(32);var a=function(t){return!0===t||!1===t||Object(r.a)(t)&&"[object Boolean]"==Object(o.a)(t)};function s(...t){return t.every(t=>t instanceof HTMLElement)}let c;function l(){if(a(c))return c;if(!n())return c=!1,c;const t=document.createElement("style");document.head.appendChild(t);try{t.sheet.insertRule("foo:focus-visible {color:inherit}",0),c=!0}catch(t){c=!1}finally{document.head.removeChild(t)}return c}i.d(e,"b",(function(){return s})),i.d(e,"a",(function(){return l}))},function(t,e,i){"use strict";var n=i(33),o=i(10);const r={aliceblue:{r:.941176,g:.972549,b:1},antiquewhite:{r:.980392,g:.921569,b:.843137},aqua:{r:0,g:1,b:1},aquamarine:{r:.498039,g:1,b:.831373},azure:{r:.941176,g:1,b:1},beige:{r:.960784,g:.960784,b:.862745},bisque:{r:1,g:.894118,b:.768627},black:{r:0,g:0,b:0},blanchedalmond:{r:1,g:.921569,b:.803922},blue:{r:0,g:0,b:1},blueviolet:{r:.541176,g:.168627,b:.886275},brown:{r:.647059,g:.164706,b:.164706},burlywood:{r:.870588,g:.721569,b:.529412},cadetblue:{r:.372549,g:.619608,b:.627451},chartreuse:{r:.498039,g:1,b:0},chocolate:{r:.823529,g:.411765,b:.117647},coral:{r:1,g:.498039,b:.313725},cornflowerblue:{r:.392157,g:.584314,b:.929412},cornsilk:{r:1,g:.972549,b:.862745},crimson:{r:.862745,g:.078431,b:.235294},cyan:{r:0,g:1,b:1},darkblue:{r:0,g:0,b:.545098},darkcyan:{r:0,g:.545098,b:.545098},darkgoldenrod:{r:.721569,g:.52549,b:.043137},darkgray:{r:.662745,g:.662745,b:.662745},darkgreen:{r:0,g:.392157,b:0},darkgrey:{r:.662745,g:.662745,b:.662745},darkkhaki:{r:.741176,g:.717647,b:.419608},darkmagenta:{r:.545098,g:0,b:.545098},darkolivegreen:{r:.333333,g:.419608,b:.184314},darkorange:{r:1,g:.54902,b:0},darkorchid:{r:.6,g:.196078,b:.8},darkred:{r:.545098,g:0,b:0},darksalmon:{r:.913725,g:.588235,b:.478431},darkseagreen:{r:.560784,g:.737255,b:.560784},darkslateblue:{r:.282353,g:.239216,b:.545098},darkslategray:{r:.184314,g:.309804,b:.309804},darkslategrey:{r:.184314,g:.309804,b:.309804},darkturquoise:{r:0,g:.807843,b:.819608},darkviolet:{r:.580392,g:0,b:.827451},deeppink:{r:1,g:.078431,b:.576471},deepskyblue:{r:0,g:.74902,b:1},dimgray:{r:.411765,g:.411765,b:.411765},dimgrey:{r:.411765,g:.411765,b:.411765},dodgerblue:{r:.117647,g:.564706,b:1},firebrick:{r:.698039,g:.133333,b:.133333},floralwhite:{r:1,g:.980392,b:.941176},forestgreen:{r:.133333,g:.545098,b:.133333},fuchsia:{r:1,g:0,b:1},gainsboro:{r:.862745,g:.862745,b:.862745},ghostwhite:{r:.972549,g:.972549,b:1},gold:{r:1,g:.843137,b:0},goldenrod:{r:.854902,g:.647059,b:.12549},gray:{r:.501961,g:.501961,b:.501961},green:{r:0,g:.501961,b:0},greenyellow:{r:.678431,g:1,b:.184314},grey:{r:.501961,g:.501961,b:.501961},honeydew:{r:.941176,g:1,b:.941176},hotpink:{r:1,g:.411765,b:.705882},indianred:{r:.803922,g:.360784,b:.360784},indigo:{r:.294118,g:0,b:.509804},ivory:{r:1,g:1,b:.941176},khaki:{r:.941176,g:.901961,b:.54902},lavender:{r:.901961,g:.901961,b:.980392},lavenderblush:{r:1,g:.941176,b:.960784},lawngreen:{r:.486275,g:.988235,b:0},lemonchiffon:{r:1,g:.980392,b:.803922},lightblue:{r:.678431,g:.847059,b:.901961},lightcoral:{r:.941176,g:.501961,b:.501961},lightcyan:{r:.878431,g:1,b:1},lightgoldenrodyellow:{r:.980392,g:.980392,b:.823529},lightgray:{r:.827451,g:.827451,b:.827451},lightgreen:{r:.564706,g:.933333,b:.564706},lightgrey:{r:.827451,g:.827451,b:.827451},lightpink:{r:1,g:.713725,b:.756863},lightsalmon:{r:1,g:.627451,b:.478431},lightseagreen:{r:.12549,g:.698039,b:.666667},lightskyblue:{r:.529412,g:.807843,b:.980392},lightslategray:{r:.466667,g:.533333,b:.6},lightslategrey:{r:.466667,g:.533333,b:.6},lightsteelblue:{r:.690196,g:.768627,b:.870588},lightyellow:{r:1,g:1,b:.878431},lime:{r:0,g:1,b:0},limegreen:{r:.196078,g:.803922,b:.196078},linen:{r:.980392,g:.941176,b:.901961},magenta:{r:1,g:0,b:1},maroon:{r:.501961,g:0,b:0},mediumaquamarine:{r:.4,g:.803922,b:.666667},mediumblue:{r:0,g:0,b:.803922},mediumorchid:{r:.729412,g:.333333,b:.827451},mediumpurple:{r:.576471,g:.439216,b:.858824},mediumseagreen:{r:.235294,g:.701961,b:.443137},mediumslateblue:{r:.482353,g:.407843,b:.933333},mediumspringgreen:{r:0,g:.980392,b:.603922},mediumturquoise:{r:.282353,g:.819608,b:.8},mediumvioletred:{r:.780392,g:.082353,b:.521569},midnightblue:{r:.098039,g:.098039,b:.439216},mintcream:{r:.960784,g:1,b:.980392},mistyrose:{r:1,g:.894118,b:.882353},moccasin:{r:1,g:.894118,b:.709804},navajowhite:{r:1,g:.870588,b:.678431},navy:{r:0,g:0,b:.501961},oldlace:{r:.992157,g:.960784,b:.901961},olive:{r:.501961,g:.501961,b:0},olivedrab:{r:.419608,g:.556863,b:.137255},orange:{r:1,g:.647059,b:0},orangered:{r:1,g:.270588,b:0},orchid:{r:.854902,g:.439216,b:.839216},palegoldenrod:{r:.933333,g:.909804,b:.666667},palegreen:{r:.596078,g:.984314,b:.596078},paleturquoise:{r:.686275,g:.933333,b:.933333},palevioletred:{r:.858824,g:.439216,b:.576471},papayawhip:{r:1,g:.937255,b:.835294},peachpuff:{r:1,g:.854902,b:.72549},peru:{r:.803922,g:.521569,b:.247059},pink:{r:1,g:.752941,b:.796078},plum:{r:.866667,g:.627451,b:.866667},powderblue:{r:.690196,g:.878431,b:.901961},purple:{r:.501961,g:0,b:.501961},red:{r:1,g:0,b:0},rosybrown:{r:.737255,g:.560784,b:.560784},royalblue:{r:.254902,g:.411765,b:.882353},saddlebrown:{r:.545098,g:.270588,b:.07451},salmon:{r:.980392,g:.501961,b:.447059},sandybrown:{r:.956863,g:.643137,b:.376471},seagreen:{r:.180392,g:.545098,b:.341176},seashell:{r:1,g:.960784,b:.933333},sienna:{r:.627451,g:.321569,b:.176471},silver:{r:.752941,g:.752941,b:.752941},skyblue:{r:.529412,g:.807843,b:.921569},slateblue:{r:.415686,g:.352941,b:.803922},slategray:{r:.439216,g:.501961,b:.564706},slategrey:{r:.439216,g:.501961,b:.564706},snow:{r:1,g:.980392,b:.980392},springgreen:{r:0,g:1,b:.498039},steelblue:{r:.27451,g:.509804,b:.705882},tan:{r:.823529,g:.705882,b:.54902},teal:{r:0,g:.501961,b:.501961},thistle:{r:.847059,g:.74902,b:.847059},tomato:{r:1,g:.388235,b:.278431},transparent:{r:0,g:0,b:0,a:0},turquoise:{r:.25098,g:.878431,b:.815686},violet:{r:.933333,g:.509804,b:.933333},wheat:{r:.960784,g:.870588,b:.701961},white:{r:1,g:1,b:1},whitesmoke:{r:.960784,g:.960784,b:.960784},yellow:{r:1,g:1,b:0},yellowgreen:{r:.603922,g:.803922,b:.196078}};i.d(e,"a",(function(){return u})),i.d(e,"b",(function(){return g})),i.d(e,"d",(function(){return h})),i.d(e,"e",(function(){return d})),i.d(e,"c",(function(){return I}));const a=/^rgb\(\s*((?:(?:25[0-5]|2[0-4]\d|1\d\d|\d{1,2})\s*,\s*){2}(?:25[0-5]|2[0-4]\d|1\d\d|\d{1,2})\s*)\)$/i,s=/^rgba\(\s*((?:(?:25[0-5]|2[0-4]\d|1\d\d|\d{1,2})\s*,\s*){3}(?:0|1|0?\.\d*)\s*)\)$/i,c=/^#((?:[0-9a-f]{6}|[0-9a-f]{3}))$/i,l=/^#((?:[0-9a-f]{8}|[0-9a-f]{4}))$/i;function u(t){return c.test(t)}function A(t){return function(t){return l.test(t)}(t)}function g(t){return a.test(t)}function h(t){const e=c.exec(t);if(null===e)return null;let i=e[1];if(3===i.length){const t=i.charAt(0),e=i.charAt(1),n=i.charAt(2);i=t.concat(t,e,e,n,n)}const r=parseInt(i,16);return isNaN(r)?null:new n.a(Object(o.e)((16711680&r)>>>16,0,255),Object(o.e)((65280&r)>>>8,0,255),Object(o.e)(255&r,0,255),1)}function d(t){const e=a.exec(t);if(null===e)return null;const i=e[1].split(",");return new n.a(Object(o.e)(Number(i[0]),0,255),Object(o.e)(Number(i[1]),0,255),Object(o.e)(Number(i[2]),0,255),1)}function I(t){const e=t.toLowerCase();return u(e)?h(e):A(e)?function(t){const e=l.exec(t);if(null===e)return null;let i=e[1];if(4===i.length){const t=i.charAt(0),e=i.charAt(1),n=i.charAt(2),o=i.charAt(3);i=t.concat(t,e,e,n,n,o,o)}const r=parseInt(i,16);return isNaN(r)?null:new n.a(Object(o.e)((16711680&r)>>>16,0,255),Object(o.e)((65280&r)>>>8,0,255),Object(o.e)(255&r,0,255),Object(o.e)((4278190080&r)>>>24,0,255))}(e):g(e)?d(e):function(t){return s.test(t)}(e)?function(t){const e=s.exec(t);if(null===e)return null;const i=e[1].split(",");return 4===i.length?new n.a(Object(o.e)(Number(i[0]),0,255),Object(o.e)(Number(i[1]),0,255),Object(o.e)(Number(i[2]),0,255),Number(i[3])):null}(e):function(t){return r.hasOwnProperty(t)}(e)?function(t){const e=r[t.toLowerCase()];return e?new n.a(e.r,e.g,e.b,e.hasOwnProperty("a")?e.a:void 0):null}(e):null}},,function(t,e,i){"use strict";var n=i(5),o=i(21),r=i(110),a=i(9),s=i(134),c=i(34),l=i(45);class u extends r.a{}Object(a.a)([o.b],u.prototype,"download",void 0),Object(a.a)([o.b],u.prototype,"href",void 0),Object(a.a)([o.b],u.prototype,"hreflang",void 0),Object(a.a)([o.b],u.prototype,"ping",void 0),Object(a.a)([o.b],u.prototype,"referrerpolicy",void 0),Object(a.a)([o.b],u.prototype,"rel",void 0),Object(a.a)([o.b],u.prototype,"target",void 0),Object(a.a)([o.b],u.prototype,"type",void 0);class A extends s.a{}Object(a.a)([Object(o.b)({attribute:"aria-expanded",mode:"fromView"})],A.prototype,"ariaExpanded",void 0),Object(l.a)(u,c.a,A);const g=i(93).a`
    <a
        class="control"
        part="control"
        download="${t=>t.download}"
        href="${t=>t.href}"
        hreflang="${t=>t.hreflang}"
        ping="${t=>t.ping}"
        referrerpolicy="${t=>t.referrerpolicy}"
        rel="${t=>t.rel}"
        target="${t=>t.target}"
        type="${t=>t.type}"
        aria-atomic="${t=>t.ariaAtomic}"
        aria-busy="${t=>t.ariaBusy}"
        aria-controls="${t=>t.ariaControls}"
        aria-current="${t=>t.ariaCurrent}"
        aria-describedBy="${t=>t.ariaDescribedby}"
        aria-details="${t=>t.ariaDetails}"
        aria-disabled="${t=>t.ariaDisabled}"
        aria-errormessage="${t=>t.ariaErrormessage}"
        aria-expanded="${t=>t.ariaExpanded}"
        aria-flowto="${t=>t.ariaFlowto}"
        aria-haspopup="${t=>t.ariaHaspopup}"
        aria-hidden="${t=>t.ariaHidden}"
        aria-invalid="${t=>t.ariaInvalid}"
        aria-keyshortcuts="${t=>t.ariaKeyshortcuts}"
        aria-label="${t=>t.ariaLabel}"
        aria-labelledby="${t=>t.ariaLabelledby}"
        aria-live="${t=>t.ariaLive}"
        aria-owns="${t=>t.ariaOwns}"
        aria-relevant="${t=>t.ariaRelevant}"
        aria-roledescription="${t=>t.ariaRoledescription}"
    >
        ${c.c}
        <span class="content" part="content">
            <slot></slot>
        </span>
        ${c.b}
    </a>
`;var h=i(30),d=i(135),I=i(11);const M=h.b`
    ${d.b}
    ${d.a}
    ${d.c}
    ${d.d}
    ${d.e}
    ${d.f}
`.withBehaviors(I.a,I.b,I.c,I.d,I.e,I.f,I.g,I.i,I.j,I.k,I.l,I.m,I.n,I.o,I.p,I.q,I.v,I.y,I.z,I.A);i.d(e,"a",(function(){return C}));let C=class extends u{appearanceChanged(t,e){t!==e&&(this.classList.add(e),this.classList.remove(t))}connectedCallback(){super.connectedCallback(),this.appearance||(this.appearance="neutral")}};Object(n.a)([o.b],C.prototype,"appearance",void 0),C=Object(n.a)([Object(r.b)({name:"fluent-anchor",template:g,styles:M,shadowOptions:{delegatesFocus:!0,mode:"closed"}})],C)},function(t,e,i){"use strict";var n=i(19),o=i(110),r=i(21);class a extends o.a{constructor(){super(...arguments),this.accordion=!1,this.titleLevel=2}connectedCallback(){super.connectedCallback(),null!==this.expanded&&void 0!==this.expanded||(this.expanded=!0)}}Object(n.a)([Object(r.b)({mode:"boolean"})],a.prototype,"accordion",void 0),Object(n.a)([Object(r.b)({mode:"boolean"})],a.prototype,"expanded",void 0),Object(n.a)([Object(r.b)({mode:"fromView"})],a.prototype,"title",void 0),Object(n.a)([Object(r.b)({attribute:"title-level",mode:"fromView",converter:r.c})],a.prototype,"titleLevel",void 0);var s=i(93),c=i(136);const l=s.a`
    <template>
        ${Object(c.a)(t=>t.accordion,s.a`
                <fluent-accordion part="control">
                    <fluent-accordion-item
                        part="item"
                        class="item"
                        expanded="${t=>t.expanded?"true":"false"}"
                        heading-level="${t=>t.titleLevel}"
                    >
                        <svg
                            class="icon"
                            part="expanded-icon"
                            slot="expanded-icon"
                            width="12"
                            height="12"
                            viewBox="0 0 12 12"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                                fill="currentcolor"
                                d="M11.4551 8.98242L6 3.5332L0.544922 8.98242L0.0175781 8.45508L6 2.4668L11.9824 8.45508L11.4551 8.98242Z"
                            />
                        </svg>
                        <svg
                            class="icon"
                            part="collapsed-icon"
                            slot="collapsed-icon"
                            width="12"
                            height="12"
                            viewBox="0 0 12 12"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                                fill="currentcolor"
                                d="M11.3613 2.73633L11.8887 3.26367L6 9.15234L0.111328 3.26367L0.638672 2.73633L6 8.09766L11.3613 2.73633Z"
                                fill="black"
                            />
                        </svg>

                        <span part="title" slot="heading">${t=>t.title}</span>
                        <slot></slot>
                        <div class="action" part="action">
                            <slot name="action"></slot>
                        </div>
                    </fluent-accordion-item>
                </fluent-accordion>
            `)}
        ${Object(c.a)(t=>!t.accordion,s.a`
                <div class="control" part="control">
                    <span
                        class="heading"
                        part="title"
                        slot="heading"
                        role="heading"
                        aria-level="${t=>t.titleLevel}"
                        >${t=>t.title}</span
                    >
                    <slot></slot>
                    <div class="action" part="action">
                        <slot name="action"></slot>
                    </div>
                </div>
            `)}
    </template>
`;var u=i(30),A=i(11);const g=u.b`
    :host {
        overflow: hidden;
    }

    fluent-accordion {
        border: none;
        margin-bottom: 10px;
    }

    fluent-accordion-item {
        border: none;
    }

    fluent-accordion-item::part(heading),
    .heading {
        margin-bottom: 6px;
        font-size: var(--type-ramp-minus-1-font-size);
    }

    .heading {
        margin-inline-start: 5px;
        display: inline-block;
        line-height: var(--type-ramp-plus-1-line-height);
        margin-bottom: 6px;
    }

    fluent-accordion-item::part(region) {
        padding: 0;
        flex-direction: column;
    }

    .action {
        color: ${A.c.var};
        font-size: var(--type-ramp-minus-1-font-size);
        height: 38px;
        display: flex;
        margin-inline-start: 16px;
        align-items: center;
    }
`.withBehaviors(A.c);i.d(e,"a",(function(){return h}));let h=class extends a{};h=Object(n.a)([Object(o.b)({name:"msft-edge-shopping-section",template:l,styles:g})],h)},function(t,e,i){"use strict";var n=i(30),o=i(21),r=i(24);const a={mode:"open"},s={},c=new Map;class l{constructor(t,e=t.definition){"string"==typeof e&&(e={name:e}),this.type=t,this.name=e.name,this.template=e.template;const i=o.a.collect(t,e.attributes),r=new Array(i.length),c={},l={};for(let t=0,e=i.length;t<e;++t){const e=i[t];r[t]=e.attribute,c[e.name]=e,l[e.attribute]=e}this.attributes=i,this.observedAttributes=r,this.propertyLookup=c,this.attributeLookup=l,this.shadowOptions=void 0===e.shadowOptions?a:null===e.shadowOptions?void 0:Object.assign(Object.assign({},a),e.shadowOptions),this.elementOptions=void 0===e.elementOptions?s:Object.assign(Object.assign({},s),e.elementOptions),this.styles=void 0===e.styles?void 0:Array.isArray(e.styles)?n.a.create(e.styles):e.styles instanceof n.a?e.styles:n.a.create([e.styles])}define(t=customElements){const e=this.type;if(!this.isDefined){const t=this.attributes,i=e.prototype;for(let e=0,n=t.length;e<n;++e)r.a.defineProperty(i,t[e]);Reflect.defineProperty(e,"observedAttributes",{value:this.observedAttributes,enumerable:!0}),c.set(e,this),this.isDefined=!0}return t.get(this.name)||t.define(this.name,e,this.elementOptions),this}static forType(t){return c.get(t)}}var u=i(59),A=i(18),g=function(t,e,i,n){var o,r=arguments.length,a=r<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,i):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,i,n);else for(var s=t.length-1;s>=0;s--)(o=t[s])&&(a=(r<3?o(a):r>3?o(e,i,a):o(e,i))||a);return r>3&&a&&Object.defineProperty(e,i,a),a};const h=new WeakMap,d={bubbles:!0,composed:!0};function I(t){return t.shadowRoot||h.get(t)||null}class M extends u.a{constructor(t,e){super(t),this.boundObservables=null,this.behaviors=null,this.needsInitialization=!0,this._template=null,this._styles=null,this.view=null,this.isConnected=!1,this.element=t,this.definition=e;const i=e.shadowOptions;if(void 0!==i){const e=t.attachShadow(i);"closed"===i.mode&&h.set(t,e)}const n=r.a.getAccessors(t);if(n.length>0){const e=this.boundObservables=Object.create(null);for(let i=0,o=n.length;i<o;++i){const o=n[i].name,r=t[o];void 0!==r&&(delete t[o],e[o]=r)}}}get template(){return this._template}set template(t){this._template!==t&&(this._template=t,this.needsInitialization||this.renderTemplate(t))}get styles(){return this._styles}set styles(t){this._styles!==t&&(null!==this._styles&&this.removeStyles(this._styles),this._styles=t,this.needsInitialization||null===t||this.addStyles(t))}addStyles(t){const e=I(this.element)||this.element.getRootNode();if(t instanceof HTMLStyleElement)e.prepend(t);else{const i=t.behaviors;t.addStylesTo(e),null!==i&&this.addBehaviors(i)}}removeStyles(t){const e=I(this.element)||this.element.getRootNode();if(t instanceof HTMLStyleElement)e.removeChild(t);else{const i=t.behaviors;t.removeStylesFrom(e),null!==i&&this.removeBehaviors(i)}}addBehaviors(t){const e=this.behaviors||(this.behaviors=[]),i=t.length;for(let n=0;n<i;++n)e.push(t[n]);if(this.isConnected){const e=this.element;for(let n=0;n<i;++n)t[n].bind(e,r.b)}}removeBehaviors(t){const e=this.behaviors;if(null===e)return;const i=t.length;for(let n=0;n<i;++n){const i=e.indexOf(t[n]);-1!==i&&e.splice(i,1)}if(this.isConnected){const e=this.element;for(let n=0;n<i;++n)t[n].unbind(e)}}onConnectedCallback(){if(this.isConnected)return;const t=this.element;this.needsInitialization?this.finishInitialization():null!==this.view&&this.view.bind(t,r.b);const e=this.behaviors;if(null!==e)for(let i=0,n=e.length;i<n;++i)e[i].bind(t,r.b);this.isConnected=!0}onDisconnectedCallback(){if(!1===this.isConnected)return;this.isConnected=!1;const t=this.view;null!==t&&t.unbind();const e=this.behaviors;if(null!==e){const t=this.element;for(let i=0,n=e.length;i<n;++i)e[i].unbind(t)}}onAttributeChangedCallback(t,e,i){const n=this.definition.attributeLookup[t];void 0!==n&&n.onAttributeChangedCallback(this.element,i)}emit(t,e,i){return!!this.isConnected&&this.element.dispatchEvent(new CustomEvent(t,Object.assign(Object.assign({detail:e},d),i)))}finishInitialization(){const t=this.element,e=this.boundObservables;if(null!==e){const i=Object.keys(e);for(let n=0,o=i.length;n<o;++n){const o=i[n];t[o]=e[o]}this.boundObservables=null}const i=this.definition;null===this._template&&(this.element.resolveTemplate?this._template=this.element.resolveTemplate():i.template&&(this._template=i.template||null)),null!==this._template&&this.renderTemplate(this._template),null===this._styles&&(this.element.resolveStyles?this._styles=this.element.resolveStyles():i.styles&&(this._styles=i.styles||null)),null!==this._styles&&this.addStyles(this._styles),this.needsInitialization=!1}renderTemplate(t){const e=this.element,i=I(e)||e;null!==this.view?(this.view.dispose(),this.view=null):this.needsInitialization||A.a.removeChildNodes(i),t&&(this.view=t.render(e,i,e))}static forCustomElement(t){const e=t.$fastController;if(void 0!==e)return e;const i=l.forType(t.constructor);if(void 0===i)throw new Error("Missing FASTElement definition.");return t.$fastController=new M(t,i)}}function C(t){return class extends t{constructor(){super(),M.forCustomElement(this)}$emit(t,e,i){return this.$fastController.emit(t,e,i)}connectedCallback(){this.$fastController.onConnectedCallback()}disconnectedCallback(){this.$fastController.onDisconnectedCallback()}attributeChangedCallback(t,e,i){this.$fastController.onAttributeChangedCallback(t,e,i)}}}g([r.c],M.prototype,"isConnected",void 0),i.d(e,"a",(function(){return p})),i.d(e,"b",(function(){return f}));const p=Object.assign(C(HTMLElement),{from:t=>C(t),define:(t,e)=>new l(t,e).define().type});function f(t){return function(e){new l(e,t).define()}}},,,,,,function(t,e,i){"use strict";var n=i(5),o=i(21),r=i(110),a=i(9),s=i(24),c=i(134),l=i(34),u=i(45),A=i(87),g=i(18),h=i(129);const d="ElementInternals"in window&&"setFormValue"in window.ElementInternals.prototype,I=new Map;class M extends(function(t){const e=class extends t{constructor(...t){super(...t),this.dirtyValue=!1,this.disabled=!1,this.proxyEventsToBlock=["change","click"],this.proxyInitialized=!1,this.required=!1,this.initialValue=this.initialValue||""}static get formAssociated(){return d}get validity(){return d?this.elementInternals.validity:this.proxy.validity}get form(){return d?this.elementInternals.form:this.proxy.form}get validationMessage(){return d?this.elementInternals.validationMessage:this.proxy.validationMessage}get willValidate(){return d?this.elementInternals.willValidate:this.proxy.willValidate}get labels(){if(d)return Object.freeze(Array.from(this.elementInternals.labels));if(this.proxy instanceof HTMLElement&&this.proxy.ownerDocument&&this.id){const t=this.proxy.labels,e=Array.from(this.proxy.getRootNode().querySelectorAll(`[for='${this.id}']`)),i=t?e.concat(Array.from(t)):e;return Object.freeze(i)}return A.a}valueChanged(t,e){this.dirtyValue=!0,this.proxy instanceof HTMLElement&&(this.proxy.value=this.value),this.setFormValue(this.value),this.validate()}initialValueChanged(t,e){this.dirtyValue||(this.value=this.initialValue,this.dirtyValue=!1)}disabledChanged(t,e){this.proxy instanceof HTMLElement&&(this.proxy.disabled=this.disabled),g.a.queueUpdate(()=>this.classList.toggle("disabled",this.disabled))}nameChanged(t,e){this.proxy instanceof HTMLElement&&(this.proxy.name=this.name)}requiredChanged(t,e){this.proxy instanceof HTMLElement&&(this.proxy.required=this.required),g.a.queueUpdate(()=>this.classList.toggle("required",this.required)),this.validate()}get elementInternals(){if(!d)return null;let t=I.get(this);return t||(t=this.attachInternals(),I.set(this,t)),t}connectedCallback(){super.connectedCallback(),this.addEventListener("keypress",this._keypressHandler),this.value||(this.value=this.initialValue,this.dirtyValue=!1),d||this.attachProxy()}disconnectedCallback(){this.proxyEventsToBlock.forEach(t=>this.proxy.removeEventListener(t,this.stopPropagation))}checkValidity(){return d?this.elementInternals.checkValidity():this.proxy.checkValidity()}reportValidity(){return d?this.elementInternals.reportValidity():this.proxy.reportValidity()}setValidity(t,e,i){d?this.elementInternals.setValidity(t,e,i):"string"==typeof e&&this.proxy.setCustomValidity(e)}formDisabledCallback(t){this.disabled=t}formResetCallback(){this.value=this.initialValue,this.dirtyValue=!1}attachProxy(){var t;this.proxyInitialized||(this.proxyInitialized=!0,this.proxy.style.display="none",this.proxyEventsToBlock.forEach(t=>this.proxy.addEventListener(t,this.stopPropagation)),this.proxy.disabled=this.disabled,this.proxy.required=this.required,"string"==typeof this.name&&(this.proxy.name=this.name),"string"==typeof this.value&&(this.proxy.value=this.value),this.proxy.setAttribute("slot","form-associated-proxy"),this.proxySlot=document.createElement("slot"),this.proxySlot.setAttribute("name","form-associated-proxy")),null===(t=this.shadowRoot)||void 0===t||t.appendChild(this.proxySlot),this.appendChild(this.proxy)}detachProxy(){var t;this.removeChild(this.proxy),null===(t=this.shadowRoot)||void 0===t||t.removeChild(this.proxySlot)}validate(){this.proxy instanceof HTMLElement&&this.setValidity(this.proxy.validity,this.proxy.validationMessage)}setFormValue(t,e){d&&this.elementInternals&&this.elementInternals.setFormValue(t,e||t)}_keypressHandler(t){switch(t.keyCode){case h.f:if(this.form instanceof HTMLFormElement){const t=this.form.querySelector("[type=submit]");null==t||t.click()}}}stopPropagation(t){t.stopPropagation()}};return Object(o.b)({mode:"boolean"})(e.prototype,"disabled"),Object(o.b)({mode:"fromView",attribute:"value"})(e.prototype,"initialValue"),Object(o.b)(e.prototype,"name"),Object(o.b)({mode:"boolean"})(e.prototype,"required"),Object(s.c)(e.prototype,"value"),e}(class extends r.a{constructor(){super(...arguments),this.proxy=document.createElement("input")}})){}class C extends M{constructor(){super(...arguments),this.handleSubmission=()=>{if(!this.form)return;const t=this.proxy.isConnected;t||this.attachProxy(),"function"==typeof this.form.requestSubmit?this.form.requestSubmit(this.proxy):this.proxy.click(),t||this.detachProxy()},this.handleFormReset=()=>{var t;null===(t=this.form)||void 0===t||t.reset()}}formactionChanged(){this.proxy instanceof HTMLElement&&(this.proxy.formAction=this.formaction)}formenctypeChanged(){this.proxy instanceof HTMLElement&&(this.proxy.formEnctype=this.formenctype)}formmethodChanged(){this.proxy instanceof HTMLElement&&(this.proxy.formMethod=this.formmethod)}formnovalidateChanged(){this.proxy instanceof HTMLElement&&(this.proxy.formNoValidate=this.formnovalidate)}formtargetChanged(){this.proxy instanceof HTMLElement&&(this.proxy.formTarget=this.formtarget)}typeChanged(t,e){this.proxy instanceof HTMLElement&&(this.proxy.type=this.type),"submit"===e&&this.addEventListener("click",this.handleSubmission),"submit"===t&&this.removeEventListener("click",this.handleSubmission),"reset"===e&&this.addEventListener("click",this.handleFormReset),"reset"===t&&this.removeEventListener("click",this.handleFormReset)}connectedCallback(){super.connectedCallback(),this.proxy.setAttribute("type",this.type)}}Object(a.a)([Object(o.b)({mode:"boolean"})],C.prototype,"autofocus",void 0),Object(a.a)([Object(o.b)({attribute:"form"})],C.prototype,"formId",void 0),Object(a.a)([o.b],C.prototype,"formaction",void 0),Object(a.a)([o.b],C.prototype,"formenctype",void 0),Object(a.a)([o.b],C.prototype,"formmethod",void 0),Object(a.a)([Object(o.b)({mode:"boolean"})],C.prototype,"formnovalidate",void 0),Object(a.a)([o.b],C.prototype,"formtarget",void 0),Object(a.a)([o.b],C.prototype,"type",void 0),Object(a.a)([s.c],C.prototype,"defaultSlottedContent",void 0);class p extends c.a{}Object(a.a)([Object(o.b)({attribute:"aria-expanded",mode:"fromView"})],p.prototype,"ariaExpanded",void 0),Object(a.a)([Object(o.b)({attribute:"aria-pressed",mode:"fromView"})],p.prototype,"ariaPressed",void 0),Object(u.a)(C,l.a,p);var f=i(93),D=i(75),y=i(139);const b=f.a`
    <button
        class="control"
        part="control"
        ?autofocus="${t=>t.autofocus}"
        ?disabled="${t=>t.disabled}"
        form="${t=>t.formId}"
        formaction="${t=>t.formaction}"
        formenctype="${t=>t.formenctype}"
        formmethod="${t=>t.formmethod}"
        formnovalidate="${t=>t.formnovalidate}"
        formtarget="${t=>t.formtarget}"
        name="${t=>t.name}"
        type="${t=>t.type}"
        value="${t=>t.value}"
        aria-atomic="${t=>t.ariaAtomic}"
        aria-busy="${t=>t.ariaBusy}"
        aria-controls="${t=>t.ariaControls}"
        aria-current="${t=>t.ariaCurrent}"
        aria-describedBy="${t=>t.ariaDescribedby}"
        aria-details="${t=>t.ariaDetails}"
        aria-disabled="${t=>t.ariaDisabled}"
        aria-errormessage="${t=>t.ariaErrormessage}"
        aria-expanded="${t=>t.ariaExpanded}"
        aria-flowto="${t=>t.ariaFlowto}"
        aria-haspopup="${t=>t.ariaHaspopup}"
        aria-hidden="${t=>t.ariaHidden}"
        aria-invalid="${t=>t.ariaInvalid}"
        aria-keyshortcuts="${t=>t.ariaKeyshortcuts}"
        aria-label="${t=>t.ariaLabel}"
        aria-labelledby="${t=>t.ariaLabelledby}"
        aria-live="${t=>t.ariaLive}"
        aria-owns="${t=>t.ariaOwns}"
        aria-pressed="${t=>t.ariaPressed}"
        aria-relevant="${t=>t.ariaRelevant}"
        aria-roledescription="${t=>t.ariaRoledescription}"
        ${Object(D.a)("root")}
    >
        ${l.c}
        <span part="content">
            <slot ${Object(y.a)("defaultSlottedContent")}></slot>
        </span>
        ${l.b}
    </button>
`;var j=i(30),E=i(135),m=i(11);const N=j.b`
    ${E.b}
    ${E.a}
    ${E.d}
    ${E.e}
    ${E.f}
`.withBehaviors(m.a,m.b,m.c,m.d,m.e,m.f,m.g,m.i,m.j,m.k,m.l,m.m,m.n,m.o,m.p,m.q,m.v,m.y,m.z,m.A);i.d(e,"a",(function(){return O}));let O=class extends C{appearanceChanged(t,e){t!==e&&(this.classList.add(e),this.classList.remove(t))}connectedCallback(){super.connectedCallback(),this.appearance||(this.appearance="neutral")}defaultSlottedContentChanged(){const t=this.defaultSlottedContent.filter(t=>t.nodeType===Node.ELEMENT_NODE);1===t.length&&t[0]instanceof SVGElement?this.root.classList.add("icon-only"):this.root.classList.remove("icon-only")}};Object(n.a)([o.b],O.prototype,"appearance",void 0),O=Object(n.a)([Object(r.b)({name:"fluent-button",template:b,styles:N,shadowOptions:{delegatesFocus:!0,mode:"closed"}})],O)},,,function(t,e,i){"use strict";var n=i(5),o=i(110),r=i(9),a=i(21),s=i(24),c=i(129);var l,u=i(144);!function(t){t.single="single",t.multi="multi"}(l||(l={}));class A extends o.a{constructor(){super(...arguments),this.expandmode=l.multi,this.activeItemIndex=0,this.change=()=>{this.$emit("change")},this.setItems=()=>{this.accordionIds=this.getItemIds(),this.accordionItems.forEach((t,e)=>{t instanceof u.a&&(t.addEventListener("change",this.activeItemChange),this.isSingleExpandMode()&&(this.activeItemIndex!==e?t.expanded=!1:t.expanded=!0));const i=this.accordionIds[e];t.setAttribute("id","string"!=typeof i?"accordion-"+(e+1):i),this.activeid=this.accordionIds[this.activeItemIndex],t.addEventListener("keydown",this.handleItemKeyDown)})},this.removeItemListeners=t=>{t.forEach((t,e)=>{t.removeEventListener("change",this.activeItemChange),t.removeEventListener("keydown",this.handleItemKeyDown)})},this.activeItemChange=t=>{const e=t.target;this.isSingleExpandMode()&&(this.resetItems(),t.target.expanded=!0),this.activeid=t.target.getAttribute("id"),this.activeItemIndex=Array.from(this.accordionItems).indexOf(e),this.change()},this.handleItemKeyDown=t=>{const e=t.keyCode;switch(this.accordionIds=this.getItemIds(),e){case c.d:t.preventDefault(),this.adjust(-1);break;case c.a:t.preventDefault(),this.adjust(1);break;case c.g:this.activeItemIndex=0,this.focusItem();break;case c.e:this.activeItemIndex=this.accordionItems.length-1,this.focusItem()}}}accordionItemsChanged(t,e){this.$fastController.isConnected&&(this.removeItemListeners(t),this.accordionIds=this.getItemIds(),this.setItems())}resetItems(){this.accordionItems.forEach((t,e)=>{t.expanded=!1})}getItemIds(){return this.accordionItems.map(t=>t.getAttribute("id"))}isSingleExpandMode(){return this.expandmode===l.single}adjust(t){var e,i,n;this.activeItemIndex=(e=0,i=this.accordionItems.length-1,(n=this.activeItemIndex+t)<e?i:n>i?e:n),this.focusItem()}focusItem(){const t=this.accordionItems[this.activeItemIndex];t instanceof u.a&&t.expandbutton.focus()}}Object(r.a)([Object(a.b)({attribute:"expand-mode"})],A.prototype,"expandmode",void 0),Object(r.a)([s.c],A.prototype,"accordionItems",void 0);var g=i(93),h=i(139);const d=g.a`
    <template>
        <slot name="item" part="item" ${Object(h.a)("accordionItems")}></slot>
    </template>
`;var I=i(30),M=i(103),C=i(11);const p=I.b`
  ${Object(M.a)("flex")} :host {
    box-sizing: border-box;
    flex-direction: column;
    font-family: var(--body-font);
    font-size: var(--type-ramp-minus-1-font-size);
    line-height: var(--type-ramp-minus-1-line-height);
    color: ${C.v.var};
    border-top: calc(var(--outline-width) * 1px) solid ${C.h.var};
  }
`.withBehaviors(C.c,C.h,C.v);i.d(e,"a",(function(){return f}));let f=class extends A{};f=Object(n.a)([Object(o.b)({name:"fluent-accordion",template:d,styles:p,shadowOptions:{mode:"closed"}})],f)},function(t,e,i){"use strict";var n=i(5),o=i(110),r=i(9),a=i(21),s=i(129),c=i(34),l=i(45),u=i(60);class A extends o.a{constructor(){super(...arguments),this.role=u.a.menuitem,this.handleMenuItemKeyDown=t=>{switch(t.keyCode){case s.f:case s.h:return this.invoke(),!1}return!0},this.handleMenuItemClick=t=>{this.invoke()},this.invoke=()=>{if(!this.disabled){switch(this.role){case u.a.menuitemcheckbox:case u.a.menuitemradio:this.checked=!this.checked}this.$emit("change")}}}}Object(r.a)([Object(a.b)({mode:"boolean"})],A.prototype,"disabled",void 0),Object(r.a)([Object(a.b)({attribute:"expanded"})],A.prototype,"expanded",void 0),Object(r.a)([a.b],A.prototype,"role",void 0),Object(r.a)([a.b],A.prototype,"checked",void 0),Object(l.a)(A,c.a);const g=i(93).a`
    <template
        role="${t=>t.role}"
        aria-checked="${t=>t.role!==u.a.menuitem?t.checked:void 0}"
        aria-disabled="${t=>t.disabled}"
        aria-expanded="${t=>t.expanded}"
        @keydown="${(t,e)=>t.handleMenuItemKeyDown(e.event)}"
        @click="${(t,e)=>t.handleMenuItemClick(e.event)}"
        class="${t=>t.disabled?"disabled":""} ${t=>t.expanded?"expanded":""}"
    >
        ${c.c}
        <span class="content" part="content">
            <slot></slot>
        </span>
        ${c.b}
    </template>
`;var h=i(30),d=i(103),I=i(28),M=i(91),C=i(56),p=i(12),f=i(76),D=i(11);const y=h.b`
    ${Object(d.a)("grid")} :host {
        outline: none;
        box-sizing: border-box;
        height: calc(${f.a} * 1px);
        grid-template-columns: 42px auto 42px;
        grid-template-rows: auto;
        justify-items: center;
        align-items: center;
        padding: 0;
        margin: 0 calc(var(--design-unit) * 1px);
        white-space: nowrap;
        overflow: hidden;
        color: ${D.v.var};
        fill: ${D.v.var};
        cursor: pointer;
        font-family: var(--body-font);
        font-size: var(--type-ramp-base-font-size);
        line-height: var(--type-ramp-base-line-height);
        border-radius: calc(var(--corner-radius) * 1px);
        border: calc(var(--outline-width) * 1px) solid transparent;
    }

    :host(:${I.a}) {
        border: calc(var(--outline-width) * 1px) solid ${D.p.var};
        box-shadow: 0 0 0 calc((var(--focus-outline-width) - var(--outline-width)) * 1px) ${D.p.var};
    }

    :host(:hover) {
        background: ${D.n.var};
    }

    :host(:active) {
        background: ${D.m.var};
    }

    :host(.disabled) {
        cursor: ${M.a};
        opacity: var(--disabled-opacity);
    }

    :host(.disabled:hover) .start,
    :host(.disabled:hover) .end,
    :host(.disabled:hover)::slotted(svg) {
        fill: ${D.v.var};
    }

    .content {
        grid-column-start: 2;
        justify-self: start;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .start,
    .end,
    ::slotted(svg) {
        ${""} width: 16px;
        height: 16px;
    }

    :host(:hover) .start,
    :host(:hover) .end,
    :host(:hover)::slotted(svg),
    :host(:active) .start,
    :host(:active) .end,
    :host(:active)::slotted(svg) {
        fill: ${D.v.var};
    }
`.withBehaviors(D.m,D.n,D.o,D.p,D.v,Object(C.a)(h.b`
            :host {
                forced-color-adjust: none;
                border-color: transparent;
                color: ${p.a.ButtonText};
                fill: ${p.a.ButtonText};
            }
            :host(:hover) {
                background: ${p.a.Highlight};
                color: ${p.a.HighlightText};
            }
            :host(:hover) .start,
            :host(:hover) .end,
            :host(:hover)::slotted(svg),
            :host(:active) .start,
            :host(:active) .end,
            :host(:active)::slotted(svg) {
                fill: ${p.a.HighlightText};
            }
            :host(:${I.a}) {
                background: ${p.a.Highlight};
                border-color: ${p.a.ButtonText};
                box-shadow: 0 0 0 calc(var(--focus-outline-width) * 1px) inset ${p.a.HighlightText};
                color: ${p.a.HighlightText};
                fill: ${p.a.HighlightText};
            }
            :host(.disabled),
            :host(.disabled:hover),
            :host(.disabled:hover) .start,
            :host(.disabled:hover) .end,
            :host(.disabled:hover)::slotted(svg) {
                background: ${p.a.Canvas};
                color: ${p.a.GrayText};
                fill: ${p.a.GrayText};
                opacity: 1;
            }
        `));i.d(e,"a",(function(){return b}));let b=class extends A{};b=Object(n.a)([Object(o.b)({name:"fluent-menu-item",template:g,styles:y,shadowOptions:{mode:"closed"}})],b)},,,,,,,,,function(t,e,i){"use strict";var n;i.d(e,"a",(function(){return o})),i.d(e,"b",(function(){return r})),i.d(e,"c",(function(){return a})),i.d(e,"d",(function(){return s})),i.d(e,"e",(function(){return c})),i.d(e,"f",(function(){return l})),i.d(e,"g",(function(){return u})),i.d(e,"h",(function(){return A})),function(t){t[t.alt=18]="alt",t[t.arrowDown=40]="arrowDown",t[t.arrowLeft=37]="arrowLeft",t[t.arrowRight=39]="arrowRight",t[t.arrowUp=38]="arrowUp",t[t.back=8]="back",t[t.backSlash=220]="backSlash",t[t.break=19]="break",t[t.capsLock=20]="capsLock",t[t.closeBracket=221]="closeBracket",t[t.colon=186]="colon",t[t.colon2=59]="colon2",t[t.comma=188]="comma",t[t.ctrl=17]="ctrl",t[t.delete=46]="delete",t[t.end=35]="end",t[t.enter=13]="enter",t[t.equals=187]="equals",t[t.equals2=61]="equals2",t[t.equals3=107]="equals3",t[t.escape=27]="escape",t[t.forwardSlash=191]="forwardSlash",t[t.function1=112]="function1",t[t.function10=121]="function10",t[t.function11=122]="function11",t[t.function12=123]="function12",t[t.function2=113]="function2",t[t.function3=114]="function3",t[t.function4=115]="function4",t[t.function5=116]="function5",t[t.function6=117]="function6",t[t.function7=118]="function7",t[t.function8=119]="function8",t[t.function9=120]="function9",t[t.home=36]="home",t[t.insert=45]="insert",t[t.menu=93]="menu",t[t.minus=189]="minus",t[t.minus2=109]="minus2",t[t.numLock=144]="numLock",t[t.numPad0=96]="numPad0",t[t.numPad1=97]="numPad1",t[t.numPad2=98]="numPad2",t[t.numPad3=99]="numPad3",t[t.numPad4=100]="numPad4",t[t.numPad5=101]="numPad5",t[t.numPad6=102]="numPad6",t[t.numPad7=103]="numPad7",t[t.numPad8=104]="numPad8",t[t.numPad9=105]="numPad9",t[t.numPadDivide=111]="numPadDivide",t[t.numPadDot=110]="numPadDot",t[t.numPadMinus=109]="numPadMinus",t[t.numPadMultiply=106]="numPadMultiply",t[t.numPadPlus=107]="numPadPlus",t[t.openBracket=219]="openBracket",t[t.pageDown=34]="pageDown",t[t.pageUp=33]="pageUp",t[t.period=190]="period",t[t.print=44]="print",t[t.quote=222]="quote",t[t.scrollLock=145]="scrollLock",t[t.shift=16]="shift",t[t.space=32]="space",t[t.tab=9]="tab",t[t.tilde=192]="tilde",t[t.windowsLeft=91]="windowsLeft",t[t.windowsOpera=219]="windowsOpera",t[t.windowsRight=92]="windowsRight"}(n||(n={}));const o=40,r=37,a=39,s=38,c=35,l=13,u=36,A=32},function(t,e,i){"use strict";i.d(e,"a",(function(){return n})),i.d(e,"b",(function(){return o}));class n{constructor(t,e,i){this.name=t,this.value=e,this.host=i,this.propertyName="--"+t,this.var=`var(${this.propertyName})`}bind(t){const e=this.host(t);null!==e&&("function"==typeof e.registerCSSCustomProperty?e.registerCSSCustomProperty(this):(Array.isArray(e.disconnectedCSSCustomPropertyRegistry)||(e.disconnectedCSSCustomPropertyRegistry=[]),e.disconnectedCSSCustomPropertyRegistry.push(this)))}unbind(t){const e=this.host(t);null!==e&&"function"==typeof e.unregisterCSSCustomProperty&&e.unregisterCSSCustomProperty(this)}}function o(t,e,i){return new n(t,e,i)}},function(t,e,i){"use strict";i.d(e,"a",(function(){return a})),i.d(e,"b",(function(){return s}));var n=i(2),o=i(1),r=i(52);const a=Object(o.d)(Object(r.a)(n.O,14,0,n.J,n.H,n.I)),s=Object(o.i)(o.a.rest,a);Object(o.i)(o.a.hover,a),Object(o.i)(o.a.active,a),Object(o.i)(o.a.focus,a)},function(t,e,i){"use strict";var n=i(61);function o(t,e){if("function"!=typeof t||null!=e&&"function"!=typeof e)throw new TypeError("Expected a function");var i=function(){var n=arguments,o=e?e.apply(this,n):n[0],r=i.cache;if(r.has(o))return r.get(o);var a=t.apply(this,n);return i.cache=r.set(o,a)||r,a};return i.cache=new(o.Cache||n.a),i}o.Cache=n.a,e.a=o},function(t,e,i){"use strict";var n;i.d(e,"a",(function(){return n})),function(t){t.ltr="ltr",t.rtl="rtl"}(n||(n={}))},function(t,e,i){"use strict";i.d(e,"a",(function(){return r}));var n=i(9),o=i(21);class r{}Object(n.a)([Object(o.b)({attribute:"aria-atomic",mode:"fromView"})],r.prototype,"ariaAtomic",void 0),Object(n.a)([Object(o.b)({attribute:"aria-busy",mode:"fromView"})],r.prototype,"ariaBusy",void 0),Object(n.a)([Object(o.b)({attribute:"aria-controls",mode:"fromView"})],r.prototype,"ariaControls",void 0),Object(n.a)([Object(o.b)({attribute:"aria-current",mode:"fromView"})],r.prototype,"ariaCurrent",void 0),Object(n.a)([Object(o.b)({attribute:"aria-describedby",mode:"fromView"})],r.prototype,"ariaDescribedby",void 0),Object(n.a)([Object(o.b)({attribute:"aria-details",mode:"fromView"})],r.prototype,"ariaDetails",void 0),Object(n.a)([Object(o.b)({attribute:"aria-disabled",mode:"fromView"})],r.prototype,"ariaDisabled",void 0),Object(n.a)([Object(o.b)({attribute:"aria-errormessage",mode:"fromView"})],r.prototype,"ariaErrormessage",void 0),Object(n.a)([Object(o.b)({attribute:"aria-flowto",mode:"fromView"})],r.prototype,"ariaFlowto",void 0),Object(n.a)([Object(o.b)({attribute:"aria-haspopup",mode:"fromView"})],r.prototype,"ariaHaspopup",void 0),Object(n.a)([Object(o.b)({attribute:"aria-hidden",mode:"fromView"})],r.prototype,"ariaHidden",void 0),Object(n.a)([Object(o.b)({attribute:"aria-invalid",mode:"fromView"})],r.prototype,"ariaInvalid",void 0),Object(n.a)([Object(o.b)({attribute:"aria-keyshortcuts",mode:"fromView"})],r.prototype,"ariaKeyshortcuts",void 0),Object(n.a)([Object(o.b)({attribute:"aria-label",mode:"fromView"})],r.prototype,"ariaLabel",void 0),Object(n.a)([Object(o.b)({attribute:"aria-labelledby",mode:"fromView"})],r.prototype,"ariaLabelledby",void 0),Object(n.a)([Object(o.b)({attribute:"aria-live",mode:"fromView"})],r.prototype,"ariaLive",void 0),Object(n.a)([Object(o.b)({attribute:"aria-owns",mode:"fromView"})],r.prototype,"ariaOwns",void 0),Object(n.a)([Object(o.b)({attribute:"aria-relevant",mode:"fromView"})],r.prototype,"ariaRelevant",void 0),Object(n.a)([Object(o.b)({attribute:"aria-roledescription",mode:"fromView"})],r.prototype,"ariaRoledescription",void 0)},function(t,e,i){"use strict";i.d(e,"b",(function(){return A})),i.d(e,"a",(function(){return g})),i.d(e,"c",(function(){return h})),i.d(e,"d",(function(){return d})),i.d(e,"e",(function(){return I})),i.d(e,"f",(function(){return M}));var n=i(30),o=i(12),r=i(103),a=i(28),s=i(91),c=i(56),l=i(76),u=i(11);const A=n.b`
    ${Object(r.a)("inline-flex")} :host {
        font-family: var(--body-font);
        outline: none;
        font-size: var(--type-ramp-base-font-size);
        line-height: var(--type-ramp-base-line-height);
        height: calc(${l.a} * 1px);
        min-width: calc(${l.a} * 1px);
        background-color: ${u.l.var};
        color: ${u.v.var};
        border-radius: calc(var(--corner-radius) * 1px);
        fill: currentColor;
        cursor: pointer;
    }

    .control {
        background: transparent;
        height: inherit;
        flex-grow: 1;
        box-sizing: border-box;
        display: inline-flex;
        justify-content: center;
        align-items: center;
        padding: 0 calc((10 + (var(--design-unit) * 2 * var(--density))) * 1px);
        white-space: nowrap;
        outline: none;
        text-decoration: none;
        border: calc(var(--outline-width) * 1px) solid transparent;
        color: inherit;
        border-radius: inherit;
        fill: inherit;
        cursor: inherit;
        font-family: inherit;
    }

    .control, .end, .start {
        font: inherit;
    }

    .control.icon-only {
        padding: 0;
        line-height: 0;
    }

    :host(:hover) {
        background-color: ${u.k.var};
    }

    :host(:active) {
        background-color: ${u.i.var};
    }

    .control:${a.a} {
        border: calc(var(--outline-width) * 1px) solid ${u.p.var};
        box-shadow: 0 0 0 calc((var(--focus-outline-width) - var(--outline-width)) * 1px) ${u.p.var};
    }

    .control::-moz-focus-inner {
        border: 0;
    }

    :host(.disabled) {
        opacity: var(--disabled-opacity);
        background-color: ${u.l.var};
        cursor: ${s.a};
    }

    .start,
    .end,
    ::slotted(svg) {
        ${""} width: 16px;
        height: 16px;
    }

    .start {
        margin-inline-end: 11px;
    }

    .end {
        margin-inline-start: 11px;
    }
`.withBehaviors(u.l,u.v,u.k,u.i,Object(c.a)(n.b`
        :host {
          background-color: ${o.a.ButtonFace};
          border-color: ${o.a.ButtonText};
          color: ${o.a.ButtonText};
          fill: currentColor;
        }

        :host(:hover) {
          forced-color-adjust: none;
          background-color: ${o.a.Highlight};
          color: ${o.a.HighlightText};
        }

        .control:${a.a},
        :host(.outline) .control:${a.a} {
          forced-color-adjust: none;
          background-color: ${o.a.Highlight};
          border-color: ${o.a.ButtonText};
          box-shadow: 0 0 0 calc((var(--focus-outline-width) - var(--outline-width)) * 1px) ${o.a.ButtonText};
          color: ${o.a.HighlightText};
        }

        .control:hover,
        :host(.outline) .control:hover {
          border-color: ${o.a.ButtonText};
        }

        :host(.disabled),
        :host(.disabled) .control {
            forced-color-adjust: none;
            background-color: ${o.a.ButtonFace};
            border-color: ${o.a.GrayText};
            color: ${o.a.GrayText};
            cursor: ${s.a};
            opacity: 1;
        }

        :host([href]) {
          color: ${o.a.LinkText};
        }

        :host([href]) .control:hover,
        :host(.outline[href]) .control:hover
        :host(:hover[href]),
        :host([href]) .control:${a.a}{
          forced-color-adjust: none;
          background: ${o.a.ButtonFace};
          border-color: ${o.a.LinkText};
          box-shadow: 0 0 0 1px ${o.a.LinkText} inset;
          color: ${o.a.LinkText};
          fill: currentColor;
        }
    `)),g=n.b`
    :host(.accent) {
        background: ${u.c.var};
        color: ${u.e.var};
    }

    :host(.accent:hover) {
        background: ${u.b.var};
    }

    :host(.accent:active) .control:active {
        background: ${u.a.var};
    }

    :host(.accent) .control:${a.a} {
        box-shadow: 0 0 0 calc(var(--focus-outline-width) * 1px) inset ${u.q.var};
    }

    :host(.accent.disabled) {
        background: ${u.c.var};
    }
`.withBehaviors(u.c,u.e,u.b,u.a,u.q,Object(c.a)(n.b`
        :host(.accent) .control {
            forced-color-adjust: none;
            background: ${o.a.Highlight};
            color: ${o.a.HighlightText};
        }

        :host(.accent) .control:hover {
            background: ${o.a.HighlightText};
            border-color: ${o.a.Highlight};
            color: ${o.a.Highlight};
        }

        :host(.accent) .control:${a.a} {
            border-color: ${o.a.ButtonText};
            box-shadow: 0 0 0 2px ${o.a.HighlightText} inset;
        }

        :host(.accent.disabled) .control,
        :host(.accent.disabled) .control:hover {
            background: ${o.a.ButtonFace};
            border-color: ${o.a.GrayText};
            color: ${o.a.GrayText};
        }

        :host(.accent[href]) .control{
            background: ${o.a.LinkText};
            color: ${o.a.HighlightText};
        }

        :host(.accent[href]) .control:hover {
            background: ${o.a.ButtonFace};
            border-color: ${o.a.LinkText};
            box-shadow: none;
            color: ${o.a.LinkText};
            fill: currentColor;
        }

        :host(.accent[href]) .control:${a.a} {
          border-color: ${o.a.LinkText};
          box-shadow: 0 0 0 2px ${o.a.HighlightText} inset;
      }
    `)),h=n.b`
    :host(.hypertext) {
        height: auto;
        font-size: inherit;
        line-height: inherit;
        background: transparent;
    }

    :host(.hypertext) .control {
        display: inline;
        padding: 0;
        border: none;
        box-shadow: none;
        border-radius: 0;
        line-height: 1;
    }
    :host a.control:not(:link) {
        background-color: transparent;
        cursor: default;
    }
    :host(.hypertext) .control:link,
    :host(.hypertext) .control:visited {
        background: transparent;
        color: ${u.g.var};
        border-bottom: calc(var(--outline-width) * 1px) solid ${u.g.var};
    }
    :host(.hypertext) .control:hover {
        border-bottom-color: ${u.f.var};
    }
    :host(.hypertext) .control:active {
        border-bottom-color: ${u.d.var};
    }
    :host(.hypertext) .control:${a.a} {
        border-bottom: calc(var(--focus-outline-width) * 1px) solid ${u.p.var};
    }
`.withBehaviors(u.g,u.f,u.d,u.p,Object(c.a)(n.b`
      :host(.hypertext) .control:${a.a} {
        color: ${o.a.LinkText};
        border-bottom-color: ${o.a.LinkText};
      }
    `)),d=n.b`
    :host(.lightweight) {
        background: transparent;
        color: ${u.g.var};
    }

    :host(.lightweight) .control {
        padding: 0;
        height: initial;
        border: none;
        box-shadow: none;
        border-radius: 0;
    }

    :host(.lightweight:hover) {
        color: ${u.f.var};
    }

    :host(.lightweight:active) {
        color: ${u.d.var};
    }

    :host(.lightweight) .content {
        position: relative;
    }

    :host(.lightweight) .content::before {
        content: "";
        display: block;
        height: calc(var(--outline-width) * 1px);
        position: absolute;
        top: calc(1em + 3px);
        width: 100%;
    }

    :host(.lightweight:hover) .content::before {
        background: ${u.f.var};
    }

    :host(.lightweight:active) .content::before {
        background: ${u.d.var};
    }

    :host(.lightweight) .control:${a.a} .content::before {
        background: ${u.v.var};
        height: calc(var(--focus-outline-width) * 1px);
    }

    :host(.lightweight.disabled) .content::before {
        background: transparent;
    }
`.withBehaviors(u.g,u.f,u.d,u.f,u.v,Object(c.a)(n.b`
        :host(.lightweight) .control:hover,
        :host(.lightweight) .control:${a.a} {
            forced-color-adjust: none;
            background: ${o.a.ButtonFace};
            color: ${o.a.Highlight};
        }
        :host(.lightweight) .control:hover .content::before,
        :host(.lightweight) .control:${a.a} .content::before {
            background: ${o.a.Highlight};
        }

        :host(.lightweight.disabled) .control {
            forced-color-adjust: none;
            color: ${o.a.GrayText};
        }

        :host(.lightweight.disabled) .control:hover .content::before {
            background: none;
        }

        :host(.lightweight[href]) .control:hover,
        :host(.lightweight[href]) .control:${a.a} {
            background: ${o.a.ButtonFace};
            box-shadow: none;
            color: ${o.a.LinkText};
        }

        :host(.lightweight[href]) .control:hover .content::before,
        :host(.lightweight[href]) .control:${a.a} .content::before {
            background: ${o.a.LinkText};
        }
    `)),I=n.b`
    :host(.outline) {
        background: transparent;
        border-color: ${u.A.var};
    }

    :host(.outline:hover) {
        border-color: ${u.z.var};
    }

    :host(.outline:active) {
        border-color: ${u.y.var};
    }

    :host(.outline) .control {
        border-color: inherit;
    }

    :host(.outline) .control:${a.a} {
        border: calc(var(--outline-width) * 1px) solid ${u.p.var});
        box-shadow: 0 0 0 calc((var(--focus-outline-width) - var(--outline-width)) * 1px) ${u.p.var};
    }

    :host(.outline.disabled) {
        border-color: ${u.A.var};
    }
`.withBehaviors(u.A,u.z,u.y,u.p,Object(c.a)(n.b`
      :host(.outline.disabled) .control {
        border-color: ${o.a.GrayText};
      }
    `)),M=n.b`
  :host(.stealth) {
    background: ${u.o.var};
  }

  :host(.stealth:hover) {
    background: ${u.n.var};
  }

  :host(.stealth:active) {
    background: ${u.m.var};
  }

  :host(.stealth.disabled) {
    background: ${u.o.var};
  }
`.withBehaviors(u.o,u.n,u.m,Object(c.a)(n.b`
        :host(.stealth) .control {
            forced-color-adjust: none;
            background-color: none;
            border-color: transparent;
            color: ${o.a.ButtonText};
            fill: currentColor;
        }

        :host(.stealth:hover) .control {
            background-color: ${o.a.Highlight};
            border-color: ${o.a.Highlight};
            color: ${o.a.HighlightText};
            fill: currentColor;
        }

        :host(.stealth:${a.a}) .control {
            box-shadow: 0 0 0 1px ${o.a.Highlight};
            color: ${o.a.HighlightText};
            fill: currentColor;
        }

        :host(.stealth.disabled) {
          background-color: ${o.a.ButtonFace};
        }

        :host(.stealth.disabled) .control {
            background-color: ${o.a.ButtonFace};
            border-color: transparent;
            color: ${o.a.GrayText};
        }

        :host(.stealth[href]) .control {
            color: ${o.a.LinkText};
        }

        :host(.stealth:hover[href]) .control {
            background-color: ${o.a.LinkText};
            border-color: ${o.a.LinkText};
            color: ${o.a.HighlightText};
            fill: currentColor;
        }

      :host(.stealth:${a.a}[href]) .control {
          box-shadow: 0 0 0 1px ${o.a.LinkText};
          color: ${o.a.LinkText};
          fill: currentColor;
      }
    `))},function(t,e,i){"use strict";function n(t,e){const i="function"==typeof e?e:()=>e;return(e,n)=>t(e,n)?i(e,n):null}i.d(e,"a",(function(){return n}))},function(t,e,i){"use strict";i.d(e,"a",(function(){return n}));const n="box-shadow: 0 0 calc((var(--elevation) * 0.225px) + 2px) rgba(0, 0, 0, calc(.11 * (2 - var(--background-luminance, 1)))), 0 calc(var(--elevation) * 0.4px) calc((var(--elevation) * 0.9px)) rgba(0, 0, 0, calc(.13 * (2 - var(--background-luminance, 1))));"},function(t,e,i){"use strict";var n=Math.max,o=Math.min;var r=function(t,e,i){return t>=o(e,i)&&t<n(e,i)},a=i(26),s=i(78),c=/^\s+|\s+$/g,l=/^[-+]0x[0-9a-f]+$/i,u=/^0b[01]+$/i,A=/^0o[0-7]+$/i,g=parseInt;var h=function(t){if("number"==typeof t)return t;if(Object(s.a)(t))return NaN;if(Object(a.a)(t)){var e="function"==typeof t.valueOf?t.valueOf():t;t=Object(a.a)(e)?e+"":e}if("string"!=typeof t)return 0===t?t:+t;t=t.replace(c,"");var i=u.test(t);return i||A.test(t)?g(t.slice(2),i?2:8):l.test(t)?NaN:+t};var d=function(t){return t?(t=h(t))===1/0||t===-1/0?17976931348623157e292*(t<0?-1:1):t==t?t:0:0===t?t:0};e.a=function(t,e,i){return e=d(e),void 0===i?(i=e,e=0):i=d(i),t=h(t),r(t,e,i)}},function(t,e,i){"use strict";var n=i(39),o=i(24),r=i(87);i.d(e,"a",(function(){return s}));class a extends class{constructor(t,e){this.target=t,this.options=e,this.source=null}bind(t){const e=this.options.property;this.shouldUpdate=o.a.getAccessors(t).some(t=>t.name===e),this.source=t,this.updateTarget(this.computeNodes()),this.shouldUpdate&&this.observe()}unbind(){this.updateTarget(r.a),this.source=null,this.shouldUpdate&&this.disconnect()}handleEvent(){this.updateTarget(this.computeNodes())}computeNodes(){let t=this.getNodes();return void 0!==this.options.filter&&(t=t.filter(this.options.filter)),t}updateTarget(t){this.source[this.options.property]=t}}{constructor(t,e){super(t,e)}observe(){this.target.addEventListener("slotchange",this)}disconnect(){this.target.removeEventListener("slotchange",this)}getNodes(){return this.target.assignedNodes(this.options)}}function s(t){return"string"==typeof t&&(t={property:t}),new n.a("fast-slotted",a,t)}},,,,,function(t,e,i){"use strict";i.d(e,"a",(function(){return c}));var n=i(9),o=i(110),r=i(21),a=i(34),s=i(45);class c extends o.a{constructor(){super(...arguments),this.headinglevel=2,this.expanded=!1,this.clickHandler=t=>{this.expanded=!this.expanded,this.change()},this.change=()=>{this.$emit("change")}}}Object(n.a)([Object(r.b)({attribute:"heading-level",mode:"fromView",converter:r.c})],c.prototype,"headinglevel",void 0),Object(n.a)([Object(r.b)({mode:"boolean"})],c.prototype,"expanded",void 0),Object(n.a)([r.b],c.prototype,"id",void 0),Object(s.a)(c,a.a)},,,,,,,,,,function(t,e,i){"use strict";i.r(e);var n,o=i(104),r=i(108),a=i(119),s=i(31),c=i(4),l=i(3),u=i(50),A=i(90),g=i(74),h=i(25),d=i(47);r.a,a.a,o.a,document.addEventListener("DOMContentLoaded",(function(){n=mojom&&mojom.ShoppingHandler.getRemote(),loadTimeData.getValue("dark_theme")&&(l.k.currentTheme=l.j.Dark);loadTimeData.getValue("is_rtl")&&(l.k.currentDirection=l.h.RTL);var t=!!loadTimeData.valueExists("isAutoShowAllowed")&&loadTimeData.getValue("isAutoShowAllowed"),e=loadTimeData.getValue("open_action"),i=loadTimeData.valueExists("enabled_features")?loadTimeData.getValue("enabled_features"):void 0,o=[];i&&i.length>0&&(o=u.a.Create(i),n.triggerSupportedExperiments(o));h.a.GetLogModule().setData(d.a.Client,c.a.Edge);var r=(new l.k).createElement("msft-edge-shopping");l.k.currentDevice=l.b.Desktop,l.k.localizedStrings=A.a.Create();var a=new l.k;a.addCommonStyles(r),a.createPolicyForStaticResources(),r.fixedTitle=l.k.localizedStrings.commonFixedTitle,(new l.k).addHeaderIcon(r);var I=!1;o.forEach((function(t){t.name===u.a.ExperimentNames.showShoppingEdgeLogo&&(I=!0)})),n&&n.triggerExperiment(u.a.ExperimentNames.showShoppingEdgeLogo);I&&a.addHeaderIcon(r);var M=document.querySelector("#divContentWrapper");M&&(a.createContextMenu(M,r,t,n),a.createCloseButton(r,n));(function(){var t=document.querySelector("head");if(t){var e=(new l.k).createElement("title");e.innerText=l.k.localizedStrings.commonFixedTitle,t.appendChild(e)}})(),function(t,e){var i=document.querySelector("#divContentWrapper"),o=(new l.k).createElement("DIV",{id:"freCard",class:"divPages"});i&&((new g.a).renderFREModule(t),(new g.a).createStartShoppingButton(n,o,e),(new g.a).createDismissAnchor(n,o),t.appendChild(o),i.appendChild(t))}(r,e),function(t){var e=document.createElement("fast-accordion");e.slot="footer",e.className+="txtSmall",l.k.currentTheme===l.j.Dark&&(e.style.color="#FFFFFF");var i=document.createElement("fluent-anchor");i.id="anchorSettings",i.setAttribute("href","#"),i.className+=" txtSmall",i.textContent=l.k.localizedStrings.freSettings,i.setAttribute("appearance","hypertext");var o=new l.k,r=o.createTextElement(o.getValueFromKey(l.d.FREFooter,""),l.i.Small,l.a.Title);l.k.currentTheme===l.j.Dark&&(r.style.color="#FFFFFF");r.appendChild(i),e.appendChild(r),t.appendChild(e);var a=t.querySelector("#anchorSettings");a&&a.addEventListener("click",(function(){var t=new s.a(c.d.FREManage);h.a.GetLogModule().logClientEvent(c.h.Information,c.j.ButtonClick,"FRE Footer Manage",t,n,l.g.FRE_SETTINGS),n.navigateToSettings()}))}(r),a.initializeTrapFocus(r)}))}]);