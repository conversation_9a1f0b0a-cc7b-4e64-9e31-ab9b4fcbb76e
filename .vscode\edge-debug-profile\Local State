{"abusive_adblocker_etag": "\"5E25271B8190D943537AD3FDB50874FC133E8B4A00380E2A6A888D63386F728B\"", "accessibility": {"captions": {"common_models_path": "", "soda_binary_path": ""}}, "app_defaults": {"os_activation_state": true}, "apps_count_check_time": "13396563767792291", "autofill": {"ablation_seed": "qafoRwHWeRE="}, "breadcrumbs": {"enabled": false, "enabled_time": "13394140271313865"}, "cloud_config_service_v2": {"config_observers_json_md5": "+HmVPbOkKWVe/czHWtoB4w==", "config_observers_json_semantic_version": "25.4.14.1", "config_observers_json_semantic_version_last_r_date": "13394541616778254", "last_update_checking_time": "13396563796607373", "observers": {"CloudConfigServiceV1MobileConfigObserver": {"md5": "BXpFGzjRKBQjesTgtswcrw==", "url": "https://edge-mobile-static.azureedge.net/eccp/get?settenant=edge-config&setplatform=win&setmkt=fr&setchannel=stable"}, "operation_config": {"md5": "xsYKv3QVNCVN/9emtaICNA==", "url": "https://edge-cloud-resource-static.azureedge.net/default/operation_config/default.json"}}}, "default_browser": {"browser_name_enum": 13}, "desktop_session_duration_tracker": {"last_session_end_timestamp": "1752090243"}, "dual_engine": {"consumer_mode": {"ie_usage_checked": "13396563797712220", "ie_usage_times": []}}, "edge": {"app_user_model_id": "MSEdge", "manageability": {"edge_last_active_report_time": "13396563737701960", "edge_last_active_time": "13396563840007312"}, "perf_center": {"efficiency_mode_v2_is_active": true, "perf_game_mode": true, "performance_mode": 3, "performance_mode_is_on": false}, "tab_stabs": {"closed_without_unfreeze_never_unfrozen": 0, "closed_without_unfreeze_previously_unfrozen": 0, "discard_without_unfreeze_never_unfrozen": 0, "discard_without_unfreeze_previously_unfrozen": 0}, "tab_stats": {"frozen_daily": 0, "unfrozen_daily": 0}}, "edge_ci": {"num_healthy_browsers_since_failure": 2}, "edge_llm": {"on_device": {"gpu_info": "4318:7446:D3D12 driver version 30.0.15.1169", "shader_fp16_supported": 1}}, "edge_operation_config": {"_meta": {"version": "1.1.30"}, "edge_ai_assistant": {"ntp_config": {"1": {"js": "https://edge-cloud-resource-static.azureedge.net/consumer/ai-assistant/alpha/v1/static/js/ntp.8d73e3f1.js", "server_host": "https://authint.bcentral.cn"}}, "pdf_config": {"1": {"js": "https://edge-cloud-resource-static.azureedge.net/consumer/ai-assistant/alpha/v1/static/js/pdf.js"}}, "real_name_auth_config": {"1": {"js": "https://edge-cloud-resource-static.azureedge.net/consumer/ai-assistant/alpha/v1/static/js/realNameAuth.9b5fc58a.js"}}, "side_pane_config": {"1": {"js": "https://edge-cloud-resource-static.azureedge.net/consumer/ai-assistant/alpha/v1/static/js/sidepane.d5bcd879.js"}}, "video_page_config": {"www.bilibili.com": {"1": {"js": "https://edge-cloud-resource-static.azureedge.net/consumer/ai-assistant/alpha/v1/static/js/bilibili.js"}}, "www.youtube.com": {"1": {"js": "https://edge-cloud-resource-static.azureedge.net/consumer/ai-assistant/alpha/v1/static/js/youtube.js"}}}}, "edge_drop": {"manifest_canary": {"css": "https://edge-consumer-static.azureedge.net/edropstatic/2025/02/25/1/static/css/main.2a8c2845.css", "js": "https://edge-consumer-static.azureedge.net/edropstatic/2025/02/25/1/static/js/main.915ce316.js", "version": 138}, "manifest_stable": {"css": "https://edge-consumer-static.azureedge.net/edropstatic/2025/02/25/1/static/css/main.2a8c2845.css", "js": "https://edge-consumer-static.azureedge.net/edropstatic/2025/02/25/1/static/js/main.915ce316.js", "version": 138}}, "edge_mouse_gesture": {"blocklist": ["https://earth.google.com", "https://www.figma.com", "https://map.baidu.com", "https://maps.gaode.com", "https://app.diagrams.net"]}, "edge_screenshot": {"dynamic_config": {"resources": {"2": {"untrusted_js": "https://edge-consumer-static.azureedge.net/screenshot/2025/index.6bfeeb46d8664a1f.js"}, "3": {"untrusted_js": "https://edge-consumer-static.azureedge.net/screenshot/2025/index.8147d939d9ed09c5.js"}}}}}, "edgel_llm": {"on_device": {"performance_info_version": "138.0.3351.65"}}, "fre": {"has_first_visible_browser_session_completed": true, "oem_bookmarks_set": true}, "gaming_rewards_hva_pitch_notification": {"backfilled": true}, "hardware_acceleration_mode_previous": true, "host_package_checked_on_browser_version": "138.0.7204.97", "identity_combined_status": {"aad": 2, "ad": 1}, "ie_react": {"has_done_one_time_ie_import": true}, "last_pin_migration_on_edge_version": "138.0.3351.65", "last_pin_migration_on_os_version": "11 Version 24H2 (Build 26100.4484)", "legacy": {"profile": {"name": {"migrated": true}}}, "local": {"password_hash_data_list": []}, "metrics": {"client_id_hash_key_created": true}, "migration": {"Default": {"migration_attempt": 0, "migration_version": 5}}, "network_time": {"network_time_mapping": {"local": **********882.201, "network": 1752090138412.0, "ticks": ************.0, "uncertainty": 10234652.0}}, "optimization_guide": {"model_execution": {"last_usage_by_feature": {}}, "model_store_metadata": {}, "on_device": {"last_version": "138.0.3351.65", "model_crash_count": 0, "performance_class": 2, "performance_class_version": "138.0.3351.65"}}, "os_crypt": {"audit_enabled": true, "encrypted_key": "RFBBUEkBAAAA0Iyd3wEV0RGMegDAT8KX6wEAAABS5g694VdfQaFCRBD6+/U+EAAAAB4AAABNAGkAYwByAG8AcwBvAGYAdAAgAEUAZABnAGUAAAAQZgAAAAEAACAAAAAYoLBLMuFrfCop62EQMzOif7fqZPvgHHlBHilbbXSD1wAAAAAOgAAAAAIAACAAAABVQEmpQN31pWxvDET14WmC7CM49bFvb84n8zCQFBg/bjAAAADNMR+owjH4DTsIqeFezpB5N5q5fiMwHKwfEIzP6SyI6/5UTgHPLBJNug6ya6KelPFAAAAAgkvSYr5XsIqA/rz0Nm/NBRP74GYmrXShezM2Jbr2npLdAFNueFflmfgeB60idNXy46z8Wv37bPmvG4sO/1HVqg=="}, "performance_intervention": {"last_daily_sample": "*****************"}, "policy": {"last_statistics_update": "*****************"}, "privacy_budget": {"meta_experiment_activation_salt": 0.****************}, "profile": {"info_cache": {"Default": {"active_time": **********.40574, "avatar_icon": "chrome://theme/IDR_PROFILE_AVATAR_20", "background_apps": false, "edge_account_cid": "691578f9f429c5a9", "edge_account_environment": 0, "edge_account_environment_string": "login.microsoftonline.com", "edge_account_first_name": "mrad", "edge_account_last_name": "eya", "edge_account_oid": "", "edge_account_sovereignty": 0, "edge_account_tenant_id": "9188040d-6c67-4c5b-b112-36a304b66dad", "edge_account_type": 1, "edge_force_signout_restore_info": {}, "edge_force_signout_state": 0, "edge_image_hash": **********, "edge_muid": "1EE3E419EDE0628B100DF211EC9763C0", "edge_previously_signin_user_name": "", "edge_profile_can_be_deleted": true, "edge_signed_in_default_name": ********, "edge_test_on_premises": false, "edge_wam_aad_for_app_account_type": 0, "edge_was_previously_signin": false, "enterprise_label": "", "force_signin_profile_locked": false, "gaia_id": "000300004CA23377", "gaia_name": "mrad eya", "gaia_picture_file_name": "Edge Profile Picture.png", "is_consented_primary_account": true, "is_ephemeral": false, "is_using_default_avatar": false, "is_using_default_name": true, "last_downloaded_gaia_picture_url_with_size": "**********", "managed_user_id": "", "metrics_bucket_index": 1, "name": "Profil 1", "sign_in_source": 1, "signin.with_credential_provider": false, "use_gaia_picture": true, "user_name": "<EMAIL>"}}, "last_active_profiles": ["<PERSON><PERSON><PERSON>"], "metrics": {"next_bucket_index": 2}, "profile_counts_reported": "*****************", "profiles_order": ["<PERSON><PERSON><PERSON>"]}, "profile_network_context_service": {"http_cache_finch_experiment_groups": "None None None None"}, "profiles": {"collect_potential_implicit_signin_data_started": true, "current_profile_name": "", "edge": {"guided_switch_pref": [], "implicit_signin": {"telemetry_result": 1}, "multiple_profiles_with_same_account": false}, "edge_implicitly_signed_in": [{"edge_account_type": 1, "id": "000300004CA23377"}], "edge_sso_info": {"aad_sso_algo_state": 1, "first_profile_key": "<PERSON><PERSON><PERSON>", "msa_first_profile_key": "<PERSON><PERSON><PERSON>", "msa_sso_algo_state": 2, "msa_sso_state_reached_by": 3}, "signin_last_seen_version": "138.0.3351.65", "signin_last_updated_time": **********.311968}, "segmentation_platform": {"segment_execution_result_local_state": {"edge_user_topic_on_url_protobuf": {"execution_time": "*****************", "is_ready": true, "output": [1.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 350.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "segment_id": 522}}}, "sentinel_creation_time": "0", "session_id_generator_last_value": "**********", "signin": {"active_accounts": {"0tCkmhn++KlCgjxmGokGBvaCtLEeq6+v1U7FwEaVUzI=": "*****************"}, "active_accounts_last_emitted": "*****************"}, "smart_switch": {"curated_site_list_file_path": "C:\\Users\\<USER>\\Downloads\\tunisie-telecom-scores-hub-main\\.vscode\\edge-debug-profile\\Edge Data Protection Lists\\*******\\smart_switch_list.json"}, "smartscreen": {"enabled": true, "pua_protection_enabled": true}, "startup_boost": {"last_browser_open_time": "*****************"}, "subresource_filter": {"ruleset_version": {"checksum": *********, "content": "**********", "format": 36}}, "tab_stats": {"discards_external": 0, "discards_proactive": 0, "discards_urgent": 0, "last_daily_sample": "*****************", "max_tabs_per_window": 1, "reloads_external": 0, "reloads_urgent": 0, "total_tab_count_max": 1, "window_count_max": 1}, "telemetry_client": {"cloned_install": {"user_data_dir_id": ********}, "governance": {"last_dma_change_date": "*****************", "last_known_cps": 2291}, "host_telclient_path": "QzpcUHJvZ3JhbSBGaWxlcyAoeDg2KVxNaWNyb3NvZnRcRWRnZVxBcHBsaWNhdGlvblwxMzguMC4zMzUxLjY1XHRlbGNsaWVudC5kbGw=", "install_source_name": "windows", "os_integration_level": 5, "sample_id": 75441208, "updater_version": "**********", "windows_update_applied": false}, "ukm": {"persisted_logs": []}, "uninstall_metrics": {"installation_date2": "1749666671"}, "updateclientdata": {"apps": {"ahmaebgpfccdhgidjaidaoojjcijckba": {"cohort": "", "cohortname": "", "fp": "1.4B81B4DF3AD971287E1AE02C449344FCFA5D20431DE17B2BA8854CC9CDCE4089", "installdate": -1, "max_pv": "0.0.0.0", "pv": "*******"}, "alpjnmnfbgfkmmpcfpejmmoebdndedno": {"cohort": "", "cohortname": "", "fp": "1.56E96B6819FAF673A2CC4750059D5D42BB2E89FEFC7C4D81896F64B01B35E789", "installdate": -1, "max_pv": "0.0.0.0", "pv": "********"}, "cllppcmmlnkggcmljjfigkcigaajjmid": {"cohort": "", "cohortname": "", "fp": "1.C2151A2BFFFFD177E60F73C4D9BEFE3FF3709EBCA0337FA0DD3B2E5479347C68", "installdate": -1, "max_pv": "0.0.0.0", "pv": "128.18355.18353.1"}, "eeobbhfgfagbclfofmgbdfoicabjdbkn": {"cohort": "", "cohortname": "", "fp": "1.A99D66CFCE8CA170740CE0403956F4DFAF4683829A89F4B7AD9C95303871E284", "installdate": -1, "max_pv": "0.0.0.0", "pv": "1.0.0.9"}, "fgbafbciocncjfbbonhocjaohoknlaco": {"cohort": "", "cohortname": "", "fp": "1.6B1561D18D6D7D238C1FA4FB8AEF54E1F1E6B574D958BC0A41CA955F90AFD7DC", "installdate": -1, "max_pv": "0.0.0.0", "pv": "2025.5.15.1"}, "fppmbhmldokgmleojlplaaodlkibgikh": {"cohort": "", "cohortname": "", "fp": "1.A81D1959892AE4180554347DF1B97834ABBA2E1A5E6B9AEBA000ECEA26EABECC", "installdate": -1, "max_pv": "0.0.0.0", "pv": "********"}, "gllimckfbolmioaaihpppacjccghejen": {"cohort": "", "cohortname": "", "fp": "1.8D64E3A35EE2C3E0B9E33AFD63069FDC917A5647DD1E20C5EAD97955FB6979F9", "installdate": -1, "max_pv": "0.0.0.0", "pv": "*******"}, "hajigopbbjhghbfimgkfmpenfkclmohk": {"cohort": "", "cohortname": "", "installdate": -1}, "hjaimielcgmceiphgjjfddlgjklfpdei": {"cohort": "", "cohortname": "", "fp": "1.A00289AF85D31D698A0F6753B6CE67DBAB4BDFF639BDE5FC588A5D5D8A3885D5", "installdate": -1, "max_pv": "0.0.0.0", "pv": "*******"}, "jbfaflocpnkhbgcijpkiafdpbjkedane": {"cohort": "", "cohortname": "", "installdate": -1}, "jcmcegpcehdchljeldgmmfbgcpnmgedo": {"cohort": "", "cohortname": "", "fp": "1.1E3BA1FDE3B5B7E66AA76DF46F72F0F1A0848448D3107066FDE09CCDB46EA1FA", "installdate": -1, "max_pv": "2025.6.9.1", "pv": "2025.7.2.1"}, "kmkacjgmmfchkbeglfbjjeidfckbnkca": {"cohort": "", "cohortname": "", "fp": "1.4A84F2BDD63DABE6ABDE22B9047A6942EEB7BDF93D8435CC4B188DBE72D9E30D", "installdate": -1, "max_pv": "0.0.0.0", "pv": "*******"}, "kpfehajjjbbcifeehjgfgnabifknmdad": {"cohort": "", "cohortname": "", "fp": "1.00AF3F07B5ABB71F6D30337E1EEF62FA280F06EF19485C0CF6B72171F92CCC0A", "installdate": -1, "max_pv": "0.0.0.0", "pv": "120.0.6050.0"}, "ldfkbgjbencjpgjfleiooeldhjdapggh": {"cohort": "", "cohortname": "", "installdate": -1}, "lfmeghnikdkbonehgjihjebgioakijgn": {"cohort": "", "cohortname": "", "fp": "1.13FEA54E1183CD0CE9222697B6CD786B80AD0809B618234EE2DC4E806344C52F", "installdate": -1, "max_pv": "2.0.0.23", "pv": "2.0.0.27"}, "lkkdlcloifjinapabfonaibjijloebfb": {"cohort": "", "cohortname": "", "fp": "1.18019BEB1D2B6F91D1849CE2ABC6B9BD83FFAB505BB252125F79A4ECEDFAC75A", "installdate": -1, "max_pv": "4.0.3.10", "pv": "4.0.3.11"}, "llmidpclgepbgbgoecnhcmgfhmfplfao": {"cohort": "", "cohortname": "", "fp": "1.A77B2D57885267CC15B8C6FB25100E0C9C6DA529A0A0ABE0D78D3D40B1C3156F", "installdate": -1, "max_pv": "0.0.0.0", "pv": "2.1.58.0"}, "mcfjlbnicoclaecapilmleaelokfnijm": {"cohort": "", "cohortname": "", "installdate": -1}, "mkcgfaeepibomfapiapjaceihcojnphg": {"cohort": "rrf@1.00", "cohortname": "", "installdate": -1}, "mpicjakjneaggahlnmbojhjpnileolnb": {"cohort": "", "cohortname": "", "fp": "1.E6BCF09B2C927A70A00713A6DC54CAA86F9B1B4A3FB214E34F28EEF63225EFC1", "installdate": -1, "max_pv": "********", "pv": "********"}, "ndikpojcjlepofdkaaldkinkjbeeebkl": {"cohort": "", "cohortname": "", "fp": "1.5110A67904F33F66A66CC9BE4DD3DA419A596DE32DAAE8F62BAA998D3BE5CA83", "installdate": -1, "max_pv": "0.0.0.0", "pv": "**********"}, "oankkpibpaokgecfckkdkgaoafllipag": {"cohort": "", "cohortname": "", "fp": "1.1AB07E887ACCA305058EEAB9053C96DC531C2C5C067AB4F30AFA2B31F1EDD966", "installdate": -1, "max_pv": "0.0.0.0", "pv": "6498.2024.12.2"}, "ohckeflnhegojcjlcpbfpciadgikcohk": {"cohort": "", "cohortname": "", "fp": "1.95FD9D48E4FC245A3F3A99A3A16ECD1355050BA3F4AFC555F19A97C7F9B49677", "installdate": -1, "max_pv": "0.0.0.0", "pv": "*******"}, "ojblfafjmiikbkepnnolpgbbhejhlcim": {"cohort": "", "cohortname": "", "installdate": -1}, "omnckhpgfmaoelhddliebabpgblmmnjp": {"cohort": "", "cohortname": "", "fp": "1.DD91C7C496E4D9E8DF5BEAA3D33D45F9EF196B4F888D0FAC50EAF08CAD6B29D7", "installdate": -1, "max_pv": "0.0.0.0", "pv": "*******"}, "pbdgbpmpeenomngainidcjmopnklimmf": {"cohort": "rrf@0.50", "cohortname": "", "fp": "1.B27BEC7581505715364F132DE1998818C82462DBF55A1F55F9B15E29E988D791", "installdate": -1, "max_pv": "0.0.0.0", "pv": "********"}, "pdfjdcjjjegpclfiilihfkmdfndkneei": {"cohort": "", "cohortname": "", "fp": "1.ACBE8C30301F1FAFB154AD7AC8D6EFB5C53D208310D170EDDC3B7B7C681BFE35", "installdate": -1, "max_pv": "2025.4.2.0", "pv": "2025.5.13.0"}, "pghocgajpebopihickglahgebcmkcekh": {"cohort": "", "cohortname": "", "fp": "1.455D1A3B5F7FA199F27CB165FA6F54DF7972FBB6C62D422A14C2415791175931", "installdate": -1, "max_pv": "0.0.0.0", "pv": "********"}, "plbmmhnabegcabfbcejohgjpkamkddhn": {"cohort": "", "cohortname": "", "fp": "1.1E1174204F8A0A13DE2E224A1BE882D2724A6FD13BA18A895FD5098FD5552460", "installdate": -1, "max_pv": "0.0.0.0", "pv": "3057.0.0.0"}, "pmagihnlncbcefglppponlgakiphldeh": {"cohort": "", "cohortname": "", "installdate": -1}}}, "updateclientlastupdatecheckerror": 0, "updateclientlastupdatecheckerrorcategory": 0, "updateclientlastupdatecheckerrorextracode1": 0, "user_experience_metrics": {"client_id2": "e7d2012d-9934-4a84-87f5-2ab2128f4be1", "diagnostics": {"edge_odd_consent_last_modified_date": "0", "last_data_collection_level_on_launch": 1}, "last_seen": {"BrowserMetrics": "13394546128998439", "CrashpadMetrics": "0"}, "lbfg_date": "**********", "limited_entropy_randomization_source": "CFD503C67120683FED0242D4DDF4FFB7", "log_finalized_record_id": 9, "log_record_id": 19, "low_entropy_source3": 2156, "machine_id": 14830129, "payload_counter": 1, "pseudo_low_entropy_source": 3814, "reporting_URL_enabled": false, "reporting_enabled": false, "session_id": 9, "stability": {"browser_last_live_timestamp": "13396563735881825", "exited_cleanly": true, "saved_system_profile": "CKKqjcMGEhAxMzguMC4zMzUxLjY1LTY0GAAiAmZyKhgKCldpbmRvd3MgTlQSCjEwLjAuMjYxMDAyhgEKBng4Nl82NBDOvQEYgIC8+K3/HyIhVml2b0Jvb2tfQVNVU0xhcHRvcCBYNTE1RVBfWDUxNUVQKAEwgAw44AZCCggAEAAaADIAOgBNd8QNQ1XuZg1DZQAAoD9qGAoMR2VudWluZUludGVsEMGNIBgIIAEoAIIBAIoBAKoBBng4Nl82NLABAUoKDRPkADEVXm/S2EoKDVkiPa4VXm/S2EoKDRcHFKkVXm/S2EoKDZVuF7QVXm/S2EoKDRPam1wVXm/S2EoKDZDpqz8VXm/S2FAEWgIIAGIESU5CWGoICAAQADgAQACAAaCIp8IGmAEA+AHsEIAC////////////AYgCAagC5h2yAkyv0h+xHk8DszfirX7FiAAqfsiwbAuPvS/YwC2x7motfyp+LMVMtFQTHgxGjXInoKznbDVi3++sZ9bGCqYb9QXxOQvdolOfvs9M8w1d8QKgLVfCxjwHu8o+5QIKBAgAEAASCCIECAIQAigBIgIIAjIGCAAiACoAOgsI8///7/f/////AUIpEgoxLjMuMTk1LjYxGAUgACoOUmVnS2V5Tm90Rm91bmQyB3dpbmRvd3NSAggAWvwBCcl2vp8ajzhAEekmMQisfE1AGQAAAAAAAFlAGQAAAAAAAFlAGQAAAAAAAFlAGQAAAAAAAPA/GQAAAAAAAPA/GQAAAAAAAAAAGQAAAAAAAPA/GQAAAAAAAFlAGQAAAAAAAFlAGQAAAAAAAFlAGQAAAAAAAFlAGQAAAAAAAFlAGQAAAAAAAFlAGQAAAAAAAFlAGQAAAAAAAFlAGQAAAAAAAFlAGQAAAAAAAAAAGQAAAAAAAFlAGQAAAAAAAPA/GQAAAAAAAFlAGQAAAAAAAPA/GQAAAAAAAPA/GQAAAAAAADRAGQAAAAAAAPA/GQAAAAAAAFlAGQAAAAAAAFlAegIIAIIBAhgAqgECCgA=", "saved_system_profile_hash": "DBF51F91DE73E6CDB795E11B4E940249C5CFF4E3", "stats_buildtime": "1751340322", "stats_version": "138.0.3351.65-64", "system_crash_count": 0}}, "variations_google_groups": {"Default": []}, "variations_limited_entropy_synthetic_trial_seed_v2": "53", "was": {"restarted": false}}