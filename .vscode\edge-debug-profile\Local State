{"abusive_adblocker_etag": "\"5E25271B8190D943537AD3FDB50874FC133E8B4A00380E2A6A888D63386F728B\"", "accessibility": {"captions": {"common_models_path": "", "soda_binary_path": ""}}, "apps_count_check_time": "13394140301670577", "autofill": {"ablation_seed": "qafoRwHWeRE="}, "breadcrumbs": {"enabled": false, "enabled_time": "13394140271313865"}, "default_browser": {"browser_name_enum": 13}, "desktop_session_duration_tracker": {"last_session_end_timestamp": "1749668110"}, "edge": {"app_user_model_id": "MSEdge", "manageability": {"edge_last_active_report_time": "13394140272022920", "edge_last_active_time": "13394141707617444"}, "perf_center": {"efficiency_mode_v2_is_active": true, "perf_game_mode": true, "performance_mode": 3, "performance_mode_is_on": false}, "tab_stabs": {"closed_without_unfreeze_never_unfrozen": 0, "closed_without_unfreeze_previously_unfrozen": 0, "discard_without_unfreeze_never_unfrozen": 0, "discard_without_unfreeze_previously_unfrozen": 0}, "tab_stats": {"frozen_daily": 0, "unfrozen_daily": 0}}, "fre": {"has_first_visible_browser_session_completed": true, "oem_bookmarks_set": true}, "gaming_rewards_hva_pitch_notification": {"backfilled": true}, "hardware_acceleration_mode_previous": true, "host_package_checked_on_browser_version": "137.0.7151.69", "identity_combined_status": {"aad": 2, "ad": 1}, "ie_react": {"has_done_one_time_ie_import": true}, "last_pin_migration_on_edge_version": "137.0.3296.68", "last_pin_migration_on_os_version": "11 Version 24H2 (Build 26100.4202)", "legacy": {"profile": {"name": {"migrated": true}}}, "local": {"password_hash_data_list": []}, "metrics": {"client_id_hash_key_created": true}, "migration": {"Default": {"migration_attempt": 0, "migration_version": 5}}, "network_time": {"network_time_mapping": {"local": **********970.17, "network": 1749666672322.0, "ticks": ***********.0, "uncertainty": 10238344.0}}, "optimization_guide": {"model_execution": {"last_usage_by_feature": {}}, "model_store_metadata": {}, "on_device": {"last_version": "137.0.3296.68", "model_crash_count": 0}}, "os_crypt": {"audit_enabled": true, "encrypted_key": "RFBBUEkBAAAA0Iyd3wEV0RGMegDAT8KX6wEAAABS5g694VdfQaFCRBD6+/U+EAAAAB4AAABNAGkAYwByAG8AcwBvAGYAdAAgAEUAZABnAGUAAAAQZgAAAAEAACAAAAAYoLBLMuFrfCop62EQMzOif7fqZPvgHHlBHilbbXSD1wAAAAAOgAAAAAIAACAAAABVQEmpQN31pWxvDET14WmC7CM49bFvb84n8zCQFBg/bjAAAADNMR+owjH4DTsIqeFezpB5N5q5fiMwHKwfEIzP6SyI6/5UTgHPLBJNug6ya6KelPFAAAAAgkvSYr5XsIqA/rz0Nm/NBRP74GYmrXShezM2Jbr2npLdAFNueFflmfgeB60idNXy46z8Wv37bPmvG4sO/1HVqg=="}, "performance_intervention": {"last_daily_sample": "*****************"}, "policy": {"last_statistics_update": "*****************"}, "privacy_budget": {"meta_experiment_activation_salt": 0.****************}, "profile": {"info_cache": {"Default": {"active_time": **********.000309, "avatar_icon": "chrome://theme/IDR_PROFILE_AVATAR_20", "background_apps": false, "edge_account_cid": "691578f9f429c5a9", "edge_account_environment": 0, "edge_account_environment_string": "login.microsoftonline.com", "edge_account_first_name": "mrad", "edge_account_last_name": "eya", "edge_account_oid": "", "edge_account_sovereignty": 0, "edge_account_tenant_id": "9188040d-6c67-4c5b-b112-36a304b66dad", "edge_account_type": 1, "edge_force_signout_restore_info": {}, "edge_force_signout_state": 0, "edge_image_hash": **********, "edge_muid": "1EE3E419EDE0628B100DF211EC9763C0", "edge_previously_signin_user_name": "", "edge_profile_can_be_deleted": true, "edge_signed_in_default_name": ********, "edge_test_on_premises": false, "edge_wam_aad_for_app_account_type": 0, "edge_was_previously_signin": false, "enterprise_label": "", "force_signin_profile_locked": false, "gaia_id": "000300004CA23377", "gaia_name": "mrad eya", "gaia_picture_file_name": "Edge Profile Picture.png", "is_consented_primary_account": true, "is_ephemeral": false, "is_using_default_avatar": false, "is_using_default_name": true, "last_downloaded_gaia_picture_url_with_size": "**********", "managed_user_id": "", "metrics_bucket_index": 1, "name": "Profil 1", "sign_in_source": 1, "signin.with_credential_provider": false, "use_gaia_picture": true, "user_name": "<EMAIL>"}}, "last_active_profiles": ["<PERSON><PERSON><PERSON>"], "metrics": {"next_bucket_index": 2}, "profile_counts_reported": "*****************", "profiles_order": ["<PERSON><PERSON><PERSON>"]}, "profile_network_context_service": {"http_cache_finch_experiment_groups": "None None None None"}, "profiles": {"collect_potential_implicit_signin_data_started": true, "current_profile_name": "", "edge": {"guided_switch_pref": [], "implicit_signin": {"telemetry_result": 1}, "multiple_profiles_with_same_account": false}, "edge_implicitly_signed_in": [{"edge_account_type": 1, "id": "000300004CA23377"}], "edge_sso_info": {"aad_sso_algo_state": 1, "first_profile_key": "<PERSON><PERSON><PERSON>", "msa_first_profile_key": "<PERSON><PERSON><PERSON>", "msa_sso_algo_state": 2, "msa_sso_state_reached_by": 3}, "signin_last_seen_version": "137.0.3296.68", "signin_last_updated_time": **********.426187}, "sentinel_creation_time": "0", "session_id_generator_last_value": "**********", "signin": {"active_accounts": {"0tCkmhn++KlCgjxmGokGBvaCtLEeq6+v1U7FwEaVUzI=": "*****************"}, "active_accounts_last_emitted": "*****************"}, "smartscreen": {"enabled": true, "pua_protection_enabled": true}, "startup_boost": {"last_browser_open_time": "*****************"}, "subresource_filter": {"ruleset_version": {"checksum": 0, "content": "", "format": 0}}, "tab_stats": {"discards_external": 0, "discards_proactive": 0, "discards_urgent": 0, "last_daily_sample": "*****************", "max_tabs_per_window": 1, "reloads_external": 0, "reloads_urgent": 0, "total_tab_count_max": 1, "window_count_max": 1}, "telemetry_client": {"cloned_install": {"user_data_dir_id": ********}, "governance": {"last_dma_change_date": "*****************", "last_known_cps": 2291}, "host_telclient_path": "QzpcUHJvZ3JhbSBGaWxlcyAoeDg2KVxNaWNyb3NvZnRcRWRnZVxBcHBsaWNhdGlvblwxMzcuMC4zMjk2LjY4XHRlbGNsaWVudC5kbGw=", "install_source_name": "windows", "os_integration_level": 5, "sample_id": ********, "updater_version": "**********", "windows_update_applied": false}, "ukm": {"persisted_logs": []}, "uninstall_metrics": {"installation_date2": "**********"}, "user_experience_metrics": {"client_id2": "e7d2012d-9934-4a84-87f5-2ab2128f4be1", "diagnostics": {"edge_odd_consent_last_modified_date": "0", "last_data_collection_level_on_launch": 1}, "last_seen": {"CrashpadMetrics": "0"}, "lbfg_date": "**********", "limited_entropy_randomization_source": "CFD503C67120683FED0242D4DDF4FFB7", "log_record_id": 7, "low_entropy_source3": 2156, "machine_id": 14830129, "payload_counter": 1, "pseudo_low_entropy_source": 3814, "reporting_URL_enabled": false, "reporting_enabled": false, "session_id": 6, "stability": {"browser_last_live_timestamp": "13394141696636774", "exited_cleanly": true, "saved_system_profile": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "saved_system_profile_hash": "A11FB0761855946A4E5F2771195670E3E87B39AA", "stats_buildtime": "1749180038", "stats_version": "137.0.3296.68-64", "system_crash_count": 0}}, "variations_google_groups": {"Default": []}, "variations_limited_entropy_synthetic_trial_seed_v2": "53", "was": {"restarted": false}}