
        <!DOCTYPE html>
        <html lang="en">
          <head>
            <meta charset="UTF-8" />
            <title>Error</title>
            <script type="module">
              const error = {"message":"Failed to resolve import \"@/components/Header\" from \"src/pages/Index.tsx\". Does the file exist?","stack":"    at TransformPluginContext._formatLog (file:///C:/Users/<USER>/node_modules/vite/dist/node/chunks/dep-DBxKXgDP.js:42499:41)\n    at TransformPluginContext.error (file:///C:/Users/<USER>/node_modules/vite/dist/node/chunks/dep-DBxKXgDP.js:42496:16)\n    at normalizeUrl (file:///C:/Users/<USER>/node_modules/vite/dist/node/chunks/dep-DBxKXgDP.js:40475:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async file:///C:/Users/<USER>/node_modules/vite/dist/node/chunks/dep-DBxKXgDP.js:40594:37\n    at async Promise.all (index 2)\n    at async TransformPluginContext.transform (file:///C:/Users/<USER>/node_modules/vite/dist/node/chunks/dep-DBxKXgDP.js:40521:7)\n    at async EnvironmentPluginContainer.transform (file:///C:/Users/<USER>/node_modules/vite/dist/node/chunks/dep-DBxKXgDP.js:42294:18)\n    at async loadAndTransform (file:///C:/Users/<USER>/node_modules/vite/dist/node/chunks/dep-DBxKXgDP.js:35735:27)","id":"C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx","frame":"1  |  import { jsxDEV } from \"react/jsx-dev-runtime\";\n2  |  import { useState } from \"react\";\n3  |  import Header from \"@/components/Header\";\n   |                      ^\n4  |  import AuthModal from \"@/components/AuthModal\";\n5  |  import ClientForm from \"@/components/ClientForm\";","plugin":"vite:import-analysis","pluginCode":"import { jsxDEV } from \"react/jsx-dev-runtime\";\nimport { useState } from \"react\";\nimport Header from \"@/components/Header\";\nimport AuthModal from \"@/components/AuthModal\";\nimport ClientForm from \"@/components/ClientForm\";\nimport ClientList from \"@/components/ClientList\";\nimport Statistics from \"@/components/Statistics\";\nimport { Card, CardHeader, CardTitle, CardContent } from \"@/components/ui/card\";\nimport { Shield, Users, Award } from \"lucide-react\";\nconst Index = () => {\n  const [user, setUser] = useState(null);\n  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);\n  const [clients, setClients] = useState([]);\n  const handleLogin = () => {\n    setIsAuthModalOpen(true);\n  };\n  const handleLogout = () => {\n    setUser(null);\n    setClients([]);\n  };\n  const handleAuthenticate = (userData) => {\n    setUser(userData);\n    console.log(\"Utilisateur connecté:\", userData);\n  };\n  const handleAddClient = (clientData) => {\n    const newClient = {\n      ...clientData,\n      id: Date.now().toString(),\n      dateCreation: (/* @__PURE__ */ new Date()).toISOString()\n    };\n    setClients((prev) => [...prev, newClient]);\n    console.log(\"Nouveau client ajouté:\", newClient);\n  };\n  if (!user) {\n    return /* @__PURE__ */ jsxDEV(\"div\", { className: \"min-h-screen bg-gradient-to-br from-tunisietelecom-lightgray to-white\", children: [\n      /* @__PURE__ */ jsxDEV(\n        Header,\n        {\n          isAuthenticated: false,\n          onLogin: handleLogin,\n          onLogout: handleLogout,\n          onProfile: () => {\n          }\n        },\n        void 0,\n        false,\n        {\n          fileName: \"C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx\",\n          lineNumber: 61,\n          columnNumber: 9\n        },\n        this\n      ),\n      /* @__PURE__ */ jsxDEV(\"main\", { className: \"container mx-auto px-4 py-8\", children: [\n        /* @__PURE__ */ jsxDEV(\"div\", { className: \"text-center mb-16 animate-fade-in\", children: [\n          /* @__PURE__ */ jsxDEV(\"div\", { className: \"w-32 h-24 flex items-center justify-center mx-auto mb-6\", children: /* @__PURE__ */ jsxDEV(\n            \"img\",\n            {\n              src: \"/assets/tunisie-telecom-logo.svg\",\n              alt: \"Tunisie Telecom Logo\",\n              className: \"h-24 w-auto object-contain\"\n            },\n            void 0,\n            false,\n            {\n              fileName: \"C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx\",\n              lineNumber: 72,\n              columnNumber: 15\n            },\n            this\n          ) }, void 0, false, {\n            fileName: \"C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx\",\n            lineNumber: 71,\n            columnNumber: 13\n          }, this),\n          /* @__PURE__ */ jsxDEV(\"h1\", { className: \"text-4xl md:text-6xl font-bold text-tunisietelecom-darkgray mb-6\", children: \"Tunisie Telecom\" }, void 0, false, {\n            fileName: \"C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx\",\n            lineNumber: 78,\n            columnNumber: 13\n          }, this),\n          /* @__PURE__ */ jsxDEV(\"h2\", { className: \"text-2xl md:text-3xl font-light text-gray-600 mb-8\", children: \"Système de Gestion des Scores Clients\" }, void 0, false, {\n            fileName: \"C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx\",\n            lineNumber: 81,\n            columnNumber: 13\n          }, this),\n          /* @__PURE__ */ jsxDEV(\"p\", { className: \"text-lg text-gray-600 max-w-2xl mx-auto mb-8\", children: \"Plateforme moderne pour suivre et gérer les scores de performance de vos clients. Créez votre compte pour commencer à enregistrer et analyser les données de vos clients.\" }, void 0, false, {\n            fileName: \"C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx\",\n            lineNumber: 84,\n            columnNumber: 13\n          }, this),\n          /* @__PURE__ */ jsxDEV(\n            \"button\",\n            {\n              onClick: handleLogin,\n              className: \"bg-tunisietelecom-blue hover:bg-tunisietelecom-darkblue text-white px-8 py-4 rounded-lg text-lg font-semibold shadow-lg transition-all duration-200 hover:shadow-xl transform hover:-translate-y-1\",\n              children: \"Commencer maintenant\"\n            },\n            void 0,\n            false,\n            {\n              fileName: \"C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx\",\n              lineNumber: 88,\n              columnNumber: 13\n            },\n            this\n          )\n        ] }, void 0, true, {\n          fileName: \"C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx\",\n          lineNumber: 70,\n          columnNumber: 11\n        }, this),\n        /* @__PURE__ */ jsxDEV(\"div\", { className: \"grid grid-cols-1 md:grid-cols-3 gap-8 mb-16\", children: [\n          /* @__PURE__ */ jsxDEV(Card, { className: \"border-tunisietelecom-blue/20 shadow-lg hover:shadow-xl transition-shadow duration-200\", children: [\n            /* @__PURE__ */ jsxDEV(CardHeader, { className: \"text-center\", children: [\n              /* @__PURE__ */ jsxDEV(\"div\", { className: \"w-16 h-16 bg-tunisietelecom-blue rounded-full flex items-center justify-center mx-auto mb-4\", children: /* @__PURE__ */ jsxDEV(Users, { className: \"text-white\", size: 32 }, void 0, false, {\n                fileName: \"C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx\",\n                lineNumber: 101,\n                columnNumber: 19\n              }, this) }, void 0, false, {\n                fileName: \"C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx\",\n                lineNumber: 100,\n                columnNumber: 17\n              }, this),\n              /* @__PURE__ */ jsxDEV(CardTitle, { className: \"text-tunisietelecom-darkgray\", children: \"Gestion des Clients\" }, void 0, false, {\n                fileName: \"C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx\",\n                lineNumber: 103,\n                columnNumber: 17\n              }, this)\n            ] }, void 0, true, {\n              fileName: \"C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx\",\n              lineNumber: 99,\n              columnNumber: 15\n            }, this),\n            /* @__PURE__ */ jsxDEV(CardContent, { className: \"text-center\", children: /* @__PURE__ */ jsxDEV(\"p\", { className: \"text-gray-600\", children: \"Ajoutez et gérez facilement les informations de vos clients avec leurs scores de performance.\" }, void 0, false, {\n              fileName: \"C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx\",\n              lineNumber: 106,\n              columnNumber: 17\n            }, this) }, void 0, false, {\n              fileName: \"C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx\",\n              lineNumber: 105,\n              columnNumber: 15\n            }, this)\n          ] }, void 0, true, {\n            fileName: \"C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx\",\n            lineNumber: 98,\n            columnNumber: 13\n          }, this),\n          /* @__PURE__ */ jsxDEV(Card, { className: \"border-tunisietelecom-blue/20 shadow-lg hover:shadow-xl transition-shadow duration-200\", children: [\n            /* @__PURE__ */ jsxDEV(CardHeader, { className: \"text-center\", children: [\n              /* @__PURE__ */ jsxDEV(\"div\", { className: \"w-16 h-16 bg-tunisietelecom-darkblue rounded-full flex items-center justify-center mx-auto mb-4\", children: /* @__PURE__ */ jsxDEV(Award, { className: \"text-white\", size: 32 }, void 0, false, {\n                fileName: \"C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx\",\n                lineNumber: 115,\n                columnNumber: 19\n              }, this) }, void 0, false, {\n                fileName: \"C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx\",\n                lineNumber: 114,\n                columnNumber: 17\n              }, this),\n              /* @__PURE__ */ jsxDEV(CardTitle, { className: \"text-tunisietelecom-darkgray\", children: \"Suivi des Scores\" }, void 0, false, {\n                fileName: \"C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx\",\n                lineNumber: 117,\n                columnNumber: 17\n              }, this)\n            ] }, void 0, true, {\n              fileName: \"C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx\",\n              lineNumber: 113,\n              columnNumber: 15\n            }, this),\n            /* @__PURE__ */ jsxDEV(CardContent, { className: \"text-center\", children: /* @__PURE__ */ jsxDEV(\"p\", { className: \"text-gray-600\", children: \"Surveillez les performances de vos clients avec un système de notation de 0 à 100.\" }, void 0, false, {\n              fileName: \"C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx\",\n              lineNumber: 120,\n              columnNumber: 17\n            }, this) }, void 0, false, {\n              fileName: \"C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx\",\n              lineNumber: 119,\n              columnNumber: 15\n            }, this)\n          ] }, void 0, true, {\n            fileName: \"C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx\",\n            lineNumber: 112,\n            columnNumber: 13\n          }, this),\n          /* @__PURE__ */ jsxDEV(Card, { className: \"border-tunisietelecom-blue/20 shadow-lg hover:shadow-xl transition-shadow duration-200\", children: [\n            /* @__PURE__ */ jsxDEV(CardHeader, { className: \"text-center\", children: [\n              /* @__PURE__ */ jsxDEV(\"div\", { className: \"w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4\", children: /* @__PURE__ */ jsxDEV(Shield, { className: \"text-white\", size: 32 }, void 0, false, {\n                fileName: \"C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx\",\n                lineNumber: 129,\n                columnNumber: 19\n              }, this) }, void 0, false, {\n                fileName: \"C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx\",\n                lineNumber: 128,\n                columnNumber: 17\n              }, this),\n              /* @__PURE__ */ jsxDEV(CardTitle, { className: \"text-tunisietelecom-darkgray\", children: \"Sécurisé\" }, void 0, false, {\n                fileName: \"C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx\",\n                lineNumber: 131,\n                columnNumber: 17\n              }, this)\n            ] }, void 0, true, {\n              fileName: \"C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx\",\n              lineNumber: 127,\n              columnNumber: 15\n            }, this),\n            /* @__PURE__ */ jsxDEV(CardContent, { className: \"text-center\", children: /* @__PURE__ */ jsxDEV(\"p\", { className: \"text-gray-600\", children: \"Plateforme sécurisée avec authentification pour protéger vos données clients.\" }, void 0, false, {\n              fileName: \"C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx\",\n              lineNumber: 134,\n              columnNumber: 17\n            }, this) }, void 0, false, {\n              fileName: \"C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx\",\n              lineNumber: 133,\n              columnNumber: 15\n            }, this)\n          ] }, void 0, true, {\n            fileName: \"C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx\",\n            lineNumber: 126,\n            columnNumber: 13\n          }, this)\n        ] }, void 0, true, {\n          fileName: \"C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx\",\n          lineNumber: 97,\n          columnNumber: 11\n        }, this),\n        /* @__PURE__ */ jsxDEV(\"div\", { className: \"text-center bg-white rounded-xl shadow-lg p-8 border border-tunisietelecom-blue/20\", children: [\n          /* @__PURE__ */ jsxDEV(\"h3\", { className: \"text-2xl font-bold text-tunisietelecom-darkgray mb-4\", children: \"Prêt à commencer ?\" }, void 0, false, {\n            fileName: \"C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx\",\n            lineNumber: 143,\n            columnNumber: 13\n          }, this),\n          /* @__PURE__ */ jsxDEV(\"p\", { className: \"text-gray-600 mb-6\", children: \"Créez votre compte dès maintenant et commencez à gérer vos clients efficacement.\" }, void 0, false, {\n            fileName: \"C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx\",\n            lineNumber: 146,\n            columnNumber: 13\n          }, this),\n          /* @__PURE__ */ jsxDEV(\n            \"button\",\n            {\n              onClick: handleLogin,\n              className: \"bg-tunisietelecom-blue hover:bg-tunisietelecom-darkblue text-white px-6 py-3 rounded-lg font-semibold transition-colors duration-200\",\n              children: \"Créer un compte gratuit\"\n            },\n            void 0,\n            false,\n            {\n              fileName: \"C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx\",\n              lineNumber: 149,\n              columnNumber: 13\n            },\n            this\n          )\n        ] }, void 0, true, {\n          fileName: \"C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx\",\n          lineNumber: 142,\n          columnNumber: 11\n        }, this)\n      ] }, void 0, true, {\n        fileName: \"C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx\",\n        lineNumber: 68,\n        columnNumber: 9\n      }, this),\n      /* @__PURE__ */ jsxDEV(\n        AuthModal,\n        {\n          isOpen: isAuthModalOpen,\n          onClose: () => setIsAuthModalOpen(false),\n          onAuthenticate: handleAuthenticate\n        },\n        void 0,\n        false,\n        {\n          fileName: \"C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx\",\n          lineNumber: 158,\n          columnNumber: 9\n        },\n        this\n      )\n    ] }, void 0, true, {\n      fileName: \"C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx\",\n      lineNumber: 60,\n      columnNumber: 7\n    }, this);\n  }\n  return /* @__PURE__ */ jsxDEV(\"div\", { className: \"min-h-screen bg-gradient-to-br from-tunisietelecom-lightgray to-white\", children: [\n    /* @__PURE__ */ jsxDEV(\n      Header,\n      {\n        isAuthenticated: true,\n        onLogin: handleLogin,\n        onLogout: handleLogout,\n        onProfile: () => console.log(\"Profil clicked\"),\n        userName: user.name\n      },\n      void 0,\n      false,\n      {\n        fileName: \"C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx\",\n        lineNumber: 170,\n        columnNumber: 7\n      },\n      this\n    ),\n    /* @__PURE__ */ jsxDEV(\"main\", { className: \"container mx-auto px-4 py-8\", children: [\n      /* @__PURE__ */ jsxDEV(\"div\", { className: \"mb-8 animate-fade-in\", children: [\n        /* @__PURE__ */ jsxDEV(\"h1\", { className: \"text-3xl font-bold text-tunisietelecom-darkgray mb-2\", children: \"Dashboard - Gestion des Clients\" }, void 0, false, {\n          fileName: \"C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx\",\n          lineNumber: 180,\n          columnNumber: 11\n        }, this),\n        /* @__PURE__ */ jsxDEV(\"p\", { className: \"text-gray-600\", children: [\n          \"Bienvenue \",\n          user.name,\n          \", gérez vos clients et suivez leurs scores de performance.\"\n        ] }, void 0, true, {\n          fileName: \"C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx\",\n          lineNumber: 183,\n          columnNumber: 11\n        }, this)\n      ] }, void 0, true, {\n        fileName: \"C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx\",\n        lineNumber: 179,\n        columnNumber: 9\n      }, this),\n      /* @__PURE__ */ jsxDEV(Statistics, { clients }, void 0, false, {\n        fileName: \"C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx\",\n        lineNumber: 188,\n        columnNumber: 9\n      }, this),\n      /* @__PURE__ */ jsxDEV(\"div\", { className: \"grid grid-cols-1 xl:grid-cols-2 gap-8\", children: [\n        /* @__PURE__ */ jsxDEV(\"div\", { className: \"animate-slide-in\", children: /* @__PURE__ */ jsxDEV(ClientForm, { onAddClient: handleAddClient }, void 0, false, {\n          fileName: \"C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx\",\n          lineNumber: 192,\n          columnNumber: 13\n        }, this) }, void 0, false, {\n          fileName: \"C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx\",\n          lineNumber: 191,\n          columnNumber: 11\n        }, this),\n        /* @__PURE__ */ jsxDEV(\"div\", { className: \"animate-fade-in\", children: /* @__PURE__ */ jsxDEV(ClientList, { clients }, void 0, false, {\n          fileName: \"C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx\",\n          lineNumber: 196,\n          columnNumber: 13\n        }, this) }, void 0, false, {\n          fileName: \"C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx\",\n          lineNumber: 195,\n          columnNumber: 11\n        }, this)\n      ] }, void 0, true, {\n        fileName: \"C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx\",\n        lineNumber: 190,\n        columnNumber: 9\n      }, this)\n    ] }, void 0, true, {\n      fileName: \"C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx\",\n      lineNumber: 178,\n      columnNumber: 7\n    }, this)\n  ] }, void 0, true, {\n    fileName: \"C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx\",\n    lineNumber: 169,\n    columnNumber: 5\n  }, this);\n};\nexport default Index;\n","loc":{"file":"C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx","line":3,"column":19}}
              try {
                const { ErrorOverlay } = await import("/@vite/client")
                document.body.appendChild(new ErrorOverlay(error))
              } catch {
                const h = (tag, text) => {
                  const el = document.createElement(tag)
                  el.textContent = text
                  return el
                }
                document.body.appendChild(h('h1', 'Internal Server Error'))
                document.body.appendChild(h('h2', error.message))
                document.body.appendChild(h('pre', error.stack))
                document.body.appendChild(h('p', '(Error overlay failed to load)'))
              }
            </script>
          </head>
          <body>
          </body>
        </html>
      